import { Search, Plus, ArrowLeft } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface NavHeaderProps {
  communityId: string;
  isMember: boolean;
  handleJoinCommunity:()=> void;
}

export default function NavHeader({ communityId, isMember,handleJoinCommunity }: NavHeaderProps) {
  return (
    <div className="w-full bg-white border-b border-gray-200 py-3 px-4">
      <div className="max-w-6xl mx-auto flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/comunity" className="md:hidden mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-700" />
          </Link>
          <div className="hidden md:block flex-1 max-w-md relative">
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            <input 
              type="text" 
              placeholder="Search" 
              className="w-full pl-10 pr-4 py-2 rounded-full border border-gray-300"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="md:hidden">
            <Search className="h-6 w-6 text-gray-700" />
          </div>
          <div className="md:hidden ml-2">
            {isMember && (
              <div className="md:hidden">
                <Link href={`/comunity/${communityId}/create-post`}>
                  <Image src="/Avatar.svg" width={32} height={32} alt="Profile" className="rounded-full" />
                </Link>
              </div>
            )}
          </div>
          {isMember ? (
              <div className="hidden md:flex px-5 py-2 border  rounded-full text-blue-600 border-blue-600">
                Joined
              </div>
            ) : (
              <button 
                onClick={handleJoinCommunity}
                className="bg-blue-600 text-white px-6 py-2 rounded-full font-medium"
              >
                Join
              </button>
            )}
          {isMember && (
            <Link href={`/comunity/${communityId}/create-post`} className="hidden md:flex">
              <button className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-full">
                <Plus className="mr-1 h-5 w-5" />
                Create Post
              </button>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
