import { defineAuth, secret } from "@aws-amplify/backend";
import { postConfirmation } from "../functions/post-confirmation/resource";

export const auth = defineAuth({
  loginWith: {
    email: true,
    externalProviders: {
      google: {
        clientId: secret("GOOGLE_CLIENT_ID"),
        clientSecret: secret("GOOGLE_CLIENT_SECRET"),
        scopes: ["email", "profile"],
      },
      facebook: {
        clientId: secret("FACEBOOK_CLIENT_ID"),
        clientSecret: secret("FACEBOOK_CLIENT_SECRET"),
        scopes: ["email", "public_profile"],
      },

      callbackUrls: [
        "http://localhost:3000/chats",
        "https://dev.d19d7k7q3bai5x.amplifyapp.com/chats",

        "https://main.d19d7k7q3bai5x.amplifyapp.com/chats",
      ],

      logoutUrls: [
        "http://localhost:3000/login",
        "https://main.d19d7k7q3bai5x.amplifyapp.com/login",
        "https://dev.d19d7k7q3bai5x.amplifyapp.com/login",
      ],
    },
  },
  triggers: {
      postConfirmation,
  }
},
 
);
