import { ConfirmationFormsProps } from "@/types/auth";
import { useState } from "react";

export default function ConfirmationForm({ confirmationCode, setConfirmationCode, handleConfirmSignUp, handleResendCode, loading, errorMessage }: ConfirmationFormsProps) {
  const [showError, setShowError] = useState(false);

  const handleConfirmClick = () => {
    setShowError(true);
    handleConfirmSignUp();
  };

  return (
    <div className="space-y-4">
      <p className="text-white mb-4">Enter the code sent to your email:</p>
      <input
        type="text"
        value={confirmationCode}
        onChange={(e) => {
          const onlyNumbers = e.target.value.replace(/\D/g, '');
          setConfirmationCode(onlyNumbers);
        }}
        placeholder="Confirmation code"
        className="w-full py-3 px-6 border border-gray-600 bg-gray-800 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
      />
      {showError && errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
      <div className="flex flex-col gap-2">
        <button
          onClick={handleConfirmClick}
          disabled={loading}
          className="w-full py-3 px-6 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          {loading ? "Confirming..." : "Confirm Code"}
        </button>
        <button
          onClick={handleResendCode}
          disabled={loading}
          className="w-full py-3 px-6 bg-transparent border border-gray-600 text-gray-600 rounded-lg hover:bg-gray-100"
        >
          {loading ? "Sending..." : "Resend Code"}
        </button>
      </div>
    </div>
  );
}
