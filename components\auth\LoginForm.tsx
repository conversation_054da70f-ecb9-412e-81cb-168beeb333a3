import { useForm } from "react-hook-form"
import { Input } from "../ui/input"
import { But<PERSON> } from "../ui/button"
import { Eye, EyeOff } from "lucide-react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import type { FormData } from "@/types/auth"

type Props = {
  loading: boolean
  errorMessage?: string
  onSubmit: (data: FormData) => void
}

export function LoginForm({ loading, errorMessage, onSubmit }: Props) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>()
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm text-gray-600 mb-2">
          Email
        </label>
        <Input
          id="email"
          type="email"
          placeholder="Email"
          className="w-full p-3 rounded-full bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
          {...register("email")}
        />
        {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
      </div>

      <div>
        <label htmlFor="password" className="block text-sm text-gray-600 mb-2">
          Password
        </label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            className="w-full p-3 rounded-full bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
            {...register("password")}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
        </div>
        {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
      </div>

      {errorMessage && <p className="text-red-500 text-sm mb-4">{errorMessage}</p>}

      <div className="text-right">
        <button
          type="button"
          onClick={() => router.push("/forgot-password")}
          className="text-blue-500 text-sm hover:underline"
          disabled={loading}
        >
          Forgot Password?
        </button>
      </div>

      <Button
        type="submit"
        className="w-full py-6 bg-[#5185FF] text-white font-medium rounded-full"
        disabled={loading}
      >
        {loading ? "Logging in..." : "Login"}
      </Button>
    </form>
  )
}
