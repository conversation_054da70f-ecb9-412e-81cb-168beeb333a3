"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { useUser } from "@/hooks/useUsers";
import { OneUpHealthService } from "@/lib/services/oneUpHealthService";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { HealthSystemManager } from "@/components/Health/HealthSystemMobile";

const client = generateClient<Schema>();

interface ConnectedSystem {
  id: string;
  name: string;
}

interface HealthSystem {
  id: string;
  name: string;
  isConnected: boolean;
}

export default function Setting() {
  const router = useRouter();
  const { currentUser } = useUser();

  const [isLoading, setIsLoading] = useState(true);

  const [showAddModal, setShowAddModal] = useState(false);

  useEffect(() => {
    if (currentUser) {
      fetchConnectedSystems();
    } else {
      setIsLoading(false);
    }
  }, [currentUser]);

  const fetchConnectedSystems = async () => {
    if (!currentUser) return;

    try {
      const service = new OneUpHealthService({ userId: currentUser.userId });
      await service.initialize();

      const systems = await service.getConnectedHealthSystems();
    } catch (error) {
      console.error("Failed to fetch connected systems:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddMore = () => {
    setShowAddModal(true);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-full bg-[#E9F0FF]">
        <div className="flex items-center pt-[51px] pl-6 py-4 overflow-hidden">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <h1 className="text-[16px] font-normal ml-4">Setting</h1>
        </div>
        <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
          <div className="flex justify-center items-center h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      <div className="flex items-center pt-[51px] pl-6 py-4 overflow-hidden">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <h1 className="text-[16px] font-normal ml-4">Setting</h1>
      </div>

      <div
        className={`h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto ${showAddModal ? "blur-sm" : ""}`}
      >
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {/* Using the new adaptive component */}
          <HealthSystemManager onClose={handleAddMore} />
        </motion.div>
      </div>

      {/* Add Health System Modal */}
      {showAddModal && (
        <HealthSystemManager
          isModal={false} // false because we're using the mobile view
          onClose={() => setShowAddModal(false)}
        />
      )}
    </div>
  );
}
