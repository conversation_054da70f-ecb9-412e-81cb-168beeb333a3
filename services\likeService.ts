import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { PostLikeResponse, PostLikeWithUser } from "@/types/like";
import { UpdatePostLikesResponse } from "@/types/moment";

// Cache implementation
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes
const likeCache = new Map<string, CacheItem<any>>();

const getCachedData = <T>(key: string): T | null => {
  const item = likeCache.get(key);
  if (!item) return null;
  
  const now = Date.now();
  if (now - item.timestamp > CACHE_EXPIRATION) {
    likeCache.delete(key);
    return null;
  }
  
  return item.data as T;
};

const setCachedData = <T>(key: string, data: T): void => {
  likeCache.set(key, {
    data,
    timestamp: Date.now()
  });
};

const invalidateCache = (postId?: string): void => {
  if (postId) {
    // Clear specific post data
    likeCache.delete(`likes_${postId}`);
    likeCache.forEach((_, key) => {
      if (key.includes(postId)) likeCache.delete(key);
    });
  } else {
    // Clear all cache
    likeCache.clear();
  }
};

export const likeService = {
  checkExistingLike: async (postId: string, userId: string) => {
    const cacheKey = `like_check_${postId}_${userId}`;
    const cachedData = getCachedData<PostLikeResponse>(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    const client = generateClient<Schema>();
    // Use model API for checking like
    const response = await client.models.PostLike.list({
      filter: {
        postId: { eq: postId },
        userId: { eq: userId }
      }
    });
    setCachedData(cacheKey, response);
    return response;
  },

  createLike: async (userId: string, postId: string) => {
    invalidateCache(postId);
    const client = generateClient<Schema>();

    // Check if like already exists for this user and post
    const existing = await client.models.PostLike.list({
      filter: {
        postId: { eq: postId },
        userId: { eq: userId }
      }
    });
    if (existing.data.length > 0) {
      const post = await client.models.Post.get({ id: postId });
      // Remove include
      const likesList = await client.models.PostLike.list({
        filter: { postId: { eq: postId } }
      });
      return {
        like: existing.data[0],
        updatedPost: post,
        likesList: likesList.data,
        alreadyLiked: true
      };
    }

    // Create new like
    const result = await client.models.PostLike.create({
      id: crypto.randomUUID(),
      userId,
      postId,
      createdAt: new Date().toISOString()
    });

    const post = await client.models.Post.get({ id: postId });
    let updatedPost;
    if (post.data) {
      updatedPost = await client.models.Post.update({
        id: postId,
        likes: (post.data.likes || 0) + 1
      });
    }

    // Remove include
    const likesList = await client.models.PostLike.list({
      filter: { postId: { eq: postId } }
    });

    return {
      like: result,
      updatedPost,
      likesList: likesList.data,
      alreadyLiked: false
    };
  },

  deleteLike: async (likeId: string, postId: string, userId: string) => {
    invalidateCache(postId);
    const client = generateClient<Schema>();

    const like = await client.models.PostLike.get({ id: likeId });
    if (!like.data || like.data.userId !== userId) {
      throw new Error("Unauthorized or like not found");
    }

    const result = await client.models.PostLike.delete({ id: likeId });
    const post = await client.models.Post.get({ id: postId });
    let updatedPost;
    // Перевірка на null і число
    if (post.data && typeof post.data.likes === "number" && post.data.likes > 0) {
      updatedPost = await client.models.Post.update({
        id: postId,
        likes: post.data.likes - 1
      });
    }

    // Remove include
    const likesList = await client.models.PostLike.list({
      filter: { postId: { eq: postId } }
    });

    return {
      deleted: result,
      updatedPost,
      likesList: likesList.data
    };
  },

  updatePostLikes: async (postId: string, likesCount: number) => {
    invalidateCache(postId);
    const client = generateClient<Schema>();
    // Use model API for updating post likes
    return await client.models.Post.update({
      id: postId,
      likes: likesCount
    });
  },

  fetchLikes: async (postId: string) => {
    const cacheKey = `likes_${postId}`;
    const cachedData = getCachedData<{
      listLikesByPost: {
        items: PostLikeWithUser[];
      }
    }>(cacheKey);
    
    if (cachedData) {
      return cachedData;
    }
    
    const client = generateClient<Schema>();
    // Remove include
    const response = await client.models.PostLike.list({
      filter: { postId: { eq: postId } }
    });
    const formatted = {
      listLikesByPost: {
        items: response.data.map(like => ({
          id: like.id,
          userId: like.userId,
          postId: like.postId,
          createdAt: like.createdAt,
          // user info can be fetched separately if needed
        }))
      }
    };
    setCachedData(cacheKey, formatted);
    return formatted;
  },
  
  // Export cache control functions for external use
  clearCache: () => invalidateCache(),
  clearPostCache: (postId: string) => invalidateCache(postId)
};
