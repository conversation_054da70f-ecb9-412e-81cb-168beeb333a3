import { useState, useEffect, useCallback, useRef } from "react";
import { generateClient } from "aws-amplify/api";

import { Schema } from "@/amplify/data/resource";
import { ERROR_FAILED_LOAD_MESSAGES } from "@/constants/chat";
import { MessageWithoutChat } from "@/types/chat";

const client = generateClient<Schema>();
const MESSAGE_BATCH_SIZE = 10;

export function useMessageLoader(chatId: string | null | undefined) {
  const [messages, setMessages] = useState<Array<MessageWithoutChat>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nextToken, setNextToken] = useState<string | null | undefined>(
    undefined
  );
  const [hasMore, setHasMore] = useState(true);
  const [observerTarget, setObserverTarget] = useState<HTMLElement | null>(
    null
  );

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const currentChatIdRef = useRef(chatId);
  currentChatIdRef.current = chatId;

  const fetchMessages = useCallback(
    async (id: string, pageToken: string | null | undefined) => {
      if (isLoading || isLoadingMore || currentChatIdRef.current !== id) {
        console.log("Skipping fetch: Already loading or chat changed");
        return;
      }

      const isInitialLoad = !pageToken;
      if (isInitialLoad) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }
      setError(null);

      console.log(`Loading messages for chat ${id}, token: ${pageToken}`);

      try {
        const result = await client.models.ChatMessage.listMessagesByDate(
          { chatId: id },
          {
            sortDirection: "DESC",
            limit: MESSAGE_BATCH_SIZE,
            nextToken: pageToken ?? undefined,
          }
        );

        if (currentChatIdRef.current !== id) {
          console.log("Fetch completed but chat changed, discarding results.");

          setIsLoading(false);
          setIsLoadingMore(false);
          return;
        }

        if (result.data && result.data.length > 0) {
          console.log(`Received ${result.data.length} messages.`);

          // Map the API response to match MessageWithoutChat type, using any for the user property
          const mappedMessages: MessageWithoutChat[] = result.data.map(
            (msg) => ({
              id: msg.id,
              chatId: msg.chatId,
              userId: msg.userId,
              message: msg.message,
              messageType: msg.messageType,
              showMap: msg.showMap,
              attachments: msg.attachments,
              createdAt: msg.createdAt,
              updatedAt: msg.updatedAt,
              // Using any type to bypass type checking
              user: msg.user as any,
            })
          );

          const chronologicallyOrderedNewBatch = [...mappedMessages].reverse();

          if (isInitialLoad) {
            setMessages(chronologicallyOrderedNewBatch);
          } else {
            setMessages((prev) => [...chronologicallyOrderedNewBatch, ...prev]);
          }

          setNextToken(result.nextToken);
          setHasMore(!!result.nextToken);
          console.log("Fetch successful. Has more:", !!result.nextToken);
        } else {
          if (isInitialLoad) {
            setMessages([]);
          }
          setHasMore(false);
          setNextToken(null);
          console.log("No more messages received.");
        }
      } catch (err) {
        console.error("Failed to fetch messages:", err);

        if (currentChatIdRef.current === id) {
          setError(ERROR_FAILED_LOAD_MESSAGES);
          setHasMore(false);
        }
      } finally {
        if (currentChatIdRef.current === id) {
          setIsLoading(false);
          setIsLoadingMore(false);
        }
      }
    },
    [isLoading, isLoadingMore]
  );

  /** Loads the next batch of older messages. */
  const loadMoreMessages = useCallback(() => {
    console.log("Attempting to load more messages...", {
      chatId: currentChatIdRef.current,
      nextToken,
      hasMore,
      isLoadingMore,
    });
    if (
      currentChatIdRef.current &&
      nextToken &&
      hasMore &&
      !isLoadingMore &&
      !isLoading
    ) {
      console.log(
        `⬆️ Loading OLDER messages for chat ${currentChatIdRef.current} with token: ${nextToken}`
      );
      fetchMessages(currentChatIdRef.current, nextToken);
    } else {
      console.log("❌ Conditions not met for loading more messages.");
    }
  }, [nextToken, hasMore, isLoadingMore, isLoading, fetchMessages]);

  /** Adds a message to the end of the list (e.g., for optimistic UI updates). */
  const addMessage = useCallback((message: MessageWithoutChat) => {
    setMessages((prev) =>
      prev.some((m) => m.id === message.id) ? prev : [...prev, message]
    );
  }, []);

  /** Removes a message from the list by ID. */
  const removeMessage = useCallback((messageId: string) => {
    setMessages((prevMessages) =>
      prevMessages.filter((message) => message.id !== messageId)
    );
  }, []);

  useEffect(() => {
    setMessages([]);
    setNextToken(undefined);
    setHasMore(true);
    setError(null);
    setIsLoading(false);
    setIsLoadingMore(false);

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    if (chatId) {
      console.log(`Chat ID changed to: ${chatId}. Initiating initial fetch.`);

      fetchMessages(chatId, null);
    } else {
      console.log("Chat ID is null/undefined. Clearing messages.");
    }
  }, [chatId]);

  useEffect(() => {
    if (!observerTarget || !hasMore || !chatId) {
      return;
    }

    console.log("Setting up Intersection Observer...");

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && hasMore && !isLoadingMore && !isLoading) {
          if (debounceTimerRef.current) {
            clearTimeout(debounceTimerRef.current);
          }

          debounceTimerRef.current = setTimeout(() => {
            console.log("Observer triggered, loading more messages.");
            loadMoreMessages();
            debounceTimerRef.current = null;
          }, 300);
        } else {
        }
      },
      {
        threshold: 0.1,
        rootMargin: "100px 0px 0px 0px",
      }
    );

    observer.observe(observerTarget);
    console.log("Observer attached to element:", observerTarget);

    return () => {
      console.log("Disconnecting observer and clearing timer.");
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
      observer.disconnect();
    };
  }, [
    observerTarget,
    hasMore,
    isLoadingMore,
    isLoading,
    loadMoreMessages,
    chatId,
  ]);

  return {
    messages,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    addMessage,
    removeMessage,
    setObserverTarget,
  };
}
