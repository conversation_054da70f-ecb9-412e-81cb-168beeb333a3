"use client"

import { useState } from "react"
import Image from "next/image"
import CustomButton from "../ui/CustomButton"

interface MomentCommentFormProps {
  currentUser: { email: string } | null;
  onSubmit: (content: string) => Promise<void>;
  isLoading: boolean;
  isMobile?: boolean;
}

export default function MomentCommentForm({ 
  currentUser, 
  onSubmit, 
  isLoading,
  isMobile = false 
}: MomentCommentFormProps) {
  const [comment, setComment] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!comment.trim() || !currentUser) return
    
    await onSubmit(comment)
    setComment("")
  }

  if (isMobile) {
    return (
      <div className="fixed inset-x-0 bottom-0 z-50 bg-white border-t border-gray-100 shadow-lg">
        <div className="p-4">
          <form onSubmit={handleSubmit}>
            <input
              type="text"
              placeholder={currentUser ? "Write a comment..." : "Please login to comment"}
              className="w-full p-3 mb-8 rounded-lg text-gray-700 outline-none"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              disabled={!currentUser}
            />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <button type="button" className="text-gray-500">
                  <div className="w-6 h-6 flex items-center justify-center rounded-md">
                    <Image src={'/Utility.svg'} alt="Utility" width={24} height={24}/>
                  </div>
                </button>
                <button type="button" className="text-gray-500">
                  <div className="w-6 h-6 flex items-center justify-center rounded-md">
                    <Image src={'/video.svg'} alt="Video" width={24} height={24}/>
                  </div>
                </button>
              </div>
              <button 
                type="submit" 
                className="bg-blue-500 text-white h-[36px] w-[72px] rounded-full text-base font-medium"
                disabled={!currentUser || isLoading}
              >
                Post
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }

  return (
    <div className="mb-4">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <input
          type="text"
          placeholder={currentUser ? "Write a comment..." : "Please login to comment"}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          className="flex flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
          disabled={!currentUser}
        />
        <CustomButton 
          type="submit"
          disabled={!currentUser || !comment.trim()}
          loading={isLoading}
          className="!w-auto px-4 !h-[40px]"
        >
          Post
        </CustomButton>
      </form>
    </div>
  )
}
