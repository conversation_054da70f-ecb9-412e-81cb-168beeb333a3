"use client";

import { Dialog, DialogContent } from "@/components/ui/dialog";
import NewChatList from "@/components/NewChat/NewChatList";
import { useEffect, useContext, useCallback, useRef } from "react";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import { useViewState } from "@/components/Chat/context/ViewStateContext";

interface NewChatFullDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NewChatFullDialog({
  isOpen,
  onClose,
}: NewChatFullDialogProps) {
  const { setShowNewChatPage } = useContext(ChatContext);
  const { setActiveView } = useViewState();

  // Create stable references to the functions we need
  const onCloseRef = useRef(onClose);
  const setShowNewChatPageRef = useRef(setShowNewChatPage);
  const setActiveViewRef = useRef(setActiveView);

  // Update refs when props change without triggering renders
  useEffect(() => {
    onCloseRef.current = onClose;
    setShowNewChatPageRef.current = setShowNewChatPage;
    setActiveViewRef.current = setActiveView;
  }, [onClose, setShowNewChatPage, setActiveView]);

  // Create a stable reference to the handleClose function for NewChatList
  const handleListClose = useCallback(() => {
    document.body.style.overflow = "";
    document.body.style.pointerEvents = "";
    setShowNewChatPageRef.current(false);
    setActiveViewRef.current("CHAT_LIST");
    onCloseRef.current();
  }, []); // No dependencies means this callback is created once

  // Create a stable reference to the dialog's onOpenChange handler
  const handleOpenChange = useCallback(
    (open: boolean) => {
      if (!open) {
        handleListClose();
      }
    },
    [handleListClose]
  ); // Only depend on our stable handleListClose

  useEffect(() => {
    return () => {
      document.body.style.overflow = "";
      document.body.style.pointerEvents = "";
    };
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent
        showCloseButton={false}
        className="p-0 max-w-full sm:max-w-4xl h-[600px] flex flex-col overflow-hidden"
      >
        <NewChatList inDialog={true} onClose={handleListClose} />
      </DialogContent>
    </Dialog>
  );
}
