import { events } from "aws-amplify/data";
import { AI_ASSISTANT_ID } from "../../../constants/chat";

interface EventPayload {
  [key: string]: any;
  sessionId: string;
  chatId: string;
}

export class EventManager {
  private channelName: string;
  private sessionId: string;
  private chatId: string;
  private namespace: string;
  private isCompleteSent: boolean = false;
  private accumulatedTokens: string = ""; // Add this line

  constructor(
    channelName: string,
    sessionId: string,
    chatId: string,
    namespace = "default"
  ) {
    this.channelName = channelName;
    this.sessionId = sessionId;
    this.chatId = chatId;
    this.namespace = namespace;
    this.accumulatedTokens = ""; // Initialize here as well
    console.log(
      `[EventManager] Initialized with channel: ${this.getFullChannelName()}`
    );
  }

  private getFullChannelName(): string {
    return `/${this.namespace}/${this.channelName}`;
  }

  private async publish(type: string, payload: Partial<EventPayload>) {
    const fullChannelName = this.getFullChannelName();
    const eventData = {
      type,
      payload: { ...payload, sessionId: this.sessionId, chatId: this.chatId },
      sessionId: this.sessionId,
    };

    try {
      const result = await events.post(fullChannelName, eventData);

      return result;
    } catch (error) {
      console.error(`[EventManager] Failed to publish ${type} event:`, error);
      throw new Error(`Failed to publish ${type} event`);
    }
  }

  async sendToken(chunk: string) {
    if (this.isCompleteSent) {
      console.warn(
        `[EventManager] Attempted to send token after complete event`
      );
      return;
    }

    if (!chunk || chunk.trim() === "") {
      console.log(`[EventManager] Skipping empty token`);
      return;
    }
    console.log(`[EventManager] Sending token chunk: ${chunk}`);
    this.accumulatedTokens += chunk; // Accumulate the token
    await this.publish("token", { chunk });
  }

  async sendComplete(messageId: string, fullText: string) {
    // Log accumulated tokens for debugging before sending complete
    console.log(
      `[EventManager] Accumulated tokens before complete: ${this.accumulatedTokens}`
    );
    console.log(`[EventManager] Full text from AI service: ${fullText}`);
    // Optionally compare accumulatedTokens with fullText here if needed
    // if (this.accumulatedTokens !== fullText) {
    //   console.warn("[EventManager] Accumulated tokens differ from AI full text!");
    // }

    await this.publish("complete", {
      fullText, // Send the original fullText from the AI service
      messageId,
      userId: AI_ASSISTANT_ID,
    });
    this.isCompleteSent = true;
    // Reset accumulated tokens for the next message if the instance is reused (though typically it might not be)
    this.accumulatedTokens = "";
  }

  async sendError(message: string) {
    await this.publish("error", { message });
  }
}
