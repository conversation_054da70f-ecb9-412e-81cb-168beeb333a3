import { AiService } from "./AiService";
import { EventManager } from "./EventManager";

import { generateClient } from "@aws-amplify/api";
import { Context } from "../interfaces/QuestionnaireContext";
import { QuestionnaireStrategy } from "../interfaces/QuestionnaireStrategy";
import { QuestionnaireStrategyFactory } from "../strategies/QuestionnaireStrategyFactory";
import { HealthQuestionnaireStrategy } from "../strategies/HealthQuestionnaireStrategy";
import { MedicalProfileStrategy } from "../strategies/MedicalProfileStrategy";

import type { Schema } from "../../data/resource";

const client = generateClient<Schema>();

const DEFAULT_QUESTIONNAIRE_ID = "1";

export interface QuestionnaireResult {
  status: "completed" | "in-progress";
  messageId?: string;
  userResponse?: string;
  chatMessage?: any;
}

export class QuestionnaireService {
  private chatId: string;
  private aiService: AiService;
  private strategy: QuestionnaireStrategy;
  private context: Context;
  private initialized = false;

  constructor(chatId: string, aiService: AiService) {
    this.chatId = chatId;
    this.aiService = aiService;
    this.context = {
      metadata: {
        chatId: chatId,
        questionnaireId: DEFAULT_QUESTIONNAIRE_ID,
        currentQuestionIndex: 0,
        isQuestionnaireComplete: false,
      },
      chatHistory: [],
      questions: [],
    };

    this.strategy = new HealthQuestionnaireStrategy(aiService);
  }

  /**
   * Initializes the questionnaire state and loads questions
   */
  async initState(): Promise<void> {
    console.log("[QuestionnaireService] Initializing questionnaire state");
    await this.syncStateWithDatabase();

    const questionnaireId = this.context.metadata.questionnaireId || "1";

    if (questionnaireId === "2") {
      console.log("[QuestionnaireService] Using Medical Profile Strategy");
      this.strategy = new MedicalProfileStrategy(this.aiService);
    } else {
      console.log("[QuestionnaireService] Using Health Questionnaire Strategy");
      this.strategy = new HealthQuestionnaireStrategy(this.aiService);
    }

    if (!this.context.metadata.isQuestionnaireComplete) {
      await this.strategy.initialize(this.context);
      await this.updateMetadata(this.context.metadata);
    }

    console.log(
      "[QuestionnaireService] Current questionnaire state:",
      JSON.stringify({
        isComplete: this.context.metadata.isQuestionnaireComplete,
        currentIndex: this.context.metadata.currentQuestionIndex,
        questionCount: this.context.questions.length,
      })
    );
  }

  private async syncStateWithDatabase(): Promise<void> {
    try {
      console.log(
        `[QuestionnaireService] Syncing state with database for chat ${this.chatId}`
      );

      const { data: chat } = await client.models.Chat.get({
        id: this.chatId,
      });

      if (!chat) {
        console.error("[QuestionnaireService] Chat not found in database");
        return;
      }

      if (chat.metadata) {
        this.context.metadata = {
          chatId: this.chatId,
          questionnaireId: chat.questionnaireId || DEFAULT_QUESTIONNAIRE_ID,

          createdBy: chat.metadata.createdBy || "system", //!

          isArchived: chat.metadata.isArchived,
          category: chat.metadata.category,
          configuration: chat.metadata.configuration,
          currentQuestionIndex: chat.metadata.currentQuestionIndex || 0,
          isQuestionnaireComplete:
            chat.metadata.isQuestionnaireComplete || false,
          questionnaireCompletedAt: chat.metadata.questionnaireCompletedAt,
          diagnosisMessageSent: chat.metadata.diagnosisMessageSent,

          earlyCompletion: chat.metadata.earlyCompletion || false,
        };
      } else {
        this.context.metadata = {
          chatId: this.chatId,
          questionnaireId: chat.questionnaireId || DEFAULT_QUESTIONNAIRE_ID,
          currentQuestionIndex: 0,
          isQuestionnaireComplete: false,

          createdBy: "system",
        };
      }

      console.log(
        `[QuestionnaireService] Using questionnaire ID: ${this.context.metadata.questionnaireId}`
      );

      const { data: messagesData } =
        await client.models.ChatMessage.listMessagesByDate(
          { chatId: this.chatId },
          { sortDirection: "ASC" }
        );
      this.context.chatHistory = messagesData || [];

      console.log(
        `[QuestionnaireService] Loaded ${this.context.chatHistory.length} chat messages`
      );

      const isComplete =
        this.context.metadata.isQuestionnaireComplete === true ||
        !!this.context.metadata.questionnaireCompletedAt;

      if (isComplete) {
        this.context.metadata.isQuestionnaireComplete = true;
        console.log(
          "[QuestionnaireService] Database indicates questionnaire is complete"
        );
      }
    } catch (error) {
      console.error(
        "[QuestionnaireService] Error syncing with database:",
        error
      );
    }
  }

  isQuestionnaireComplete(): boolean {
    return this.context.metadata.isQuestionnaireComplete === true;
  }

  async markQuestionnaireComplete(
    earlyCompletion: boolean = false
  ): Promise<void> {
    console.log(
      `[QuestionnaireService] Marking questionnaire as complete${earlyCompletion ? " (early completion)" : ""}`
    );

    const updates = {
      isQuestionnaireComplete: true,
      currentQuestionIndex: this.context.questions.length,
      questionnaireCompletedAt: new Date().toISOString(),
      earlyCompletion: earlyCompletion,
    };

    if (earlyCompletion) {
      updates["earlyCompletion"] = true;
    }

    await this.updateMetadata(updates);
    this.context.metadata.isQuestionnaireComplete = true;

    this.context.questions = [];
  }

  async forceMarkAsComplete(): Promise<void> {
    await this.markQuestionnaireComplete(true);
  }

  async handleQuestionnaireResponse(
    userResponse: string,
    eventManager: EventManager
  ): Promise<QuestionnaireResult> {
    try {
      console.log("[QuestionnaireService] Processing questionnaire response");

      await this.syncStateWithDatabase();

      console.log(
        `[QuestionnaireService] Current question index before processing: ${this.context.metadata.currentQuestionIndex}`
      );

      if (this.isQuestionnaireComplete()) {
        console.log(
          "[QuestionnaireService] Questionnaire already completed, returning completed status"
        );
        return { status: "completed", userResponse: userResponse };
      }

      const previousQuestionIndex = this.context.metadata.currentQuestionIndex;

      const result = await this.strategy.handleResponse(
        userResponse,
        this.context,
        eventManager
      );

      console.log(
        `[QuestionnaireService] Question index after strategy execution: ${this.context.metadata.currentQuestionIndex}`
      );

      if (
        this.context.metadata.currentQuestionIndex === previousQuestionIndex &&
        !result.isComplete &&
        this.context.metadata.currentQuestionIndex <
          this.context.questions.length
      ) {
        this.context.metadata.currentQuestionIndex++;
        console.log(
          `[QuestionnaireService] Forced increment of question index to: ${this.context.metadata.currentQuestionIndex}`
        );
      }

      const metadataUpdates = {
        ...this.context.metadata,
        currentQuestionIndex: this.context.metadata.currentQuestionIndex,
      };

      await this.updateMetadata(metadataUpdates);

      if (result.isComplete) {
        console.log(
          "[QuestionnaireService] Strategy indicates questionnaire is complete"
        );
        await this.markQuestionnaireComplete(
          this.context.metadata.earlyCompletion
        );
        return {
          status: "completed",
          userResponse: userResponse,
          messageId: result.messageId,
          chatMessage: result.chatMessage,
        };
      }

      return {
        status: "in-progress",
        messageId: result.messageId,
        chatMessage: result.chatMessage,
        userResponse,
      };
    } catch (error) {
      console.error(
        "[QuestionnaireService] Error handling questionnaire response:",
        error
      );
      throw error;
    }
  }

  private async updateMetadata(updates: any): Promise<void> {
    try {
      const { data: chat } = await client.models.Chat.get({
        id: this.chatId,
      });

      if (!chat) {
        console.error(
          "[QuestionnaireService] Chat not found when updating metadata"
        );
        throw new Error("Chat not found when updating metadata");
      }

      console.log(
        "[QuestionnaireService] Updating metadata:",
        JSON.stringify(updates)
      );

      const updatedMetadata = {
        createdBy: updates.createdBy ?? chat.metadata?.createdBy,
        isArchived: updates.isArchived ?? chat.metadata?.isArchived,
        category: updates.category ?? chat.metadata?.category,
        configuration: updates.configuration ?? chat.metadata?.configuration,
        currentQuestionIndex:
          updates.currentQuestionIndex ?? chat.metadata?.currentQuestionIndex,
        isQuestionnaireComplete:
          updates.isQuestionnaireComplete ??
          chat.metadata?.isQuestionnaireComplete,
        questionnaireCompletedAt:
          updates.questionnaireCompletedAt ??
          chat.metadata?.questionnaireCompletedAt,
        diagnosisMessageSent:
          updates.diagnosisMessageSent ?? chat.metadata?.diagnosisMessageSent,
        earlyCompletion:
          updates.earlyCompletion ?? chat.metadata?.earlyCompletion,
      };

      if (typeof this.context.metadata.currentQuestionIndex === "number") {
        console.log(
          `[QuestionnaireService] Explicitly setting question index to: ${this.context.metadata.currentQuestionIndex}`
        );
        updatedMetadata.currentQuestionIndex =
          this.context.metadata.currentQuestionIndex;
      }

      const result = await client.models.Chat.update({
        id: this.chatId,
        metadata: updatedMetadata,
      });

      if (!result.data?.metadata) {
        console.error(
          "[QuestionnaireService] Metadata update response is missing data"
        );
      } else {
        console.log(
          `[QuestionnaireService] Confirmed index in database: ${result.data.metadata.currentQuestionIndex}`
        );
      }

      this.context.metadata = result.data?.metadata || updatedMetadata;

      console.log(
        "[QuestionnaireService] Metadata update successful, local state synchronized"
      );
    } catch (error) {
      console.error("[QuestionnaireService] Error updating metadata:", error);
      throw error;
    }
  }

  /**
   * Returns the current questionnaire context
   */
  getContext(): Context {
    return this.context;
  }

  /**
   * Returns the current strategy being used
   */
  getStrategy(): QuestionnaireStrategy {
    return this.strategy;
  }
}
