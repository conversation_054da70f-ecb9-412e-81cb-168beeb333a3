import { useState, useEffect } from "react";
import { generateClient } from "aws-amplify/api";
import type { Schema } from "@/amplify/data/resource";

// Type definition for User
type UserType = {
  id?: string;
  userId?: string;
  name?: string;
  email?: string;
  [key: string]: any;
};

// Type guard to check if currentUser has userId
export const isValidUser = (user: any): user is UserType & { userId: string } => {
  return user && typeof user === 'object' && 'userId' in user && typeof user.userId === 'string' && user.userId !== '';
};

export function useFriendRequests(currentUser: any) {
  const [users, setUsers] = useState<any[]>([]);
  const [friendRequests, setFriendRequests] = useState<any[]>([]);
  const [pendingRequestCount, setPendingRequestCount] = useState(0);
  const [loading, setLoading] = useState(true);
  
  const client = generateClient<Schema>();

  // Fetch all users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const { data } = await client.models.User.list();
        
        if (Array.isArray(data)) {
          const validUsers = data
            .filter(user => {
              if (!user || typeof user !== "object") return false;
              const typedUser = user as UserType;
              
              if (!isValidUser(currentUser)) return typedUser.userId;
              
              return typedUser.userId && typedUser.userId !== currentUser.userId;
            })
            .map(user => {
              const typedUser = user as UserType;
              return {
                userId: typedUser.userId,
                name: typedUser.name || "",
                email: typedUser.email || "",
                friendRequestStatus: null
              };
            });
          setUsers(validUsers);
        } else {
          setUsers([]);
        }
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };

    if (isValidUser(currentUser)) {
      fetchUsers();
      fetchFriendRequests();
    }
  }, [currentUser]);

  // Fetch friend requests
  const fetchFriendRequests = async () => {
    if (!isValidUser(currentUser)) return;
    
    try {
      const sentRequests = await client.models.FriendRequest.listSentFriendRequests({
        senderId: currentUser.userId as string
      });
      
      const receivedRequests = await client.models.FriendRequest.listReceivedFriendRequests({
        receiverId: currentUser.userId as string
      });
      
      const allRequests = [...(sentRequests.data || []), ...(receivedRequests.data || [])];
      setFriendRequests(allRequests);
      
      const pendingCount = receivedRequests.data?.filter(req => req.status === "PENDING").length || 0;
      setPendingRequestCount(pendingCount);
      
      updateUsersWithFriendStatus(allRequests);
    } catch (error) {
      console.error("Error fetching friend requests:", error);
    }
  };
  
  // Update users with friend request status
  const updateUsersWithFriendStatus = (requests: any[]) => {
    if (!isValidUser(currentUser)) return;
    
    const userId: string = currentUser.userId;
    
    setUsers(prevUsers => {
      return prevUsers.map(user => {
        const sentRequest = requests.find(req => 
          req.senderId === userId && req.receiverId === user.userId
        );
        
        const receivedRequest = requests.find(req => 
          req.senderId === user.userId && req.receiverId === userId
        );
        
        let friendRequestStatus = null;
        
        if (sentRequest) {
          friendRequestStatus = {
            type: "sent",
            status: sentRequest.status
          };
        } else if (receivedRequest) {
          friendRequestStatus = {
            type: "received",
            status: receivedRequest.status,
            requestId: receivedRequest.id
          };
        }
        
        return {
          ...user,
          friendRequestStatus
        };
      });
    });
  };

  // Send friend request
  const sendFriendRequest = async (userId: string) => {
    if (!isValidUser(currentUser)) {
      console.error("No current user found");
      return;
    }
    
    try {
      console.log(`Sending friend request from ${currentUser.userId} to ${userId}`);
      
      const result = await client.models.FriendRequest.create({
        senderId: currentUser.userId as string,
        receiverId: userId,
        status: "PENDING",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      
      console.log("Friend request created:", result);
      
      setUsers(prevUsers => {
        return prevUsers.map(user => {
          if (user.userId === userId) {
            return {
              ...user,
              friendRequestStatus: {
                type: "sent",
                status: "PENDING"
              }
            };
          }
          return user;
        });
      });
      
      fetchFriendRequests();
    } catch (error) {
      console.error("Error sending friend request:", error);
    }
  };

  // Respond to friend request
  const respondToFriendRequest = async (requestId: string, status: "ACCEPTED" | "DECLINED") => {
    try {
      await client.models.FriendRequest.update({
        id: requestId,
        status,
        updatedAt: new Date().toISOString()
      });
      
      fetchFriendRequests();
    } catch (error) {
      console.error(`Error ${status.toLowerCase()}ing friend request:`, error);
    }
  };

  return {
    users,
    loading,
    friendRequests,
    pendingRequestCount,
    sendFriendRequest,
    respondToFriendRequest,
    fetchFriendRequests
  };
}
