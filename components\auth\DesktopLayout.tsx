import Image from "next/image"
import Link from "next/link"
import { SocialButtons } from "./SocialButtons"
import RegistrationForm from "@/components/auth/RegistrationForm"
import ConfirmationForm from "@/components/auth/ConfirmationForm"
import { UseFormReturn } from "react-hook-form"
import { FormData } from "@/types/auth"
import RightSide from "./RightSide"

interface DesktopLayoutProps {
  isCodeSent: boolean;
  form: UseFormReturn<FormData>;
  handleRegister: (data: FormData) => void;
  loading: boolean;
  errorMessage?: string;
  confirmationCode: string;
  setConfirmationCode: (code: string) => void;
  handleConfirmSignUp: () => void;
  handleResendCode: () => void;
  handleGoogleSignIn: () => void;
  handleFacebookSignIn: () => void;
}

export function DesktopLayout({
  isCodeSent,
  form,
  handleRegister,
  loading,
  errorMessage,
  confirmationCode,
  setConfirmationCode,
  handleConfirmSignUp,
  handleResendCode,
  handleGoogleSignIn,
  handleFacebookSignIn,
}: DesktopLayoutProps) {
  return (
    <>
   

    
      <div className="hidden md:flex w-full md:w-1/2 flex-col justify-start h-full bg-white px-8 lg:px-16 xl:px-28 2xl:px-[140px] py-8 lg:pt-32 lg:pb-[224px] h-responsive-padding rounded-3xl overflow-hidden">
      <div className="flex">
          <p className="text-[#FFB300] text-lg font-medium mb-2">jin</p>
          <p className="text-[#4187FF] text-lg font-medium mb-2">I</p>
          <p className="text-[#D93C85] text-lg font-medium mb-2">X</p>
          </div>
        <h1 className="text-4xl font-normal text-gray-900 mb-4">Health journal</h1>
        <h1 className="text-4xl font-normal text-gray-900 mb-6">from jinx to jinIX</h1>
        
      
          <>
            <RegistrationForm
              form={form}
              handleRegister={handleRegister}
              loading={loading}
              errorMessage={errorMessage || ""}
            />

            <style jsx global>{`
              @media (max-height: 900px) {
                .h-responsive-padding {
                  padding-top: clamp(1rem, 5vh, 8rem) !important;
                  padding-bottom: clamp(2rem, 10vh, 14rem) !important;
                }
              }
              @media (max-height: 800px) {
                .h-responsive-padding {
                  overflow: auto !important;
                }
              }
            `}</style>

            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gray-300"></div>
              <p className="mx-4 text-gray-600 text-sm">Or</p>
              <div className="flex-1 h-px bg-gray-300"></div>
            </div>

            <SocialButtons
              onGoogleSignIn={handleGoogleSignIn}
              onFacebookSignIn={handleFacebookSignIn}
              loading={loading}
            />
             <p className="text-center text-sm text-gray-600 mt-4">
              Already have an account?{" "}
              <Link href="/login" className="text-blue-500 hover:underline">
                Sign In
              </Link>
            </p>
          </>
       
      </div>

      <RightSide />
        
    </>
  );
}
