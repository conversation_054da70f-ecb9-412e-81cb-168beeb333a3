import { useState, useEffect, useRef, useCallback } from "react";
import { events } from "aws-amplify/data";
import { Schema } from "@/amplify/data/resource";
import { AI_ASSISTANT_ID } from "@/constants/chat";
import { GhostMessageType, MessageWithoutChat } from "@/types/chat";

interface EventPayload {
  chunk?: string;
}

interface EventData {
  type?: string;
  event?: {
    type?: string;
    payload?: EventPayload;
  };
  payload?: { payload?: EventPayload };
  message?: { payload?: EventPayload };
}

const UPDATE_INTERVAL = 50;
const TRANSITION_DELAY = 100;

/**
 * Custom hook to manage streaming messages in a chat application.
 * @param chatId - The ID of the chat, or null if not available
 * @param sessionId - The session ID, or null if not available
 * @param addMessage - Optional callback to add a permanent message to the chat
 * @param onFinalMessageAdded - Optional callback to be called when the final message is added
 * @returns Object containing streaming state and utility functions
 */
export function useMessageStreaming(
  chatId: string | null,
  sessionId: string | null,
  addMessage?: (message: MessageWithoutChat) => void,
  onFinalMessageAdded?: () => void
) {
  const [ghostMessage, setGhostMessage] = useState<
    (GhostMessageType & { isConverting?: boolean }) | null
  >(null);
  const [error, setError] = useState<string | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const streamingMessageIdRef = useRef<string | null>(null);
  const chunkBuffer = useRef<string[]>([]);
  const updateTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const expectingAssistantResponse = useRef(false);
  const lastUserMessageTime = useRef<Date | null>(null);
  const isActiveRef = useRef(true);
  const lastProcessedContent = useRef<string>("");
  const streamCompletedRef = useRef(false);
  const finalMessageAddedRef = useRef(false);

  const allReceivedTokens = useRef<{ chunks: string[]; timestamps: number[] }>({
    chunks: [],
    timestamps: [],
  });
  const allReceivedEventsRef = useRef<EventData[]>([]);
  const debugLogTimer = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    resetStreaming();
  }, [chatId]);

  useEffect(() => {
    if (!onFinalMessageAdded) return;
    finalMessageAddedRef.current = false;
  }, [chatId, onFinalMessageAdded]);

  useEffect(() => {
    if (!chatId || !sessionId) return;

    let eventsChannel: Awaited<ReturnType<typeof events.connect>>;
    isActiveRef.current = true;
    streamCompletedRef.current = false;

    allReceivedTokens.current = { chunks: [], timestamps: [] };
    allReceivedEventsRef.current = [];

    const extractChunk = (data: EventData): string | undefined => {
      let chunk: string | undefined;

      if (data.type === "data" && data.event?.type === "token") {
        chunk = data.event.payload?.chunk;
      } else if (data.payload?.payload?.chunk) {
        chunk = data.payload.payload.chunk;
      } else if (data.message?.payload?.chunk) {
        chunk = data.message.payload.chunk;
      } else if (data.event?.payload?.chunk) {
        chunk = data.event.payload.chunk;
      }

      if (chunk) {
        allReceivedTokens.current.chunks.push(chunk);
        allReceivedTokens.current.timestamps.push(Date.now());
      }

      return chunk;
    };

    const processChunk = (chunk: string) => {
      if (finalMessageAddedRef.current || !isActiveRef.current) return;

      setGhostMessage((prev) => {
        const currentTime = new Date().toISOString();

        if (!prev) {
          return {
            id: `temp-${Date.now()}`,
            chatId,
            userId: AI_ASSISTANT_ID,
            message: chunk,
            createdAt: currentTime,
            updatedAt: currentTime,
            isGhost: true,
          };
        }

        const updatedMessage = prev.message + chunk;

        return {
          ...prev,
          message: updatedMessage,
          updatedAt: currentTime,
        };
      });
    };

    const processChunks = () => {
      if (finalMessageAddedRef.current || chunkBuffer.current.length === 0)
        return;

      const content = chunkBuffer.current.join("");
      chunkBuffer.current = [];

      processChunk(content);
    };

    const scheduleUpdate = () => {
      if (!updateTimer.current) {
        updateTimer.current = setTimeout(() => {
          processChunks();
          updateTimer.current = null;
        }, UPDATE_INTERVAL);
      }
    };

    const completeStreaming = () => {
      if (finalMessageAddedRef.current) {
        setGhostMessage(null);
        setIsTransitioning(false);
        return;
      }

      if (chunkBuffer.current.length > 0) {
        processChunks();
      }

      if (!ghostMessage) {
        setGhostMessage(null);
        return;
      }

      chunkBuffer.current = [];
      if (updateTimer.current) {
        clearTimeout(updateTimer.current);
        updateTimer.current = null;
      }

      setGhostMessage((prev) =>
        prev ? { ...prev, isConverting: true } : null
      );
      setIsTransitioning(true);

      setTimeout(() => {
        if (isActiveRef.current && !finalMessageAddedRef.current) {
          setGhostMessage(null);
          setIsTransitioning(false);
          lastProcessedContent.current = "";
        }
      }, TRANSITION_DELAY);
    };

    const handleEventData = (data: EventData) => {
      if (!isActiveRef.current) return;

      allReceivedEventsRef.current.push(data);

      if (data.type === "data" && data.event?.type === "complete") {
        streamCompletedRef.current = true;

        if (chunkBuffer.current.length > 0) {
          processChunks();
        }

        completeStreaming();
        return;
      }

      if (streamCompletedRef.current) {
        return;
      }

      const chunk = extractChunk(data);
      if (chunk) {
        if (
          !debugLogTimer.current &&
          allReceivedTokens.current.chunks.length % 20 === 0
        ) {
          debugLogTimer.current = setTimeout(() => {
            debugLogTimer.current = null;
          }, 500);
        }

        chunkBuffer.current.push(chunk);
        scheduleUpdate();
      } else {
      }
    };

    const connectToEventChannel = async () => {
      try {
        // Use the proper channel path that matches your backend configuration
        eventsChannel = await events.connect(`default/chat-${chatId}`);
        eventsChannel.subscribe({
          next: handleEventData,
          error: (err) => {
            console.error("Event subscription error:", err);
            setError("Failed to subscribe to streaming events");
          },
        });
      } catch (err) {
        console.error("Failed to connect to event channel:", err);
        setError("Failed to connect to streaming events");
      }
    };

    connectToEventChannel();

    return () => {
      isActiveRef.current = false;
      // Log raw events on cleanup as well, in case completion wasn't reached
      console.log(
        "[Ghost Message] Cleanup - All raw events received:",
        allReceivedEventsRef.current
      );
      if (updateTimer.current) {
        clearTimeout(updateTimer.current);

        if (chunkBuffer.current.length > 0) {
          processChunks();
        }
      }
      if (debugLogTimer.current) {
        clearTimeout(debugLogTimer.current);
      }
      if (eventsChannel) eventsChannel.close();
    };
  }, [chatId, sessionId, addMessage, ghostMessage]);

  const handleFinalMessageAdded = useCallback(() => {
    finalMessageAddedRef.current = true;
    setGhostMessage(null);
    setIsTransitioning(false);
    lastProcessedContent.current = "";
  }, []);

  const resetStreaming = () => {
    streamingMessageIdRef.current = null;
    expectingAssistantResponse.current = false;
    lastUserMessageTime.current = null;
    chunkBuffer.current = [];
    if (updateTimer.current) clearTimeout(updateTimer.current);
    updateTimer.current = null;
    lastProcessedContent.current = "";
    streamCompletedRef.current = false;
    finalMessageAddedRef.current = false;

    allReceivedTokens.current = { chunks: [], timestamps: [] };
    allReceivedEventsRef.current = [];
    if (debugLogTimer.current) {
      clearTimeout(debugLogTimer.current);
      debugLogTimer.current = null;
    }

    setGhostMessage(null);
    setIsTransitioning(false);
  };

  const prepareForResponse = () => {
    lastUserMessageTime.current = new Date();
    expectingAssistantResponse.current = true;
  };

  const checkForNewAIMessages = (
    messages: Array<Omit<Schema["ChatMessage"]["type"], "chat">>
  ) => {
    if (!expectingAssistantResponse.current || !lastUserMessageTime.current)
      return;

    const newAIMessages = messages.filter(
      (msg) =>
        msg.userId === AI_ASSISTANT_ID &&
        msg.createdAt &&
        new Date(msg.createdAt) > lastUserMessageTime.current!
    );

    if (newAIMessages.length > 0) {
      setGhostMessage(null);
      streamingMessageIdRef.current = newAIMessages[0].id;
      expectingAssistantResponse.current = false;
      finalMessageAddedRef.current = true;
    }
  };

  return {
    ghostMessage:
      ghostMessage && !ghostMessage.isConverting ? ghostMessage : null,
    streamingMessageId: streamingMessageIdRef.current,
    isStreaming: !!ghostMessage,
    isTransitioning,
    streamingError: error,
    resetStreaming,
    prepareForResponse,
    checkForNewAIMessages,
    onFinalMessageAdded: handleFinalMessageAdded,
  };
}
