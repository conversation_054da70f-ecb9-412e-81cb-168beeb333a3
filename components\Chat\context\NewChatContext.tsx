"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { Chat } from "@/types/chat";
import { useViewState } from "./ViewStateContext";

// Extended SelectedUser interface to cover both id and userId scenarios
export interface SelectedUser {
  id: string;
  userId?: string; // Adding userId for flexibility
  email?: string;
  profilePicture?: string;
}

// Interface for new chat related data
export interface NewChatData {
  selectedUserIds: string[];
  selectedQuestionnaireId: string | null;
  isCreatingChat: boolean;
  totalUserCount: number;
  handleConfirmCreateChat: (params?: { chatName?: string }) => Promise<void>;
  handleClose: () => void;
}

/**
 * A wrapper component that provides backward compatibility with existing code
 * by mapping the new ViewState context to the old ChatContext interface
 */
export const ChatContextAdapter = ({ children }: { children: ReactNode }) => {
  const {
    currentChat,
    setCurrentChat,
    selectedUserDetail,
    setSelectedUserDetail,
    activeView,
    setActiveView,
    newChatData,
    setNewChatData,
  } = useViewState();

  // Map the new view state to the old boolean flags
  const showChatDetails =
    activeView === "CHAT_DETAILS" || activeView === "USER_DETAILS";
  const showNewChatPage = activeView === "NEW_CHAT";

  // Map the old boolean setters to the new view state
  const setShowChatDetails = (show: boolean) => {
    if (show) {
      setActiveView("CHAT_DETAILS");
    } else if (activeView === "CHAT_DETAILS" || activeView === "USER_DETAILS") {
      setActiveView("CHAT_CONVERSATION");
    }
  };

  const setShowNewChatPage = (show: boolean) => {
    if (show) {
      setActiveView("NEW_CHAT");
    } else if (activeView === "NEW_CHAT") {
      setActiveView("CHAT_LIST");
    }
  };

  // Create the compatibility value object
  const value = {
    currentChat,
    setCurrentChat,
    selectedUserDetail,
    setSelectedUserDetail,
    showChatDetails,
    setShowChatDetails,
    showNewChatPage,
    setShowNewChatPage,
    newChatData,
    setNewChatData,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

// Type for the compatibility layer
export type ChatContextType = {
  currentChat: Chat | null;
  setCurrentChat: (chat: Chat | null) => void;
  selectedUserDetail: SelectedUser | null;
  setSelectedUserDetail: (user: SelectedUser | null) => void;
  showChatDetails: boolean;
  setShowChatDetails: (show: boolean) => void;
  showNewChatPage: boolean;
  setShowNewChatPage: (show: boolean) => void;
  newChatData: NewChatData | null;
  setNewChatData: (data: NewChatData | null) => void;
};

// Create the context with default values
export const ChatContext = createContext<ChatContextType>({
  currentChat: null,
  setCurrentChat: () => {},
  selectedUserDetail: null,
  setSelectedUserDetail: () => {},
  showChatDetails: false,
  setShowChatDetails: () => {},
  showNewChatPage: false,
  setShowNewChatPage: () => {},
  newChatData: null,
  setNewChatData: () => {},
});

// Hook for using the context
export const useChatContext = () => useContext(ChatContext);
