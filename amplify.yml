version: 1
environment:
  computeType: BUILD_GENERAL1_LARGE  # Збільшення пам’яті для уникнення ENOMEM
backend:
  phases:
    preBuild:
      commands:
        - npm ci --cache .npm --prefer-offline  # Встановлення залежностей із кешуванням
    build:
      commands:
        - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
  cache:
    paths:
      - .npm/**/*  # Кеш для npm
      - node_modules/**/*  # Кеш для залежностей
frontend:
  phases:
    preBuild:
      commands:
        - npm ci --cache .npm --prefer-offline  # Додано для frontend
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - .next/cache/**/*  # Кеш Next.js
      - .npm/**/*  # Кеш npm
      - node_modules/**/*  # Кеш залежностей