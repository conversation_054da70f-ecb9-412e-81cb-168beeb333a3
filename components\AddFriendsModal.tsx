"use client";

import AddFriendsPage from "@/app/(protected)/add-friends/page";
import Image from "next/image";
import { useEffect } from "react";
import { createPortal } from "react-dom";

interface AddFriendsModalProps {
  onClose: () => void;
  isModal?: boolean;
}

export default function AddFriendsModal({ onClose, isModal = true }: AddFriendsModalProps) {
  // Add ESC key handler for modal
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    window.addEventListener('keydown', handleEsc);
    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [onClose]);

  const modalContent = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-[#00000099] backdrop-blur-[10px]">
      <div className="bg-gray-50 rounded-lg w-[500px] h-[600px] overflow-auto relative">
        <AddFriendsPage />
      </div>
    </div>
  );

  // Use createPortal to render the modal at the document body level
  return typeof document !== 'undefined' ? createPortal(modalContent, document.body) : null;
}
