import Image from "next/image";
import React, { useEffect, useState } from "react";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";

interface TimelineMed {
  name: string;
  start: number;
  end: number;
  color: string;
}

const colors = [
  "#FFA800", "#FF6B9E", "#4F6BFF", "#2EC4B6", "#FFB76B",
  "#FF9E6B", "#4F6BFF", "#2EC4B6", "#FFA800", "#FF6B9E",
  "#4F6BFF", "#2EC4B6", "#FFB76B", "#FF9E6B", "#2EC4B6",
];

export default function MedicationTimeline({ userId }: { userId: string }) {
  const [medicationTimelineData, setMedicationTimelineData] = useState<TimelineMed[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const service = new OneUpHealthService({ userId });
      try {
        const records: MedicalRecord[] = await service.fetchMedicalRecordsFromDatabase();
        const meds = records.filter(r => r.type === "Medication");
        // Map to timeline data (simulate start/end by month index)
        const timeline = meds.map((med, i) => {
          const start = Math.floor(Math.random() * 7);
          const end = start + Math.floor(Math.random() * 5) + 2;
          return {
            name: med.description,
            start,
            end,
            color: colors[i % colors.length],
          };
        });
        setMedicationTimelineData(timeline);
      } catch (e) {
        setMedicationTimelineData([]);
      }
      setLoading(false);
    };
    fetchData();
  }, [userId]);

  return (
    <div className="w-full md:w-[100%] mx-auto mb-4 bg-gray-100 p-3 rounded-[28px] border border-gray-200">
      <div className="p-4 bg-white rounded-[28px] shadow-sm border border-gray-200 flex flex-col">
        <div className="flex items-center mb-2">
          <Image
            src="/outpatient/heart-rate-monitor-blue.svg"
            alt="Medication Timeline Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-semibold text-lg text-[#2EC4B6]">
            Medication Administration Timeline
          </span>
        </div>
        <div style={{ width: "100%" }}>
          <div
            style={{
              position: "relative",
              width: "100%",
              height: medicationTimelineData.length > 0 ? medicationTimelineData.length * 32 + 80 : 112,
              minHeight: 112,
              paddingBottom: 32,
              boxSizing: "border-box",
            }}
          >
            {medicationTimelineData.map((_, i) => (
              <div
                key={i}
                style={{
                  position: "absolute",
                  top: `${i * 32 + 10}px`,
                  left: 0,
                  width: "100%",
                  borderTop: "1px dashed #E5E7EB",
                  zIndex: 0,
                }}
              />
            ))}
            <div style={{ width: "100%", overflowX: "auto" }}>
              <table className="w-full border-separate" style={{ borderSpacing: 0, position: "relative", zIndex: 1 }}>
                <tbody>
                  {loading ? (
                    <tr><td className="text-gray-400 text-center py-2">Loading...</td></tr>
                  ) : medicationTimelineData.length === 0 ? (
                    <tr><td className="text-gray-400 text-center py-2">No medication data found</td></tr>
                  ) : (
                    medicationTimelineData.map((med, i) => (
                      <tr key={med.name} style={{ height: 32 }}>
                        <td className="text-xs text-gray-600 pr-2" style={{ width: 140 }}>{med.name}</td>
                        <td>
                          <div style={{ position: "relative", height: 28 }}>
                            <div
                              style={{
                                position: "absolute",
                                left: `${(med.start / 13) * 100}%`,
                                width: `${((med.end - med.start) / 13) * 100}%`,
                                height: 20,
                                background: med.color,
                                borderRadius: 10,
                              }}
                            />
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
            {/* Timeline labels positioned at the bottom */}
            <div
              className="flex justify-between text-xs text-gray-400 mt-1"
              style={{
                position: "absolute",
                left: 140,
                right: 0,
                bottom: 0,
                height: 18,
                alignItems: "center",
                background: "white",
                zIndex: 2,
                pointerEvents: "none",
              }}
            >
              <span>2024 Jan</span>
              <span>2025 Jan</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
