import { z } from 'zod';

export const registrationSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[!@#$%^&*(),.?":{}|<>_]/, 'Password must contain at least one special character'),
  confirmPassword: z.string()
    .min(8, 'Confirmation password must be at least 8 characters long')
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
});



export const resetPasswordSchema = z.object({
  code: z.string().min(6, "Verification code must be at least 6 characters").nonempty("Verification code is required"),
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[!@#$%^&*(),.?":{}|<>_]/, 'Password must contain at least one special character')
    .nonempty("New password is required"),
  confirmPassword: z.string()
    .min(8, 'Confirmation password must be at least 8 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[!@#$%^&*(),.?":{}|<>_]/, 'Password must contain at least one special character')
    .nonempty("Please confirm your password")
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});

