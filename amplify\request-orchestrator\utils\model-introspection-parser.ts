export function parseModelIntrospection(modelIntrospectionJson: any): any {
  if (!modelIntrospectionJson || typeof modelIntrospectionJson !== "object") {
    throw new Error("Invalid model introspection: Must be a non-null object");
  }

  const { models, enums, nonModels, mutations, inputs, version } =
    modelIntrospectionJson;

  if (!models) {
    throw new Error("Invalid model introspection: Missing 'models' property");
  }

  return {
    models,
    enums: enums || {},
    nonModels: nonModels || {},
    mutations: mutations || {},
    inputs: inputs || {},
    version: version || 1,
  };
}

export function validateModelIntrospection(modelIntrospection: any): boolean {
  if (!modelIntrospection || typeof modelIntrospection !== "object") {
    return false;
  }

  if (
    !modelIntrospection.models ||
    typeof modelIntrospection.models !== "object"
  ) {
    return false;
  }

  if (Object.keys(modelIntrospection.models).length === 0) {
    return false;
  }

  return true;
}
