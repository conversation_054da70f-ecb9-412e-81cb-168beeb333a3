const { OpenAI } = require('openai');

// 使用您提供的新 API 密钥
const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

async function testOpenAI() {
  console.log('🔍 Testing OpenAI API connection...');
  console.log('API Key:', OPENAI_API_KEY.substring(0, 20) + '...');
  
  const openai = new OpenAI({
    apiKey: OPENAI_API_KEY,
  });

  try {
    console.log('\n📡 Sending test request to OpenAI...');
    
    const response = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        {
          role: 'user',
          content: 'Hello! Please respond with "API test successful" if you can see this message.'
        }
      ],
      max_tokens: 50,
      temperature: 0.7,
    });

    console.log('\n✅ OpenAI API Response:');
    console.log('Model:', response.model);
    console.log('Usage:', response.usage);
    console.log('Response:', response.choices[0].message.content);
    console.log('\n🎉 OpenAI API is working correctly!');
    
    return true;
  } catch (error) {
    console.error('\n❌ OpenAI API Error:');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    
    if (error.status) {
      console.error('HTTP Status:', error.status);
    }
    
    if (error.code) {
      console.error('Error code:', error.code);
    }
    
    return false;
  }
}

// 运行测试
testOpenAI().then((success) => {
  if (success) {
    console.log('\n✨ Test completed successfully!');
  } else {
    console.log('\n💥 Test failed. Please check the error details above.');
  }
  process.exit(success ? 0 : 1);
}).catch((error) => {
  console.error('\n🚨 Unexpected error:', error);
  process.exit(1);
});
