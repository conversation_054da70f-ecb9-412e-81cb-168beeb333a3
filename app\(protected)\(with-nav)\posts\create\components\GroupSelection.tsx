import { useRouter } from "next/navigation";
import Image from "next/image";

export function GroupSelection() {
  const router = useRouter();
  
  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center">
        <button 
          className="flex items-center py-1 px-2 rounded-full text-sm text-black bg-white relative"
          style={{
            background: 'linear-gradient(#fff, #fff) padding-box, linear-gradient(to right, #FFB300, #0051FF, #FF569F) border-box',
            border: '1px solid transparent',
          }}
        >
          <div className="w-6 h-6 rounded-full flex-shrink-0 mr-2 overflow-hidden">
            <Image
              src="/groupavatar.svg"
              alt="Group Avatar"
              width={24}
              height={24}
              className="object-cover rounded-full"
            />
          </div>
          <p className="text-sm">Group Name</p>
        </button>
      </div>
      <button 
        className="text-sm text-[#171717] uppercase mr-2"
        onClick={() => router.push("/posts/rules")}
      >
        Rules
      </button>
    </div>
  );
}
