import { AiService } from "./AiService";
import { generateClient } from "@aws-amplify/api";
import { AI_ASSISTANT_ID } from "../../../constants/chat";
import type { Schema } from "../../data/resource";

const client = generateClient<Schema>();

export class MedicalProfileService {
  private aiService: AiService;

  constructor(aiService: AiService) {
    this.aiService = aiService;
  }

  async generateProfile(chatId: string): Promise<any> {
    console.log(
      `[MedicalProfileService] Generating medical profile for chat ${chatId}`
    );

    try {
      const { data: messagesData } =
        await client.models.ChatMessage.listMessagesByDate(
          { chatId },
          { sortDirection: "ASC" }
        );

      if (!messagesData || messagesData.length === 0) {
        throw new Error("No chat messages found for profile generation");
      }

      const userResponses = messagesData
        .filter((msg) => msg && msg.userId !== AI_ASSISTANT_ID)
        .map((msg) => msg.message || "")
        .filter((message) => message.trim().length > 0);

      const systemQuestions = messagesData
        .filter((msg) => msg && msg.userId === AI_ASSISTANT_ID)
        .map((msg) => msg.message || "")
        .filter((message) => message.trim().length > 0);

      console.log(
        `[MedicalProfileService] Processing ${userResponses.length} user responses`
      );

      const profileData = await this.createProfileWithAI(
        userResponses,
        systemQuestions
      );

      const profileId = await this.saveProfileToDatabase(chatId, profileData);

      return {
        profileId,
        ...profileData,
      };
    } catch (error) {
      console.error(
        "[MedicalProfileService] Error generating medical profile:",
        error
      );
      throw error;
    }
  }

  private async createProfileWithAI(
    userResponses: string[],
    systemQuestions: string[]
  ): Promise<any> {
    let context = "";
    const minLength = Math.min(systemQuestions.length, userResponses.length);

    for (let i = 0; i < minLength; i++) {
      context += `Question: ${systemQuestions[i]}\nAnswer: ${userResponses[i]}\n\n`;
    }

    if (userResponses.length > systemQuestions.length) {
      context += "Additional information provided by the patient:\n";
      for (let i = systemQuestions.length; i < userResponses.length; i++) {
        context += `- ${userResponses[i]}\n`;
      }
    }

    const prompt = `
      You are a medical AI assistant creating a structured patient profile. Based on the conversation 
      below, extract relevant medical information and organize it into a structured JSON object.
      
      ${context}
      
      Generate a JSON object with the following fields:
      - demographicInfo: basic patient information (age, gender, etc.)
      - mainSymptoms: primary symptoms reported
      - medicalHistory: relevant medical history mentioned
      - medications: any medications mentioned
      - lifestyle: lifestyle factors mentioned (smoking, exercise, etc.)
      - riskFactors: potential risk factors identified
      - notes: any additional important information
      
      Return ONLY the valid JSON object with no additional explanation.
    `;

    try {
      const response = await this.aiService.getChatCompletion(prompt, []);
      const profileData = JSON.parse(response);

      return profileData;
    } catch (error) {
      console.error(
        "[MedicalProfileService] Error generating profile with AI:",
        error
      );

      return {
        demographicInfo: {},
        mainSymptoms: [],
        medicalHistory: [],
        medications: [],
        lifestyle: {},
        riskFactors: [],
        notes: "Error occurred during profile generation",
      };
    }
  }

  private async saveProfileToDatabase(
    chatId: string,
    profileData: any
  ): Promise<string> {
    try {
      const { data: chat } = await client.models.Chat.get({ id: chatId });

      // Find the patient userId from ChatParticipant (assume not AI/doctor)
      const { data: participants } = await client.models.ChatParticipant.listChatParticipantsByChat({ chatId });
      if (!participants || participants.length === 0) {
        throw new Error("Unable to find valid user for this chat");
      }
      // Pick the first non-AI participant as the patient
      const userId = participants.find(p => p.userId !== AI_ASSISTANT_ID)?.userId;
      if (!userId) {
        throw new Error("Unable to find valid user for this chat");
      }

      const { data: profile } = await client.models.MedicalProfile.create({
        userId,
        chatId,
        profileData,
        createdAt: new Date().toISOString(),
      });

      if (!profile || profile.userId !== userId) {
        throw new Error("Failed to create medical profile record");
      }

      console.log(
        `[MedicalProfileService] Created medical profile with ID: ${profile.userId}`
      );

      return profile.userId;
    } catch (error) {
      console.error(
        "[MedicalProfileService] Error saving profile to database:",
        error
      );
      throw error;
    }
  }
}
