"use client";

import * as React from "react";
import dynamic from "next/dynamic";
import { useMap as useMapHook } from "@/hooks/useMap";
import { useSearchParams } from "next/navigation";
import DoctorList from "@/components/map/DoctorList";
import GeoErrorModal from "@/components/map/GeoErrorModal";

const LeafletMapWithNoSSR = dynamic(
  () => import("@/components/map/LeafletMapComponent"),
  {
    ssr: false,
  }
);

function MapPageContent() {
  const searchParams = useSearchParams();

  const initialCountry = searchParams?.get("country") || "";
  const initialZipCode = searchParams?.get("zipCode") || "";

  const {
    error,
    geoError,
    zipCode,
    handleZipChange,
    country,
    handleCountryChange,
    showZipInput,
    entitiesWithinRadius,
    setSelectedEntity,
    calculateDistance,
    lat,
    long,
  } = useMapHook(51.505, -0.09, 13, initialCountry, initialZipCode);

  const [panTo, setPanTo] = React.useState<{
    lat: number;
    long: number;
  } | null>(null);
  const [isListExpanded, setIsListExpanded] = React.useState(false);
  const [selectedEntity, setSelectedEntityState] = React.useState<any | null>(null);
  const [searchTerm, setSearchTerm] = React.useState(""); // Додаємо стан searchTerm

  const filteredEntities = entitiesWithinRadius.filter(
    (entity) => calculateDistance(lat, long, entity.lat, entity.long) <= 100
  );

  const handleDoctorClick = (entity: any) => {
    setSelectedEntityState(entity);
    setSelectedEntity(entity);
    setPanTo({ lat: entity.lat, long: entity.long });
  };

  React.useEffect(() => {
    setPanTo({ lat, long });
  }, [lat, long]);

  return (
    <div className="h-screen w-full flex flex-col md:flex-row">
      <div className="w-full md:w-1/3 md:h-full h-auto order-2 md:order-1 md:max-w-[420px] ">
        <DoctorList
          filteredEntities={filteredEntities}
          lat={lat}
          long={long}
          calculateDistance={calculateDistance}
          handleDoctorClick={handleDoctorClick}
          isListExpanded={isListExpanded}
          setIsListExpanded={setIsListExpanded}
          searchTerm={searchTerm} // Передаємо searchTerm
          setSearchTerm={setSearchTerm} // Передаємо setSearchTerm
          selectedEntity={selectedEntity} // Додаємо selectedEntity
        />
      </div>

      <div
        className={`flex-1 transition-all duration-300 ${
          isListExpanded ? "h-[20%]" : "h-[70%]"
        } md:h-full md:w-2/3 order-1 md:order-2`}
      >
        {error ? (
          <div className="flex h-full w-full items-center justify-center bg-red-50 text-red-600">
            {error.message}
          </div>
        ) : (
          
          <LeafletMapWithNoSSR
            lat={lat}
            long={long}
            zoom={13}
            filteredEntities={filteredEntities}
            selectedEntity={selectedEntity}
            setSelectedEntity={setSelectedEntityState}
            panTo={panTo}
            searchTerm={searchTerm} // Передаємо searchTerm
            setSearchTerm={setSearchTerm} // Передаємо setSearchTerm
            setIsListExpanded={setIsListExpanded}
          />
        )}
      </div>

      {geoError && (
        <GeoErrorModal
          geoError={geoError}
          country={country}
          handleCountryChange={handleCountryChange}
          showZipInput={showZipInput}
          zipCode={zipCode}
          handleZipChange={handleZipChange}
        />
      )}
    </div>
  );
}

export default function MapPage() {
  return (
    <React.Suspense fallback={<div>Loading map...</div>}>
      <MapPageContent />
    </React.Suspense>
  );
}