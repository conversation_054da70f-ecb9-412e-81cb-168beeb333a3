export interface FormData {
  email: string;
  password: string;
  confirmPassword: string;
}

export type RegisterFormData = {
  email: string;
  password: string;
  confirmPassword: string;
};

export type ConfirmationFormProps = {
  onConfirm: (code: string) => Promise<void>;
  loading: boolean;
};

export interface AuthButtonsProps {
  handleGoogleLogin: () => Promise<void>;
  handleFacebookLogin: () => Promise<void>;
}

export type UserProfileProps = {
  user: any;
  onLogout: () => void;
};

export type ChatMessageProps = {
  message: string;
  isUser: boolean;
};

export interface User {
  email: string;
  name: string;
}

export interface ChatLayoutProps {
  user: any;
  onLogout: () => Promise<void>;
}

export interface ConfirmationFormsProps {
  confirmationCode: string;
  setConfirmationCode: (code: string) => void;
  handleConfirmSignUp: () => void;
  handleResendCode: () => void;
  loading: boolean;
  errorMessage: string;
}

export interface LeafletProps {
  lat: number;
  long: number;
  zoom: number;
  embedded?: boolean;
  minimal?: boolean;
}

export interface MedicalEntity {
  name: string;
  type: "doctor" | "hospital";
  lat: number;
  long: number;
  address?: string;
  phone?: string;
  hours?: string;
  Specialties?: string[];
  WebsiteURL?: string;
}

export interface RegistrationFormProps {
  form: any;
  handleRegister: (data: any) => void;
  loading: boolean;
  errorMessage: string;
}

export interface ResetPasswordForm {
  code: string;
  newPassword: string;
  confirmPassword: string;
}

export interface SocialSignInButtonsProps {
  handleGoogleSignIn: () => void;
  handleFacebookSignIn: () => void;
}

export interface UserAttributes {
  email: string;
}
