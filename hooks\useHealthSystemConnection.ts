import { useState } from "react";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { toast } from "sonner";

const client = generateClient<Schema>();

export interface HealthProvider {
  id: string;
  name: string;
  systemId: string;
}

export const HEALTH_PROVIDERS: HealthProvider[] = [
  { id: "epic", name: "Epic Systems", systemId: "8364847" },
  { id: "cerner", name: "<PERSON><PERSON>", systemId: "4728" },
  { id: "allscripts", name: "Allscripts", systemId: "8364849" },
  { id: "athena", name: "Athenahealth", systemId: "8364850" },
  { id: "nextgen", name: "NextGen Healthcare", systemId: "8364851" },
  { id: "meditech", name: "MEDITECH", systemId: "8364852" },
  { id: "eclinicalworks", name: "eClinicalWorks", systemId: "8365599" },
  { id: "cpsi", name: "<PERSON><PERSON>", systemId: "8364854" },
  { id: "mckesson", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", systemId: "8364855" },
];

// Make sure to export the hook as a function
export function useHealthSystemConnection(userId?: string) {
  const [connectingSystem, setConnectingSystem] = useState<string | null>(null);

  const connectToProvider = async (provider: HealthProvider) => {
    if (!userId) {
      toast.error("User ID is required to connect to a health provider");
      return;
    }

    try {
      setConnectingSystem(provider.id);

      const authUrlResponse: any = await client.mutations.getOneUpHealthAuthUrl(
        {
          systemId: provider.systemId,
        }
      );

      // Check for success and access the nested data structure
      if (
        authUrlResponse.data?.success &&
        authUrlResponse.data?.data?.authorization_url
      ) {
        window.location.href = authUrlResponse.data.data.authorization_url;
      } else {
        const errorMessage =
          authUrlResponse.data?.error || "Failed to get authorization URL";
        toast.error(errorMessage);
        console.error("Authorization URL error:", errorMessage);
      }
    } catch (error) {
      console.error("Connection failed:", error);
      toast.error("Connection failed. Please try again.");
    } finally {
      setConnectingSystem(null);
    }
  };

  return {
    connectingSystem,
    connectToProvider,
    HEALTH_PROVIDERS,
  };
}
