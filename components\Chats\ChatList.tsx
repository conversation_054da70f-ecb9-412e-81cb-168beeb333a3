"use client";

import type React from "react";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { useUser } from "@/hooks/useUsers";
import { useMemo, useEffect, useState, useCallback } from "react";
import { AI_ASSISTANT_ID } from "@/constants/chat";
import { usePathname } from "next/navigation";
import { Skeleton } from "@/components/ui/skeleton";
import { generateClient } from "aws-amplify/api";
import { useAllChats } from "@/components/Chat/context/AllChatsContext";

import { ChatActionsMenu } from "./ActionMenu";
import { ChatItemProps } from "./types";
import ChatItem from "./ChatItem";
import { useCurrentChat } from "../Chat/context/ChatContext";
import { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>();

const ChatItemSkeleton = () => {
  return (
    <div className="flex items-center gap-4 py-[13px] px-5 w-full rounded-[24px]">
      <Skeleton className="h-[60px] w-[60px] rounded-full" />
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-center mb-2">
          <Skeleton className="h-5 w-[120px]" />
          <Skeleton className="h-4 w-[40px]" />
        </div>
        <Skeleton className="h-4 w-full max-w-[200px]" />
      </div>
    </div>
  );
};

const SectionLabel = ({ label }: { label: string }) => {
  return (
    <div className="flex items-center w-full py-[6px] px-6">
      <h3 className="text-[#929292] font-semibold text-sm">{label}</h3>
    </div>
  );
};

const ensureALSDoctorChat = async (
  userId: string,
  onNewChatCreated?: (chat: any) => void
): Promise<void> => {
  if (!userId) return;

  try {
    const chatId = `${userId}-als`;

    const { data: existingChat } = await client.models.Chat.get({ id: chatId });

    if (existingChat) {
      console.log("ALS Doctor Agent chat already exists:", existingChat.id);
      return;
    }

    console.log("Creating ALS Doctor Agent chat with ID:", chatId);
    const { data: chat } = await client.models.Chat.create({
      id: chatId,
      name: "AI Doctor Agent: ALS",
      description: "AI Doctor specializing in ALS treatment and care",
      chatType: "AI",
      questionnaireId: "1",
    });

    if (!chat) {
      console.error("Failed to create ALS Doctor Agent chat");
      return;
    }

    await client.models.ChatParticipant.create({
      id: chatId,
      chatId: chatId,
      userId: userId,
      joinedAt: new Date().toISOString(),
    });

    const { data: message } = await client.models.ChatMessage.create({
      chatId: chatId,
      userId: AI_ASSISTANT_ID,
      message:
        "Hello, I'm your AI Doctor Agent specializing in ALS. How can I assist you today?",
      messageType: "SYSTEM",
      attachments: [],
      createdAt: new Date().toISOString(),
    });

    console.log("Successfully created ALS Doctor Agent chat:", chatId);

    const newChat = {
      ...chat,
      chatParticipants: [
        {
          userId: userId,
          joinedAt: new Date().toISOString(),
        },
      ],
      lastMessage: message,
    };

    if (onNewChatCreated) {
      onNewChatCreated(newChat);
    }
  } catch (error) {
    console.error("Error ensuring ALS Doctor Agent chat exists:", error);
  }
};

export function ChatList() {
  const { currentUser, loading: isLoadingUser } = useUser();
  const userId = currentUser?.userId || null;
  const {
    chats: userChats,
    isLoading: isLoadingChats,
    addChat,
  } = useAllChats();
  const pathname = usePathname();
  const { setCurrentChat } = useCurrentChat();
  const [searchQuery, setSearchQuery] = useState("");
  const [localChats, setLocalChats] = useState<any[]>([]);
  const [alsChatCreated, setAlsChatCreated] = useState(false);

  const currentChatId =
    pathname?.startsWith("/chats/") && pathname !== "/chats"
      ? pathname.split("/")[2]
      : null;

  useEffect(() => {
    if (userId && !isLoadingUser && !alsChatCreated) {
      ensureALSDoctorChat(userId, (newChat) => {
        addChat(newChat);
        setLocalChats((prev) => {
          if (prev.some((chat) => chat.id === newChat.id)) {
            return prev;
          }
          return [...prev, newChat];
        });
        setAlsChatCreated(true);
      });
    }
  }, [userId, isLoadingUser, alsChatCreated, addChat]);

  useEffect(() => {
    if (userChats) {
      const alsChatId = userId ? `${userId}-als` : null;
      const alsChatInUserChats =
        alsChatId && userChats.some((chat) => chat.id === alsChatId);

      if (alsChatInUserChats) {
        setLocalChats([]);
      } else {
        // Otherwise, keep only the chats in localChats that aren't in userChats
        setLocalChats((prev) =>
          prev.filter(
            (localChat) =>
              !userChats.some((userChat) => userChat.id === localChat.id)
          )
        );
      }
    }
  }, [userChats, userId]);

  const combinedChats = useMemo(() => {
    if (!userChats || userChats.length === 0) return localChats;

    const chatMap = new Map();

    userChats.forEach((chat) => {
      if (chat && chat.id) {
        chatMap.set(chat.id, chat);
      }
    });

    // Then add localChats if they don't exist in userChats
    localChats.forEach((chat) => {
      if (chat && chat.id && !chatMap.has(chat.id)) {
        chatMap.set(chat.id, chat);
      }
    });

    return Array.from(chatMap.values());
  }, [userChats, localChats]);

  useEffect(() => {
    if (currentChatId && combinedChats.length > 0) {
      const chat = combinedChats.find((chat) => chat.id === currentChatId);
      if (chat) {
        setCurrentChat(chat);
      }
    } else {
      setCurrentChat(null);
    }
  }, [currentChatId, combinedChats, setCurrentChat]);

  const { pinnedChats, regularChats } = useMemo(() => {
    const filteredUserChats = (combinedChats || []).filter((chat) => {
      if (chat.id === `${userId}-health`) return false;
      return true;
    });

    const allChats = filteredUserChats.map((chat) => {
      const chatType = chat.chatType || "AI";

      let displayName;
      let avatarSrc;

      if (chatType === "DIRECT" && chat.chatParticipants) {
        const otherParticipant = chat.chatParticipants.find(
          (p: {
            userId: string;
            user?: { email?: string; name?: string; profilePicture?: string };
          }) => p.userId !== userId && p.userId !== AI_ASSISTANT_ID
        );

        displayName =
          otherParticipant?.user?.email ||
          otherParticipant?.user?.name ||
          "Chat";

        avatarSrc = otherParticipant?.user?.profilePicture || "/Avatar.png";
      } else if (chatType === "GROUP") {
        displayName = chat.name || "Group Chat";
        avatarSrc = "/groupavatar.svg";
      } else if (chatType === "AI") {
        displayName = chat.name || "AI Assistant";
        avatarSrc = "/chat/logo_als.png";
      } else {
        displayName = chat.name || "Chat";
        avatarSrc = "/Avatar.png";
      }

      const messageText = chat?.lastMessage?.message || "No messages yet";
      const messageSender = chat?.lastMessage?.senderName || "";

      const message = messageSender
        ? `${messageSender}: ${messageText}`
        : messageText;

      const timeToUse = chat.lastMessage?.createdAt;

      const time = timeToUse
        ? new Date(timeToUse).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })
        : "";

      return {
        chatId: chat.id,
        avatarSrc,
        displayName,
        message,
        time,
        chatType,
      };
    });

    const filteredChats = !searchQuery
      ? allChats
      : allChats.filter(
          (chat) =>
            chat.displayName
              .toLowerCase()
              .includes(searchQuery.toLowerCase()) ||
            chat.message.toLowerCase().includes(searchQuery.toLowerCase())
        );

    const pinned = filteredChats.filter((chat) => chat.chatType === "AI");
    const regular = filteredChats.filter((chat) => chat.chatType !== "AI");

    return {
      pinnedChats: pinned,
      regularChats: regular,
    };
  }, [combinedChats, userId, searchQuery]);

  const isLoading = isLoadingUser || isLoadingChats;
  const hasNoChats =
    !isLoading && pinnedChats.length === 0 && regularChats.length === 0;

  return (
    <div
      className="flex flex-col h-full
     bg-white overflow-hidden"
    >
      <div className="px-5 py-6">
        <div className="relative">
          <div className="flex items-center gap-3 w-full">
            <div className="relative flex-1">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <Image
                  src="/graysearch.svg"
                  alt="Search"
                  width={20}
                  height={20}
                  className="opacity-70"
                />
              </div>
              <Input
                placeholder="Search"
                className="pl-11 bg-[#F5F5F5] border-none rounded-[24px] h-12 text-[#737373] placeholder:text-[#A3A3A3] font-medium text-base focus-visible:ring-[#2D7DEE]/20 focus-visible:ring-offset-0"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="max-sm:hidden">
              <ChatActionsMenu />
            </div>
          </div>
        </div>
      </div>
      <div className="flex-1 overflow-y-auto overflow-x-hidden max-sm:pb-20 flex flex-col h-full">
        {isLoading ? (
          <div className="flex flex-col gap-2 px-5 py-2">
            {Array(10)
              .fill(0)
              .map((_, index) => (
                <ChatItemSkeleton key={index} />
              ))}
          </div>
        ) : hasNoChats ? (
          <div className="flex flex-col justify-center items-center h-full text-gray-400 gap-4 py-10">
            <Image
              src="/bubble-chat.svg"
              alt="No chats"
              width={64}
              height={64}
              className="opacity-30"
            />
            <p className="text-[#737373]">
              {searchQuery ? "No chats match your search" : "No chats found"}
            </p>
          </div>
        ) : (
          <>
            {/* Pinned Section */}
            {pinnedChats.length > 0 && (
              <div className="flex flex-col">
                <SectionLabel label="Pinned" />
                <div className="px-3 py-2">
                  {pinnedChats.map((chat) => (
                    <ChatItem key={chat.chatId} {...chat} />
                  ))}
                </div>
              </div>
            )}

            {/* Regular Chats Section */}
            {regularChats.length > 0 && (
              <div className="flex flex-col mt-2">
                <SectionLabel label="All Message" />
                <div className="px-3 py-2">
                  {regularChats.map((chat) => (
                    <ChatItem key={chat.chatId} {...chat} />
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
