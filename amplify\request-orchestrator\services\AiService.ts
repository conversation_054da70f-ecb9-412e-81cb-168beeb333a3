import { OpenAI } from "openai";
import type { ChatCompletionMessageParam } from "openai/resources/index.mjs";
import { StreamCallbacks } from "../types";

export interface CompletionOptions {
  onToken?: (token: string) => Promise<void>;
  onComplete?: (fullText: string) => Promise<void>;
  onError?: (error: Error) => Promise<void>;
}

export interface HealthCheckResult {
  isHealthy: boolean;
  details: {
    model?: string;
    object?: string;
    responseLength?: number;
    error?: string;
    type?: string;
    code?: string;
  };
}

export class AiService {
  private openai: OpenAI;
  apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.openai = new OpenAI({ apiKey });

    if (!apiKey || apiKey.trim() === "") {
      console.error("WARNING: No API key provided to AiService");
    } else {
      console.log(
        "AiService initialized with API key starting with:",
        apiKey.substring(0, 5) + "..."
      );
    }
  }

  async getChatCompletion(
    prompt: string,
    history: ChatCompletionMessageParam[]
  ): Promise<string> {
    try {
      console.log(
        "Getting chat completion for prompt:",
        prompt.substring(0, 50) + "..."
      );

      const messages: ChatCompletionMessageParam[] = [
        ...history,
        { role: "user", content: prompt },
      ];

      console.log(
        "Sending request to OpenAI with message count:",
        messages.length
      );

      const response = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
      });

      if (!response.choices || response.choices.length === 0) {
        throw new Error("No response from OpenAI");
      }

      const result = response.choices[0].message.content || "";
      console.log("Response content:", result.substring(0, 50) + "...");

      return result;
    } catch (error) {
      console.error("Error in getChatCompletion:", error);

      // Fallback response when OpenAI API fails
      console.log("Using fallback response due to API error");
      return this.getFallbackResponse(prompt);
    }
  }

  private getFallbackResponse(prompt: string): string {
    const fallbackResponses = [
      "I'm currently experiencing some technical difficulties with my AI service. However, I'm here to help you with your health questions. Could you please try asking your question again in a moment?",
      "Thank you for your message. I'm having trouble connecting to my AI service right now, but I want to assist you with your health concerns. Please try again shortly.",
      "I apologize, but I'm experiencing a temporary service interruption. As your AI health assistant, I'm committed to helping you. Please retry your question in a few moments.",
      "Hello! I'm your AI health assistant. I'm currently having some connectivity issues, but I'm here to support your health journey. Please try sending your message again.",
      "I appreciate your patience. There seems to be a temporary issue with my AI service. I'm designed to help with health-related questions and concerns. Please try again shortly."
    ];

    // Simple hash function to select a consistent response based on prompt
    let hash = 0;
    for (let i = 0; i < prompt.length; i++) {
      const char = prompt.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    const index = Math.abs(hash) % fallbackResponses.length;
    return fallbackResponses[index];
  }

  async getChatCompletionStream(
    prompt: string,
    history: ChatCompletionMessageParam[],
    callbacks: StreamCallbacks
  ): Promise<void> {
    try {
      console.log(
        "Getting streaming chat completion for prompt:",
        prompt.substring(0, 50) + "..."
      );

      const messages: ChatCompletionMessageParam[] = [
        ...history,
        { role: "user", content: prompt },
      ];

      console.log(
        "Sending streaming request to OpenAI with message count:",
        messages.length,
        "First message role:",
        messages[0]?.role
      );

      if (!this.apiKey || this.apiKey.trim() === "") {
        throw new Error("Missing API key for OpenAI");
      }

      console.log("Stream request configuration:", {
        model: "gpt-4o-mini",
        messageCount: messages.length,
        maxTokens: 1000,
        temperature: 0.7,
        stream: true,
      });

      const stream = await this.openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7,
        stream: true,
      });

      let fullText = "";
      let lastTokenTime = Date.now();
      let tokenCount = 0;

      console.log("Stream created successfully, awaiting tokens...");

      try {
        for await (const chunk of stream) {
          const now = Date.now();
          const delta = chunk.choices[0]?.delta;

          if (delta) {
            if (delta.role) {
              console.log(`Role change received: ${delta.role}`);
            }

            if (delta.content !== undefined && delta.content !== null) {
              const content = delta.content;
              console.log(`[AiService] Raw token received: "${content}"`);
              tokenCount++;

              const timeSinceLastToken = now - lastTokenTime;

              fullText += content;
              lastTokenTime = now;

              if (content) {
                try {
                  await callbacks.onToken(content);
                } catch (callbackError) {
                  console.error("[AiService] Error in onToken callback:", callbackError);
                }
              }
            }
          }
        }
      } catch (streamError) {
        console.error("Error processing stream:", streamError);

        await callbacks.onError(streamError as Error);
        return;
      }

      console.log(
        `Stream completed. Total tokens: ${tokenCount}, Final text length: ${fullText.length}`
      );

      await callbacks.onComplete(fullText);
    } catch (error: any) {
      console.error("Error in getChatCompletionStream:", error);
      if (callbacks && typeof callbacks.onError === 'function') {
        await callbacks.onError(error);
      } else {
        console.error("[AiService] onError callback is not available to report error.");
      }
    }
  }
}
