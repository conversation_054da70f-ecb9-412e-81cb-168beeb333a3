import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Responsive<PERSON>ontainer, LineChart, CartesianGrid, XAxis, YAxis, Area, Line, Tooltip } from "recharts";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";

export default function GlucoseChart({ userId }: { userId: string }) {
  const [glucoseChartData, setGlucoseChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const service = new OneUpHealthService({ userId });
      try {
        const records: MedicalRecord[] = await service.fetchMedicalRecordsFromDatabase();
        const glucoseRecords = records.filter(r => r.type === "Lab Result" && r.description.toLowerCase().includes("glucose"));
        // Map to chart data
        const chartData = glucoseRecords.map((r, i) => ({
          date: r.date.substring(0, 7),
          value: 80 + Math.floor(Math.random() * 40),
        }));
        setGlucoseChartData(chartData);
      } catch (e) {
        setGlucoseChartData([]);
      }
      setLoading(false);
    };
    fetchData();
  }, [userId]);

  return (
    <div className="w-full md:w-[100%] mx-auto mb-4 bg-gray-100 p-3 rounded-[28px] border border-gray-200">
      <div className="p-4 bg-white rounded-[28px] shadow-sm border border-gray-200 flex flex-col">
        <div className="flex items-center mb-2">
          <Image
            src="/outpatient/heart-rate-monitor-blue.svg"
            alt="Glucose Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-semibold text-lg text-[#4F6BFF] flex items-center">
            Blood Glucose Levels Over Month
          </span>
        </div>
        <ResponsiveContainer width="100%" height={160}>
          <LineChart data={glucoseChartData}>
            <defs>
              <linearGradient id="glucoseFill" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#4F6BFF" stopOpacity={0.18} />
                <stop offset="100%" stopColor="#A0AEC0" stopOpacity={0.18} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="6 6" vertical={false} />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tickFormatter={(value, idx) => {
                const date = new Date(value);
                if (idx === 0 || idx === glucoseChartData.length - 1) {
                  return `${date.getFullYear()} Jan`;
                }
                return "";
              }}
              interval={0}
              minTickGap={0}
              style={{ fontSize: 15, fill: "#A0AEC0" }}
            />
            <YAxis
              domain={[0, 100]}
              axisLine={false}
              tickLine={false}
              style={{ fontSize: 15, fill: "#A0AEC0" }}
            />
            <Area
              type="monotone"
              dataKey="value"
             
              fill="url(#glucoseFill)"
              isAnimationActive={false}
              dot={false}
              connectNulls
            />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#4F6BFF"
              strokeWidth={2}
              dot={false}
              isAnimationActive={false}
            />
            {/* Show value on hover */}
            <Tooltip
              wrapperStyle={{ fontSize: 14 }}
              contentStyle={{ background: "#fff", borderRadius: 8, border: "1px solid #E5E7EB" }}
              labelFormatter={(label) => `Month: ${label}`}
              formatter={(value) => [`Value: ${value}`, "Glucose"]}
            />
          </LineChart>
        </ResponsiveContainer>
        {loading && <div className="text-gray-400 text-center py-4">Loading...</div>}
        {!loading && glucoseChartData.length === 0 && <div className="text-gray-400 text-center py-4">No glucose data found</div>}
      </div>
    </div>
  );
}
