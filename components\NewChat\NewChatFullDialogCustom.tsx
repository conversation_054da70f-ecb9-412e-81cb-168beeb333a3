"use client";

import { Dialog } from "@/components/ui/dialog";
import { DialogContent } from "@radix-ui/react-dialog";
import { cn } from "@/lib/utils";
import NewChatList from "@/components/NewChat/NewChatList";
import { forwardRef } from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";

interface NewChatFullDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function NewChatFullDialog({
  isOpen,
  onClose,
}: NewChatFullDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent
        className={cn(
          "p-0 overflow-hidden bg-white",
          "fixed z-50 grid gap-4 bg-white",
          // Desktop styles
          "md:max-w-4xl md:min-h-[80vh] md:max-h-[90vh] md:rounded-lg md:left-[50%] md:top-[50%] md:translate-x-[-50%] md:translate-y-[-50%]",
          // Mobile styles - full screen
          "max-md:inset-0 max-md:w-full max-md:h-full max-md:max-w-none max-md:rounded-none max-md:translate-x-0 max-md:translate-y-0"
        )}
      >
        <NewChatList inDialog={true} onClose={onClose} />
      </DialogContent>
    </Dialog>
  );
}
