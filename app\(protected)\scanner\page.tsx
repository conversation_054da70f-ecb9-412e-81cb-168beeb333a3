"use client";

import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";

export default function ScannerPage() {
  const router = useRouter();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    async function setupCamera() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: "environment" },
          audio: false,
        });
        
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          setHasPermission(true);
        }
      } catch (err) {
        console.error("Error accessing camera:", err);
        setHasPermission(false);
      }
    }

    setupCamera();

    // Cleanup function to stop camera when component unmounts
    return () => {
      if (videoRef.current && videoRef.current.srcObject) {
        const stream = videoRef.current.srcObject as MediaStream;
        const tracks = stream.getTracks();
        tracks.forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="relative h-screen w-screen">
      {/* Camera view */}
      <div className="absolute inset-0 overflow-hidden">
        {hasPermission === false && (
          <div className="absolute inset-0 flex items-center justify-center bg-black text-white">
            Camera access denied. Please enable camera permissions.
          </div>
        )}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          className="min-w-full min-h-full object-cover h-screen w-screen"
        />
        
        {/* Scan animation line */}
        <div className="absolute left-0 right-0 h-1 bg-blue-500 top-1/3 animate-scan opacity-70" 
          style={{ boxShadow: '0 0 8px 2px rgba(59, 130, 246, 0.8)' }}
        />
      </div>
      
      {/* Close button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => router.back()}
        className="absolute top-4 left-4 text-white bg-black/20 rounded-full z-10"
      >
        <Image src="/chats/arrow-left.svg" alt="Back" width={24} height={24} />
      </Button>
      
      {/* Scan text */}
      <div className="absolute top-6 left-0 right-0 text-center z-10">
        <h2 className="text-white text-xl font-medium">Scan</h2>
      </div>

      {/* Bottom navigation - now overlaid on camera */}
      <div className="absolute bottom-10 left-0 right-0 flex justify-around items-center z-10">
        <Button 
          variant="ghost" 
          className="flex flex-col items-center bg-transparent p-0"
          onClick={() => router.push('/my-qr-code')}
        >
          <div className="bg-[#FFFFFF4D] flex items-center justify-center rounded-full border border-white/80" 
               style={{ width: '48px', height: '48px', minWidth: '48px', minHeight: '48px' }}>
            <div style={{ width: '24px', height: '24px' }} className="flex flex-col items-center justify-center">
              <div className="w-4 h-4 rounded-full border-2 border-white mb-0.5"></div>
              <div className="w-5 h-2.5 border-t-2 border-l-2 border-r-2 rounded-t-full border-white"></div>
            </div>
          </div>
          <span className="text-white text-sm mt-2 font-normal">My QR Code</span>
        </Button>
        
        <Button 
          variant="ghost" 
          className="flex flex-col items-center bg-transparent p-0"
        >
          <div className="flex items-center justify-center rounded-full border bg-[#FFFFFF4D] border-white/80" 
               style={{ width: '48px', height: '48px', minWidth: '48px', minHeight: '48px' }}>
            <Image src="/image.svg" alt="Photos" width={24} height={24} />
          </div>
          <span className="text-white text-sm mt-2 font-normal">Photos</span>
        </Button>
      </div>
    </div>
  );
}
