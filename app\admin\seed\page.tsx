"use client";

import { useState } from "react";
import {
  seedQuestionnaire,
  seedHealthProfileQuestionnaire,
} from "../../utils/seed";
import { Schema } from "@/amplify/data/resource";
import { generateClient } from "@aws-amplify/api";
import { Amplify } from "aws-amplify";
import { parseAmplifyConfig } from "aws-amplify/utils";

import outputs from "@/amplify_outputs.json";
const amplifyConfig = parseAmplifyConfig(outputs);
const client = generateClient<Schema>();
Amplify.configure({
  ...amplifyConfig,
  API: {
    ...amplifyConfig.API,
    Events: {
      endpoint:
        "https://jo6vp743zjf6xdt4b6u5amf434.appsync-api.us-east-1.amazonaws.com/event",
      region: "us-east-1",
      defaultAuthMode: "apiKey",
      apiKey: "da2-liw5uercxfccrhgzlqhxj3tu2u",
    },
  },
});

export default function SeedPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isProfileLoading, setIsProfileLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleSeed = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      await seedQuestionnaire();
      setResult({
        success: true,
        message: "Questionnaire data successfully seeded!",
      });
    } catch (error) {
      console.error("Error seeding data:", error);
      setResult({
        success: false,
        message: `Error seeding data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleProfileSeed = async () => {
    setIsProfileLoading(true);
    setResult(null);

    try {
      await seedHealthProfileQuestionnaire();
      setResult({
        success: true,
        message: "Health Profile questionnaire data successfully seeded!",
      });
    } catch (error) {
      console.error("Error seeding health profile data:", error);
      setResult({
        success: false,
        message: `Error seeding health profile data: ${
          error instanceof Error ? error.message : String(error)
        }`,
      });
    } finally {
      setIsProfileLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-4">Database Seed Utility</h1>
      <p className="mb-6 text-gray-600">
        This page allows you to populate the database with initial mock data for
        testing and development. Click the buttons below to create
        questionnaires.
      </p>

      <div className="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
        <h2 className="text-xl font-semibold mb-3">Questionnaire Data</h2>
        <p className="mb-4">
          This will create a mock questionnaire with health questions from the
          questionnaire messages.
        </p>
        <button
          onClick={handleSeed}
          disabled={isLoading}
          className={`px-4 py-2 rounded text-white font-medium ${
            isLoading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-700"
          }`}
        >
          {isLoading ? "Seeding..." : "Seed Questionnaire Data"}
        </button>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg shadow-sm mb-6">
        <h2 className="text-xl font-semibold mb-3">
          Health Profile Questionnaire
        </h2>
        <p className="mb-4">
          This will create a questionnaire with questions about user health
          profiles including age, gender, country, height, weight, and medical
          history.
        </p>
        <button
          onClick={handleProfileSeed}
          disabled={isProfileLoading}
          className={`px-4 py-2 rounded text-white font-medium ${
            isProfileLoading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-green-600 hover:bg-green-700"
          }`}
        >
          {isProfileLoading
            ? "Seeding..."
            : "Seed Health Profile Questionnaire"}
        </button>
      </div>

      {result && (
        <div
          className={`p-4 rounded-md ${
            result.success
              ? "bg-green-50 text-green-800"
              : "bg-red-50 text-red-800"
          }`}
        >
          <p className="font-medium">{result.message}</p>
        </div>
      )}

      <button
        onClick={() => client.mutations.syncHealthSystems()}
        className="mt-4 px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
      >
        Sync Health Systems
      </button>
      <div className="mt-8 text-sm text-gray-500">
        <p>Note: This utility is intended for development purposes only.</p>
      </div>
    </div>
  );
}
