// User identifiers
export const AI_ASSISTANT_ID = "AI_ASSISTANT";
export const ANONYMOUS_USER_ID = "anonymous";
export const MESSAGE_TYPE_SYSTEM = "SYSTEM";
// Chat metadata
export const DEFAULT_CHAT_NAME = "Health Assistant Chat";
export const DEFAULT_CHAT_DESCRIPTION = "Chat session with health assistant";
export const DEFAULT_CHAT_CATEGORY = "health";

// Message types
export const MESSAGE_TYPE_TEXT = "TEXT";

// Error messages
export const ERROR_USER_NOT_AUTHENTICATED =
  "Cannot send message: User not authenticated";
export const ERROR_MISSING_USER_ID = "Cannot create chat: User ID is missing";
export const ERROR_MISSING_CHAT_ID =
  "Cannot fetch messages: Chat ID is missing";
export const ERROR_FAILED_CREATE_CHAT = "Failed to create a new chat";
export const ERROR_FAILED_SEND_MESSAGE = "Failed to send message";
export const ERROR_FAILED_LOAD_MESSAGES = "Failed to load messages";
export const ERROR_FAILED_CHAT_INIT = "Failed to initialize chat";
export const ERROR_AUTH_FAILED = "Authentication failed. Please try again.";
