"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Amplify } from "aws-amplify";
import outputs from "@/amplify_outputs.json";
import Image from "next/image";

Amplify.configure(outputs);

export default function Home() {
  const router = useRouter();

  const navigateToLogin = () => {
    router.push("/login");
  };

  const navigateToRegister = () => {
    router.push("/register");
  };

  return (
    <main
      className="min-h-screen relative overflow-hidden"
      style={{
        backgroundImage: "url('/Background.png')",
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
    >
    
    <div className="absolute inset-0 flex items-center justify-center " style={{ bottom: "280px" }}>
      <Image
        src="/main1.png"
        alt="Health"
        width={368}
        height={368}
        className="object-contain rotate-[-19deg]"
        style={{
          maxWidth: "100%",
          height: "auto",
          filter: "drop-shadow(-30px 20px 30px rgba(28, 51, 128, 0.35))",
        }}
      />
    </div>

      <div className="relative z-10 px-6 py-8 flex flex-col min-h-screen">
        
        <header className="mb-auto">
        <div>
  
</div>

        </header>
      </div>
      
      {/* Bottom fixed container with white background */}
      <div className="fixed bottom-0 left-0 right-0 bg-white px-6 rounded-t-3xl py-8 z-20 m-[6px]">
        <div>
          <h1 className="text-[34px] font-medium leading-tight tracking-[-0.01em] text-black">
            Health Journal
          </h1>
          <div className="flex items-center">
            <span className="text-[28px] font-medium mr-1">to</span>
            <Image
              src="/image4.png"
              alt="JiniX"
              width={102}
              height={48}
            />
          </div>
        </div>

        <div className="space-y-4 mt-8">
          <button
            onClick={navigateToRegister}
            className="flex items-center justify-center w-full py-4 px-6 bg-blue-500 text-white font-medium rounded-xl h-[46px] hover:bg-blue-600 transition-colors"
          >
            Create an Account
          </button>
          <button
            onClick={navigateToLogin}
            className="flex items-center justify-center w-full py-4 px-6 bg-white text-gray-900 font-medium rounded-xl h-[46px] border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            Sign In
          </button>
        </div>

        <p className="text-center text-[14px] font-medium px-[69px] text-gray-600 mt-6">
          By proceeding, you agree to our{" "}
          <Link href="#" className="text-blue-500">
            terms & policy
          </Link>{" "}
          and{" "}
          <Link href="#" className="text-blue-500">
            privacy policy
          </Link>
        </p>
      </div>
    </main>
  );
}