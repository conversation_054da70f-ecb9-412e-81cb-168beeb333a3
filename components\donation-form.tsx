"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function DonationForm() {
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [customAmount, setCustomAmount] = useState("")
  const [total, setTotal] = useState(0)

  const predefinedAmounts = [100, 50, 25, 10]

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setCustomAmount("")
    setTotal(amount)
  }

  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setCustomAmount(value)
    setSelectedAmount(null)
    setTotal(value ? Number.parseFloat(value) : 0)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    alert(`Processing donation of $${total.toFixed(2)} from ${firstName} ${lastName}`)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-12 pt-[84px]">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          <Input id="firstName" value={firstName} onChange={(e) => setFirstName(e.target.value)} required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input id="lastName" value={lastName} onChange={(e) => setLastName(e.target.value)} required />
        </div>
      </div>

      <div className="space-y-2">
        <Label>Amount</Label>
        <div className="flex items-center bg-gray-100 rounded-full p-1">
          {predefinedAmounts.map((amount) => (
            <Button
              key={amount}
              type="button"
              variant="ghost"
              onClick={() => handleAmountSelect(amount)}
              className={`flex-1 h-10 rounded-full hover:bg-orange-400 hover:text-white ${
                selectedAmount === amount ? "bg-orange-400 text-white" : "text-gray-600"
              }`}
            >
              ${amount}
            </Button>
          ))}
          <Button
            type="button"
            variant="ghost"
            onClick={() => setSelectedAmount(null)} 
            className={`h-10 rounded-full px-4 ${
              selectedAmount === null
                ? "bg-orange-400 text-white "
                : "text-gray-600 hover:bg-orange-400 hover:text-white"
            }`}
          >
            Other
          </Button>
        </div>
        <div className="">
          <Label htmlFor="customAmount">Other Amount</Label>
          <Input
            id="customAmount"
            placeholder="Donate Amount"
            value={customAmount}
            onChange={(e) => {
              const value = e.target.value;
              
              if (value === '' || /^\d*\.?\d*$/.test(value)) {
                handleCustomAmountChange(e);
              }
            }}
            className="h-[46px] rounded-lg mb-12 mt-2"
          />
        </div>
      </div>

      <div className="flex items-center justify-between border-t pt-12 pb-6">
        <Label className="text-base font-medium">Total</Label>
        <span className="text-xl font-bold">${total.toFixed(2)}</span>
      </div>

      <Button type="submit" className="w-full bg-orange-400 hover:bg-orange-500 text-black font-bold px-4  rounded-full h-[40px]">
        Donate Now
      </Button>
    </form>
  )
}
