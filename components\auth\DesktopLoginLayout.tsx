import Image from "next/image"
import { LoginForm } from "./LoginForm"
import { SocialLoginButtons } from "./SocialLoginButtons"
import Link from "next/link"

type Props = {
  loading: boolean
  errorMessage?: string
  onLogin: (data: any) => void
  onGoogleLogin: () => void
  onFacebookLogin: () => void
}

export function DesktopLoginLayout({ loading, errorMessage, onLogin, onGoogleLogin, onFacebookLogin }: Props) {
  return (
    <>
      <div className="hidden md:flex w-full md:w-1/2 flex-col justify-start h-screen px-8 lg:px-16 xl:px-28 2xl:px-[128px] py-8 lg:py-12 bg-white overflow-hidden">
        <p className="text-[#FFB300] text-lg font-medium mb-2">jinIX</p>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Health journal</h1>
        <h1 className="text-4xl font-bold text-gray-900 mb-8">from jiniX to jinIX</h1>

        <LoginForm loading={loading} errorMessage={errorMessage} onSubmit={onLogin} />

        <p className="text-center text-sm text-gray-600 mt-4">
          Don’t have an account?{" "}
          <Link href="/register" className="text-blue-500 hover:underline">
            Sign Up
          </Link>
        </p>

        <div className="flex items-center my-6">
          <div className="flex-1 h-px bg-gray-300"></div>
          <p className="mx-4 text-gray-600 text-sm">Or</p>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>

        <SocialLoginButtons loading={loading} onGoogleLogin={onGoogleLogin} onFacebookLogin={onFacebookLogin} />
      </div>
      
      <div className="hidden md:flex w-full md:w-1/2 h-screen items-center justify-center p-6 md:p-12 bg-gradient-to-br from-blue-50 to-blue-100 rounded-l-3xl">
        <div className="relative">
          <Image src="/6.png" alt="Abstract shape" width={400} height={400} className="object-contain" />
        </div>
      </div>
    </>
  )
}
