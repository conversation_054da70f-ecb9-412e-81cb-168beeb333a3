"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { Chat } from "@/types/chat";
import { useChats } from "@/hooks/chat/useChats";
import { useUser } from "@/hooks/useUsers";

type AllChatsContextType = {
  chats: Chat[];
  setChats: React.Dispatch<React.SetStateAction<Chat[]>>;
  removeChat: (chatId: string) => void;
  addChat: (chat: Chat) => void;
  isLoading: boolean;
};

const AllChatsContext = createContext<AllChatsContextType | undefined>(
  undefined
);

export function AllChatsProvider({ children }: { children: React.ReactNode }) {
  const [chats, setChats] = useState<Chat[]>([]);
  const { currentUser } = useUser();
  const userId = currentUser?.userId || null;
  const { userChats, isLoading } = useChats(userId);

  useEffect(() => {
    if (userChats) {
      setChats(userChats);
    }
  }, [userChats]);

  const removeChat = (chatId: string) => {
    setChats((currentChats) =>
      currentChats.filter((chat) => chat.id !== chatId)
    );
  };

  const addChat = (chat: Chat) => {
    setChats((currentChats) => {
      if (currentChats.some((c) => c.id === chat.id)) {
        return currentChats;
      }
      return [...currentChats, chat];
    });
  };

  return (
    <AllChatsContext.Provider
      value={{ chats, setChats, removeChat, addChat, isLoading }}
    >
      {children}
    </AllChatsContext.Provider>
  );
}

export function useAllChats() {
  const context = useContext(AllChatsContext);
  if (context === undefined) {
    throw new Error("useAllChats must be used within an AllChatsProvider");
  }
  return context;
}
