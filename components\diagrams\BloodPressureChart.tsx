import Image from "next/image";
import React, { useEffect, useState } from "react";
import { <PERSON>sponsive<PERSON><PERSON>r, LineChart, CartesianGrid, XAxis, YAxis, Tooltip, Line, Area } from "recharts";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";

export default function BloodPressureChart({ userId }: { userId: string }) {
  const [bpChartData, setBpChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const service = new OneUpHealthService({ userId });
      try {
        const records: MedicalRecord[] = await service.fetchMedicalRecordsFromDatabase();
        const bpRecords = records.filter(r => r.type === "Lab Result" && r.description.toLowerCase().includes("blood pressure"));
        // Map to chart data (simulate systolic/diastolic)
        const chartData = bpRecords.map((r, i) => ({
          date: r.date.substring(0, 7),
          systolic: 120 + Math.floor(Math.random() * 20),
          diastolic: 80 + Math.floor(Math.random() * 15),
        }));
        setBpChartData(chartData);
      } catch (e) {
        setBpChartData([]);
      }
      setLoading(false);
    };
    fetchData();
  }, [userId]);

  return (
    <div className="w-full md:w-[100%] mx-auto mb-4 bg-gray-100 p-3 rounded-[28px] border border-gray-200">
      <div className="p-4 bg-white rounded-[28px] shadow-sm border border-gray-200 flex flex-col">
        <div className="flex items-center mb-2">
          <Image
            src="/outpatient/heart-rate-monitor.svg"
            alt="BP Chart Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-semibold text-lg text-[#FF899E]">
            Systolic & Diastolic Blood Pressure
          </span>
        </div>
        <div className="flex items-center gap-4 mb-2 ml-1">
          <div className="flex items-center gap-1">
            <span className="inline-block w-4 h-4 rounded bg-[#4285F4] opacity-80"></span>
            <span className="text-[#4285F4] font-medium text-sm">Systolic</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="inline-block w-4 h-4 rounded bg-[#FF9EB3] opacity-80"></span>
            <span className="text-[#FF9EB3] font-medium text-sm">Diastolic</span>
          </div>
        </div>
        <ResponsiveContainer width="100%" height={264}>
          <LineChart data={bpChartData}>
            <defs>
              <linearGradient id="systolicFill" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#4285F4" stopOpacity={0.18} />
                <stop offset="100%" stopColor="#4285F4" stopOpacity={0.18} />
              </linearGradient>
              <linearGradient id="diastolicFill" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#FF9EB3" stopOpacity={0.18} />
                <stop offset="100%" stopColor="#FF9EB3" stopOpacity={0.18} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="6 6" vertical={false} />
            <Area
              type="monotone"
              dataKey="systolic"
              fill="url(#systolicFill)"
              isAnimationActive={false}
              dot={false}
              connectNulls
              yAxisId="left"
              activeDot={false}
            />
            <Area
              type="monotone"
              dataKey="diastolic"
              fill="url(#diastolicFill)"
              isAnimationActive={false}
              dot={false}
              connectNulls
              yAxisId="right"
              activeDot={false}
            />
            <Line
              type="monotone"
              dataKey="systolic"
              stroke="#4285F4"
              strokeWidth={2}
              dot={false}
              isAnimationActive={false}
              yAxisId="left"
            />
            <Line
              type="monotone"
              dataKey="diastolic"
              stroke="#FF9EB3"
              strokeWidth={2}
              dot={false}
              isAnimationActive={false}
              yAxisId="right"
            />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tickFormatter={(value, idx) => {
                const date = new Date(value);
                if (idx === 0 || idx === bpChartData.length - 1) {
                  return `${date.getFullYear()} Jan`;
                }
                return "";
              }}
              interval={0}
              minTickGap={0}
            />
            <YAxis
              yAxisId="left"
              domain={[60, 150]}
              axisLine={false}
              tickLine={false}
              ticks={[60, 80, 100, 120, 140, 150]}
            />
            <YAxis yAxisId="right" orientation="right" hide domain={[60, 150]} />
            {/* <Tooltip /> */}
          </LineChart>
        </ResponsiveContainer>
        {loading && <div className="text-gray-400 text-center py-4">Loading...</div>}
        {!loading && bpChartData.length === 0 && <div className="text-gray-400 text-center py-4">No blood pressure data found</div>}
      </div>
    </div>
  );
}
