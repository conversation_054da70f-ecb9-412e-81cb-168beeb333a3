"use client";
import { useState, type FormEvent, useRef, useEffect } from "react";
import type React from "react";

import { Send, Loader2, Mic } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import Image from "next/image";
import { useVoiceVisualizer, VoiceVisualizer } from "react-voice-visualizer";

// Add TypeScript definitions for SpeechRecognition
interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
  error: any;
}

interface SpeechRecognitionResultList {
  length: number;
  item(index: number): SpeechRecognitionResult;
  [index: number]: SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  length: number;
  item(index: number): SpeechRecognitionAlternative;
  [index: number]: SpeechRecognitionAlternative;
  isFinal?: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  start(): void;
  stop(): void;
  abort(): void;
  onresult: (event: SpeechRecognitionEvent) => void;
  onerror: (event: SpeechRecognitionEvent) => void;
  onend: (event: Event) => void;
  onstart: (event: Event) => void;
  onspeechend: (event: Event) => void;
  onnomatch: (event: Event) => void;
  onaudiostart: (event: Event) => void;
  onaudioend: (event: Event) => void;
  onsoundstart: (event: Event) => void;
  onsoundend: (event: Event) => void;
  onspeechstart: (event: Event) => void;
}

interface SpeechRecognitionConstructor {
  new (): SpeechRecognition;
}

declare global {
  interface Window {
    SpeechRecognition?: SpeechRecognitionConstructor;
    webkitSpeechRecognition?: SpeechRecognitionConstructor;
  }
}

interface MessageInputProps {
  onSendMessage: (
    message: string,
    connectionId?: string,
    endpoint?: string
  ) => Promise<void>;
  isDisabled?: boolean;
  isLoading?: boolean;
  connectionId?: string;
  endpoint?: string;
  handleMicClick?: (e: React.MouseEvent) => void;
}

export default function MessageInput({
  onSendMessage,
  isDisabled = false,
  isLoading = false,
  connectionId,
  endpoint,
  handleMicClick,
}: MessageInputProps) {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isVisualizing, setIsVisualizing] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [internalTranscript, setInternalTranscript] = useState("");

  const [isFocused, setIsFocused] = useState(false);
  const MAX_LENGTH = 1000;
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  const recorderControls = useVoiceVisualizer();
  const {
    recordedBlob,
    startRecording,
    stopRecording,
    togglePauseResume,
    error,
    audioRef,
  } = recorderControls;

  const isRecordingInProgress = isVisualizing;

  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      ("SpeechRecognition" in window || "webkitSpeechRecognition" in window)
    ) {
      const SpeechRecognitionConstructor =
        window.SpeechRecognition || window.webkitSpeechRecognition;

      if (SpeechRecognitionConstructor) {
        recognitionRef.current = new SpeechRecognitionConstructor();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;

        recognitionRef.current.onresult = (event) => {
          let currentTranscript = "";
          for (let i = 0; i < event.results.length; i++) {
            currentTranscript += event.results[i][0].transcript;
          }

          setInternalTranscript(currentTranscript);
        };

        recognitionRef.current.onerror = (event) => {
          console.error("Speech recognition error:", event.error);
        };
      }
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (!recordedBlob) return;

    if (internalTranscript.trim()) {
      setMessage(internalTranscript);
      setInternalTranscript("");
      setTranscript("");
    }

    setIsVisualizing(false);
  }, [recordedBlob, internalTranscript]);

  useEffect(() => {
    if (!error) return;
    console.error("Voice recorder error:", error);
    setIsVisualizing(false);

    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
  }, [error]);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIfMobile();

    window.addEventListener("resize", checkIfMobile);

    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  useEffect(() => {
    const resizeTextarea = () => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      textarea.style.height = "auto";

      const newHeight = Math.max(
        64,
        Math.min(textarea.scrollHeight, isMobile ? 120 : 200)
      );

      textarea.style.height = `${newHeight}px`;
    };

    resizeTextarea();

    window.addEventListener("resize", resizeTextarea);
    return () => window.removeEventListener("resize", resizeTextarea);
  }, [message, isMobile]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isDisabled || isLoading || isSending) return;

    try {
      setIsSending(true);
      const messageToSend = message;
      setMessage("");
      await onSendMessage(messageToSend, connectionId, endpoint);

      setTimeout(() => {
        textareaRef.current?.focus();
      }, 10);
    } catch (error) {
      console.error("Error sending message:", error);

      setMessage(message);
    } finally {
      setIsSending(false);
    }
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    if (newMessage.length <= MAX_LENGTH) {
      setMessage(newMessage);
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleLocalMicClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isVisualizing) {
      stopRecording();
      setIsVisualizing(false);

      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    } else {
      setIsVisualizing(true);
      startRecording();

      if (recognitionRef.current) {
        setTranscript("");
        setInternalTranscript("");
        recognitionRef.current.start();
      }
    }

    if (handleMicClick) {
      handleMicClick(e);
    }
  };

  const handleSubmitTranscript = (e: React.MouseEvent) => {
    e.preventDefault();
    if (!internalTranscript.trim()) return;

    stopRecording();
    setIsVisualizing(false);

    if (recognitionRef.current) {
      recognitionRef.current.stop();

      setMessage(internalTranscript);

      setInternalTranscript("");
      setTranscript("");

      setTimeout(() => {
        textareaRef.current?.focus();
      }, 10);
    }
  };

  const isProcessing = isLoading || isSending;

  const inputPlaceholder = isRecordingInProgress
    ? "Listening..."
    : isProcessing
      ? "Sending message..."
      : "Send a message";

  return (
    <div
      className="w-full p-3 bg-gray-100 rounded-[24px]  shadow-[0px_1px_1px_0px_rgba(0,0,0,0.04)]"
      ref={containerRef}
    >
      <div className="flex flex-col gap-3">
        <form onSubmit={handleSubmit} className="w-full">
          <Textarea
            ref={textareaRef}
            placeholder={inputPlaceholder}
            className="w-full px-4 py-2 focus:ring-0 focus:border-none focus:outline-0 outline-none text-gray-800 placeholder-gray-400 border-none shadow-none rounded-[16px] focus:outline-none focus:ring-blue-500"
            style={{
              minHeight: "64px",
              maxHeight: isMobile ? "120px" : "200px",
              resize: "none",
              overflow: "auto",
            }}
            value={isVisualizing ? internalTranscript : message}
            onChange={handleMessageChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={isDisabled || isProcessing || isRecordingInProgress}
            maxLength={MAX_LENGTH}
            rows={1}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSubmit(e);
              }
            }}
          />
        </form>

        <div className="grid grid-cols-[auto_1fr_auto] gap-2 w-full">
          <button
            type="button"
            onClick={handleLocalMicClick}
            className={`p-3 bg-white rounded-[16px] focus:ring-0 outline-none border-none
             shadow-[0px_3px_6px_0px_rgba(66,74,83,0.12)]
             transition-all duration-200 hover:bg-gray-50 focus:outline-none focus:ring-blue-500 focus:ring-offset-2`}
            aria-label={isVisualizing ? "Stop recording" : "Voice input"}
          >
            {isVisualizing ? (
              <Image
                src="/common/x.svg"
                alt="Cancel recording"
                width={20}
                height={20}
              />
            ) : (
              <Mic size={20} />
            )}
          </button>

          <div className="rounded-[16px] w-full px-3 flex justify-center items-center min-h-[48px]">
            {isVisualizing && (
              <div className="w-full transform rotate-180">
                <VoiceVisualizer
                  canvasContainerClassName="w-full"
                  controls={recorderControls}
                  height={40}
                  mainBarColor="#2570EB"
                  secondaryBarColor="#2570EB"
                  backgroundColor="transparent"
                  barWidth={4}
                  gap={1}
                  isControlPanelShown={false}
                  isDefaultUIShown={false}
                  isProgressIndicatorShown={true}
                  isProgressIndicatorTimeShown={false}
                  isProgressIndicatorOnHoverShown={false}
                  isProgressIndicatorTimeOnHoverShown={false}
                  isAudioProcessingTextShown={false}
                  onlyRecording={true}
                  fullscreen={false}
                  animateCurrentPick={true}
                  rounded={4}
                  speed={1}
                />
              </div>
            )}
          </div>

          <button
            onClick={isVisualizing ? handleSubmitTranscript : handleSubmit}
            className={`p-3 bg-blue-500 text-white rounded-[16px] shadow-[0px_3px_6px_0px_rgba(66,74,83,0.12)] transition-all duration-200 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
              (!message.trim() && !isVisualizing) ||
              (isVisualizing && !internalTranscript.trim()) ||
              isDisabled ||
              isProcessing
                ? "opacity-50 cursor-not-allowed"
                : ""
            }`}
            disabled={
              (!message.trim() && !isVisualizing) ||
              (isVisualizing && !internalTranscript.trim()) ||
              isDisabled ||
              isProcessing
            }
            aria-label={isVisualizing ? "Accept transcript" : "Send message"}
          >
            {isProcessing ? (
              <Loader2 size={20} className="animate-spin" />
            ) : isVisualizing ? (
              <Image
                src="/common/check.svg"
                alt="Accept"
                width={24}
                height={24}
                className="text-white"
              />
            ) : (
              <Image
                src="/chat/arrow-up.svg"
                alt="Send"
                width={24}
                height={24}
                className="text-white"
              />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
