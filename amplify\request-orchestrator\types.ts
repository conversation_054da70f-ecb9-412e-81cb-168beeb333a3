import { AppSyncResolverEvent } from "aws-lambda";

export type AppSyncEvent<TArgs = any> = AppSyncResolverEvent<TArgs>;

export interface AppSyncEventConfig {
  channelName: string;
  sessionId: string;
  namespace?: string;
}

export interface StreamingConfig {
  type: "appsync";
  connection: AppSyncEventConfig | unknown;
  maxTokens?: number;
  temperature?: number;
}

export interface Message {
  id: string;
  role: "system" | "user" | "assistant";
  content: string;
}

export interface StreamCallbacks {
  onToken: (token: string) => Promise<void>;
  onComplete: (fullText: string) => Promise<void>;
  onError: (error: Error) => Promise<void>;
}

export interface HandlerEvent {
  fieldName: string;
  arguments: any;
  identity?: IdentityInfo;
  [key: string]: any;
}

export interface IdentityInfo {
  sub: string;
  [key: string]: any;
}

export interface LambdaContext {
  getRemainingTimeInMillis: () => number;
  [key: string]: any;
}

export interface GetChatHistoryArgs {
  chatId: string;
  limit?: number;
  nextToken?: string;
}

export interface SendMessageArgs {
  chatId: string;
  userId: string;
  message: string;
  messageType?: "TEXT" | "IMAGE" | "FILE" | "SYSTEM" | "AUDIO" | "VIDEO";
  attachments?: string[];
}

export interface StreamMessageArgs {
  chatId: string;
  userId: string;
  message: string;
  eventConfig?: AppSyncEventConfig;
}

export interface ChatMessageResponse {
  id: string;
  chatId: string;
  userId: string;
  message: string;
  messageType: string;
  attachments?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateChatWithQuestionnaireArgs {
  userId: string;
  questionnaireId: string;
  name: string;
  description?: string;
  metadata: {
    createdBy: string;
    isArchived?: boolean;
    category?: string;
    configuration?: any;
  };
}
