import axios from "axios";
import { AxiosError } from "axios";
import { Amplify } from "@aws-amplify/core";
import { generateClient } from "@aws-amplify/api";
import { Schema } from "../../data/resource";

import { getAmplifyDataClientConfig } from "@aws-amplify/backend/function/runtime";
import { env } from "$amplify/env/oneup-health-connect";

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);
const client = generateClient<Schema>();

interface HandlerEvent {
  fieldName?: string;
  info?: {
    fieldName?: string;
  };
  request?: {
    userAttributes?: {
      sub?: string;
      email?: string;
    };
  };
  identity?: {
    sub?: string;
  };
  arguments?: {
    code?: string;
    appUserId?: string;
    accessToken?: string;
    systemId?: string;
    provider?: string;
    endpoint?: string;
    patientId?: string;
    refreshToken?: string;
  };
}

interface ResponseType {
  success: boolean;
  error?: string | null;
  data?: {
    access_token?: string;
    refresh_token?: string;
    expires_in?: number;
    scope?: string;
    token_type?: string;
    [key: string]: unknown;
  };
}

enum Endpoint {
  PATIENT = "Patient",
  CONDITION = "Condition",
  OBSERVATION = "Observation",
  MEDICATION = "MedicationRequest",
  PROCEDURE = "Procedure",
  ALLERGY = "AllergyIntolerance",
  ENCOUNTER = "Encounter",
}

export const handler = async (event: HandlerEvent): Promise<ResponseType> => {
  console.log("Raw event:", JSON.stringify(event, null, 2));

  const fieldName = event.fieldName || event.info?.fieldName;

  // Extract userId securely from request attributes
  const userId = event.identity?.sub;
  if (!userId) {
    return {
      success: false,
      error: "User authentication required - no user ID found",
      data: undefined,
    };
  }

  const {
    code,
    appUserId,
    accessToken,
    systemId,
    provider,
    endpoint,
    patientId,
    refreshToken,
  } = event.arguments || {};

  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    return {
      success: false,
      error: "OneUp Health credentials not configured",
      data: undefined,
    };
  }

  try {
    switch (fieldName) {
      case "createOneUpHealthUser":
        return await createUser(appUserId || `jinix_${userId}`);

      case "getOneUpHealthAuthCode":
        return await getAuthCode(appUserId || `jinix_${userId}`);

      case "getOneUpHealthAuthUrl":
        return await getAuthUrl(userId, systemId || "8364847");

      case "handleOneUpHealthCallback":
        return await handleCallback(userId, provider);

      case "refreshOneUpHealthToken":
        if (!refreshToken) {
          return {
            success: false,
            error: "Refresh token is required",
            data: undefined,
          };
        }
        return await refreshAccessToken(refreshToken, userId, provider);

      case "getUserOneUpHealthConnections":
        return await getUserConnections(appUserId || `jinix_${userId}`);

      case "fetchOneUpHealthFhirData":
        const validToken = provider
          ? await getValidAccessToken(userId, provider)
          : accessToken;
        if (!validToken) {
          return {
            success: false,
            error: "Unable to obtain a valid access token",
            data: undefined,
          };
        }
        return await fetchData(
          validToken,
          (endpoint as Endpoint) || Endpoint.PATIENT,
          patientId
        );

      case "refreshOneUpHealthUserToken":
        if (!provider) {
          return {
            success: false,
            error: "Provider is required for user token refresh",
            data: undefined,
          };
        }
        return await refreshUserToken(userId, provider);

      default:
        return {
          success: false,
          error: `Unsupported field: ${fieldName}`,
          data: undefined,
        };
    }
  } catch (error: unknown) {
    console.error("Error processing request:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Internal server error",
      data: undefined,
    };
  }
};

async function createUser(appUserId?: string): Promise<ResponseType> {
  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error("OneUpHealth credentials not configured");
  }

  const app_user_id = appUserId || `jinix_${Date.now()}`;

  try {
    console.log(`Creating user with app_user_id: ${app_user_id}`);
    const response = await axios.post(
      `https://api.1up.health/user-management/v1/user?app_user_id=${app_user_id}`,
      {},
      {
        headers: {
          client_id: clientId,
          client_secret: clientSecret,
        },
      }
    );

    console.log("User creation successful:", response.status, response.data);
    return {
      success: true,
      data: {
        id: response.data.id,
        access_token: response.data.access_token,
        app_user_id: app_user_id,
      },
      error: null,
    };
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    const responseData = axiosError?.response?.data as any;
    const statusCode = axiosError?.response?.status;

    console.error("Error creating 1upHealth user:", {
      status: statusCode,
      data: responseData,
      message: axiosError?.message,
    });

    if (
      statusCode === 409 ||
      (statusCode === 400 && responseData?.error === "this user already exists")
    ) {
      console.log("User already exists, retrieving auth code...");
      const authCodeResponse = await getAuthCode(app_user_id);
      if (authCodeResponse.success && authCodeResponse.data?.code) {
        console.log("Got auth code, exchanging for token...");
        const tokenResponse = await axios.post(
          "https://auth.1up.health/oauth2/token",
          new URLSearchParams({
            grant_type: "authorization_code",
            code: String(authCodeResponse.data.code),
            client_id: clientId,
            client_secret: clientSecret,
          }),
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        console.log("Token exchange successful");
        return {
          success: true,
          data: {
            access_token: tokenResponse.data.access_token,
            app_user_id: app_user_id,
            message: "User already exists, retrieved token",
          },
          error: null,
        };
      }
      return {
        success: true,
        data: {
          message: "User already exists",
          app_user_id: app_user_id,
        },
        error: null,
      };
    }
    throw new Error(
      `Failed to create 1upHealth user: ${JSON.stringify(responseData) || axiosError?.message}`
    );
  }
}

async function getAuthCode(appUserId: string): Promise<ResponseType> {
  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret || !appUserId) {
    throw new Error("Required parameters are missing");
  }

  try {
    console.log(`Getting auth code for app_user_id: ${appUserId}`);
    const response = await axios.post(
      `https://api.1up.health/user-management/v1/user/auth-code?app_user_id=${appUserId}&client_id=${clientId}&client_secret=${clientSecret}`,
      {}
    );

    console.log("Auth code response:", response.status, response.data);
    return {
      success: true,
      data: {
        code: response.data.code,
      },
      error: null,
    };
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error getting auth code:", {
      status: axiosError?.response?.status,
      data: axiosError?.response?.data,
      message: axiosError?.message,
    });
    throw new Error(
      `Failed to get auth code: ${axiosError?.response?.data || axiosError?.message}`
    );
  }
}

async function getAuthUrl(
  userId: string,
  systemId: string = "8364847"
): Promise<ResponseType> {
  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error("OneUpHealth credentials not configured");
  }

  try {
    console.log(`Getting auth URL for systemId: ${systemId}`);
    const appUserId = `jinix_${userId}`;
    const userResponse = await createUser(appUserId);

    if (
      userResponse.success &&
      !userResponse.data?.access_token &&
      userResponse.data?.app_user_id
    ) {
      console.log("User exists but no token provided, getting auth code...");
      const authCodeResponse = await getAuthCode(appUserId);
      if (!authCodeResponse.success || !authCodeResponse.data?.code) {
        throw new Error("Failed to get auth code for existing user");
      }
      const tokenResponse = await axios.post(
        "https://auth.1up.health/oauth2/token",
        new URLSearchParams({
          grant_type: "authorization_code",
          code: String(authCodeResponse.data.code),
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );
      userResponse.data.access_token = tokenResponse.data.access_token;
    }

    if (!userResponse.success || !userResponse.data?.access_token) {
      throw new Error(
        `Failed to create or retrieve user: ${userResponse.error || "No access token returned"}`
      );
    }

    const accessToken = userResponse.data.access_token;
    console.log("Successfully obtained access token for user");

    const appDomain = process.env.APP_DOMAIN || "http://localhost:3000";
    const redirectUri = `${appDomain}/medical-record/success`;

    console.log(
      `Requesting auth URL for system ${systemId} with redirect to ${redirectUri}`
    );
    const response = await axios.get(
      `https://system-search.1up.health/api/clinical/${systemId}`,
      {
        params: {
          sendRedirectUrlAsResponse: true,
          redirect_uri: redirectUri,
        },
        headers: {
          "x-client-id": clientId,
          Authorization: `Bearer ${accessToken}`,
          Accept: "application/json",
        },
      }
    );

    console.log("Auth URL response:", JSON.stringify(response.data, null, 2));
    const authUrl =
      response.data.authorization_url || response.data.redirect_uri;
    console.log("Successfully obtained authorization URL:", authUrl);

    return {
      success: true,
      data: {
        authorization_url: authUrl,
      },
      error: null,
    };
  } catch (error: unknown) {
    console.error(
      "Error getting auth URL:",
      error instanceof AxiosError
        ? {
            status: error.response?.status,
            data: error.response?.data,
            message: error.message,
          }
        : error
    );
    throw new Error(
      `Failed to get auth URL: ${
        error instanceof AxiosError
          ? JSON.stringify(error.response?.data) || error.message
          : error instanceof Error
            ? error.message
            : "Unknown error"
      }`
    );
  }
}

async function handleCallback(
  userId?: string,
  provider?: string
): Promise<ResponseType> {
  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret || !userId || !provider) {
    throw new Error("Required parameters are missing");
  }

  const appUserId = `jinix_${userId}`;
  const authCodeResponse = await getAuthCode(appUserId);
  if (!authCodeResponse.success || !authCodeResponse.data?.code) {
    throw new Error("Failed to get auth code for existing user");
  }
  const code = authCodeResponse.data.code as string;

  try {
    console.log("Exchanging code for tokens...");
    const tokenResponse = await axios.post(
      "https://auth.1up.health/oauth2/token",
      new URLSearchParams({
        grant_type: "authorization_code",
        code: code,
        client_id: clientId,
        client_secret: clientSecret,
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    const tokenData = tokenResponse.data;
    console.log("Token exchange successful:", tokenResponse.status);

    const now = new Date();

    // Create or update HealthDataConnection record
    await client.models.HealthDataConnection.create({
      appUserId,
      userId,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token,
      code: code || null, // Ensure code is nullable
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
    });

    // Create UserConnectedSystem record
    await client.models.UserConnectedSystem.create({
      userId,
      systemId: provider,

      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
    });

    console.log(
      `Tokens stored for appUserId: ${appUserId}, systemId: ${provider}`
    );

    // Update user connection status (assuming you have a field for this in User model)
    try {
      const updateUserResponse = await axios.post(
        process.env.API_URL || "https://api.example.com/graphql",
        {
          query: `
            mutation UpdateUser($input: UpdateUserInput!) {
              updateUser(input: $input) {
                userId
                is_1healthup_connected
              }
            }
          `,
          variables: {
            input: {
              userId: userId,
              is_1healthup_connected: true,
            },
          },
        },
        {
          headers: {
            "x-api-key": process.env.API_KEY || "",
            "Content-Type": "application/json",
          },
        }
      );
      console.log("User updated:", updateUserResponse.data);
    } catch (updateErr) {
      console.error("Error updating user status:", updateErr);
    }

    return {
      success: true,
      data: {
        message: "Callback handled successfully",
        appUserId,
        systemId: provider,
      },
      error: null,
    };
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error handling callback:", axiosError?.response?.data);
    throw new Error(
      `Failed to handle callback: ${axiosError?.response?.data || axiosError?.message}`
    );
  }
}

async function getUserConnections(appUserId: string): Promise<ResponseType> {
  try {
    // Get HealthDataConnection records
    const connections =
      await client.models.HealthDataConnection.listHealthDataConnectionsByUser({
        userId: appUserId.replace("jinix_", ""),
      });

    return {
      success: true,
      data: {
        connections: connections.data.map((conn) => ({
          appUserId: conn.appUserId,
          userId: conn.userId,
          createdAt: conn.createdAt,
        })),
      },
      error: null,
    };
  } catch (error: unknown) {
    console.error("Error getting user connections:", error);
    throw new Error("Failed to get user connections");
  }
}

async function fetchData(
  accessToken: string,
  endpoint: Endpoint,
  patientId?: string
): Promise<ResponseType> {
  try {
    let url: string;
    if (endpoint === Endpoint.PATIENT && patientId) {
      url = `https://api.1up.health/r4/Patient/${patientId}/$everything`;
    } else {
      url = `https://api.1up.health/r4/${endpoint}`;
    }

    console.log(`Fetching data from: ${url}`);
    const dataResp = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    console.log("FHIR response:", JSON.stringify(dataResp.data, null, 2));
    let entryData = dataResp.data.entry || [];
    if (typeof entryData === "string") {
      try {
        entryData = JSON.parse(entryData);
      } catch (parseError) {
        console.error("Error parsing entry data:", parseError);
        entryData = [];
      }
    }

    return {
      success: true,
      data: {
        entry: entryData,
      },
      error: null,
    };
  } catch (error: unknown) {
    console.error(`Error fetching ${endpoint} data:`, error);
    throw new Error(`Failed to fetch ${endpoint} data`);
  }
}

async function refreshAccessToken(
  refreshToken: string,
  userId?: string,
  provider?: string
): Promise<ResponseType> {
  const clientId = process.env.ONEUP_CLIENT_PORTAL_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_PORTAL_CLIENT_SECRET;

  if (!clientId || !clientSecret || !refreshToken || !userId || !provider) {
    throw new Error("Required parameters are missing");
  }

  const appUserId = `jinix_${userId}`;

  try {
    const connection = await client.models.HealthDataConnection.get({
      appUserId,
    });

    if (!connection.data) {
      throw new Error("Connection not found");
    }

    const tokenResponse = await axios.post(
      "https://auth.1up.health/oauth2/token",
      new URLSearchParams({
        client_id: String(clientId),
        client_secret: String(clientSecret),
        refresh_token: String(refreshToken),
        grant_type: "refresh_token",
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    const tokenData = tokenResponse.data;
    const now = new Date();

    await client.models.HealthDataConnection.update({
      appUserId,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || connection.data.refreshToken,
      updatedAt: now.toISOString(),
    });

    return {
      success: true,
      data: {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token || connection.data.refreshToken,
        expires_in: tokenData.expires_in,
      },
      error: null,
    };
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error refreshing token:", axiosError?.response?.data);

    // If refresh token is invalid/expired, generate a new auth code
    if (
      axiosError.response?.status === 400 ||
      axiosError.response?.status === 401
    ) {
      console.log("Refresh token expired, generating new auth code...");
      const authCodeResponse = await getAuthCode(appUserId);
      if (!authCodeResponse.success || !authCodeResponse.data?.code) {
        throw new Error("Failed to generate new auth code");
      }
      const code = String(authCodeResponse.data.code);

      const tokenResponse = await axios.post(
        "https://auth.1up.health/oauth2/token",
        new URLSearchParams({
          grant_type: "authorization_code",
          code: code,
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const tokenData = tokenResponse.data;
      const now = new Date();

      await client.models.HealthDataConnection.update({
        appUserId,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        code, // Only pass code when we have a new one
        updatedAt: now.toISOString(),
      });

      return {
        success: true,
        data: {
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token,
          expires_in: tokenData.expires_in,
        },
        error: null,
      };
    }

    throw new Error(
      `Failed to refresh token: ${axiosError?.response?.data || axiosError?.message}`
    );
  }
}

async function getValidAccessToken(
  userId: string,
  provider: string
): Promise<string> {
  const appUserId = `jinix_${userId}`;
  const clientId = process.env.ONEUP_CLIENT_PORTAL_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_PORTAL_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    throw new Error("OneUpHealth credentials not configured");
  }

  try {
    const connection = await client.models.HealthDataConnection.get({
      appUserId,
    });

    if (!connection.data) {
      throw new Error("Connection not found");
    }

    // Try using the existing access token
    try {
      // Test the access token with a lightweight API call
      await axios.get(`https://api.1up.health/r4/Patient`, {
        headers: {
          Authorization: `Bearer ${connection.data.accessToken}`,
        },
      });
      return connection.data.accessToken;
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      if (axiosError.response?.status === 401) {
        console.log("Access token invalid, attempting to refresh...");
      } else {
        throw new Error(
          `Failed to validate access token: ${axiosError?.response?.data || axiosError?.message}`
        );
      }
    }

    // Try refreshing the token
    const tokenResponse = await axios.post(
      "https://auth.1up.health/oauth2/token",
      new URLSearchParams({
        client_id: String(clientId),
        client_secret: String(clientSecret),
        refresh_token: String(connection.data.refreshToken),
        grant_type: "refresh_token",
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    const tokenData = tokenResponse.data;
    const now = new Date();

    await client.models.HealthDataConnection.update({
      appUserId,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || connection.data.refreshToken,
      updatedAt: now.toISOString(),
    });

    return tokenData.access_token;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error(
      "Error getting valid access token:",
      axiosError?.response?.data
    );

    // If refresh token is invalid/expired, generate a new auth code
    if (
      axiosError.response?.status === 400 ||
      axiosError.response?.status === 401
    ) {
      console.log("Refresh token expired, generating new auth code...");
      const authCodeResponse = await getAuthCode(appUserId);
      if (!authCodeResponse.success || !authCodeResponse.data?.code) {
        throw new Error("Failed to generate new auth code");
      }
      const code = String(authCodeResponse.data.code);

      const tokenResponse = await axios.post(
        "https://auth.1up.health/oauth2/token",
        new URLSearchParams({
          grant_type: "authorization_code",
          code: code,
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const tokenData = tokenResponse.data;
      const now = new Date();

      await client.models.HealthDataConnection.update({
        appUserId,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        code, // Only pass code when we have a new one
        updatedAt: now.toISOString(),
      });

      return tokenData.access_token;
    }

    throw new Error(
      `Failed to get valid access token: ${axiosError?.response?.data || axiosError?.message}`
    );
  }
}

async function refreshUserToken(
  userId: string,
  provider: string
): Promise<ResponseType> {
  const clientId = process.env.ONEUP_CLIENT_PORTAL_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_PORTAL_CLIENT_SECRET;

  if (!clientId || !clientSecret || !userId || !provider) {
    throw new Error("Required parameters are missing");
  }

  const appUserId = `jinix_${userId}`;

  try {
    const connection = await client.models.HealthDataConnection.get({
      appUserId,
    });

    if (!connection.data) {
      throw new Error("Connection not found");
    }

    const tokenResponse = await axios.post(
      "https://auth.1up.health/oauth2/token",
      new URLSearchParams({
        client_id: String(clientId),
        client_secret: String(clientSecret),
        refresh_token: String(connection.data.refreshToken),
        grant_type: "refresh_token",
      }),
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      }
    );

    const tokenData = tokenResponse.data;
    const now = new Date();

    await client.models.HealthDataConnection.update({
      appUserId,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || connection.data.refreshToken,
      updatedAt: now.toISOString(),
    });

    return {
      success: true,
      data: {
        access_token: tokenData.access_token,
        refresh_token: tokenData.refresh_token || connection.data.refreshToken,
        expires_in: tokenData.expires_in,
      },
      error: null,
    };
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error refreshing user token:", axiosError?.response?.data);

    // If refresh token is invalid/expired, generate a new auth code
    if (
      axiosError.response?.status === 400 ||
      axiosError.response?.status === 401
    ) {
      console.log("Refresh token expired, generating new auth code...");
      const authCodeResponse = await getAuthCode(appUserId);
      if (!authCodeResponse.success || !authCodeResponse.data?.code) {
        throw new Error("Failed to generate new auth code");
      }
      const code = String(authCodeResponse.data.code);

      const tokenResponse = await axios.post(
        "https://auth.1up.health/oauth2/token",
        new URLSearchParams({
          grant_type: "authorization_code",
          code: code,
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const tokenData = tokenResponse.data;
      const now = new Date();

      await client.models.HealthDataConnection.update({
        appUserId,
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        code, // Only pass code when we have a new one
        updatedAt: now.toISOString(),
      });

      return {
        success: true,
        data: {
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token,
          expires_in: tokenData.expires_in,
        },
        error: null,
      };
    }
    throw new Error(
      `Failed to refresh user token: ${axiosError?.response?.data || axiosError?.message}`
    );
  }
}
