import { useState, useEffect, useCallback, useRef } from "react";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import {
  DEFAULT_CHAT_NAME,
  DEFAULT_CHAT_DESCRIPTION,
  ERROR_MISSING_USER_ID,
  ERROR_FAILED_CREATE_CHAT,
  AI_ASSISTANT_ID,
} from "@/constants/chat";
import { Chat } from "@/types/chat";

const client = generateClient<Schema>();

export function useChats(
  userId: string | null,
  questionnaireId?: string,
  isHealthTab: boolean = false
) {
  const [chatId, setChatId] = useState<string | null>(null);
  const [userChats, setUserChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [nextToken, setNextToken] = useState<string | null>(null);
  const [hasMoreChats, setHasMoreChats] = useState(true);
  const [isHealthChatInitializing, setIsHealthChatInitializing] =
    useState(false);

  const healthChatInitializedRef = useRef(false);

  const fetchUserChats = useCallback(
    async (
      currentUserId: string,
      token: string | null = null,
      reset = false
    ) => {
      if (!currentUserId) return;

      try {
        if (reset) {
          setIsLoadingChats(true);
          setNextToken(null);
          setHasMoreChats(true);
        } else if (token) {
          setIsLoadingMore(true);
        } else {
          setIsLoadingChats(true);
        }

        const { data: chatParticipants, nextToken: newNextToken } =
          await client.models.ChatParticipant.listChatParticipantsByUser(
            { userId: currentUserId },
            {
              limit: 20,
              nextToken: token,
              selectionSet: [
                "id",
                "chatId",
                "userId",
                "user.name",
                "user.email",
                "user.profilePicture",
                "chat.id",
                "chat.name",
                "chat.description",
                "chat.questionnaireId",
                "chat.chatType",
                "chat.lastMessageAt",
                "chat.updatedAt",
                "chat.messages.id",
                "chat.messages.message",
                "chat.messages.userId",
                "chat.messages.messageType",
                "chat.messages.createdAt",
                "chat.messages.user.email",
                "chat.chatParticipants.userId",
                "chat.chatParticipants.user.name",
                "chat.chatParticipants.user.email",
                "chat.chatParticipants.user.profilePicture",
              ],
            }
          );

        if (!chatParticipants || chatParticipants.length === 0) {
          setUserChats([]);
          setHasMoreChats(false);
          setIsLoadingChats(false);
          setIsLoadingMore(false);
          return;
        }

        const validChats = chatParticipants
          .map((cp) => {
            if (!cp.chat) return null;

            const sortedMessages = cp.chat.messages?.slice() || [];
            sortedMessages.sort((a, b) => {
              const dateA = new Date(a.createdAt || 0);
              const dateB = new Date(b.createdAt || 0);
              return dateB.getTime() - dateA.getTime();
            });

            const lastMessage =
              sortedMessages.length > 0 ? sortedMessages[0] : null;

            let lastMessageSenderName = "Jinix";
            if (lastMessage && lastMessage.userId) {
              const sender = cp.chat.chatParticipants?.find(
                (p) => p.userId === lastMessage.userId
              );
              if (sender && sender.user) {
                lastMessageSenderName =
                  sender.user.email || sender.user.name || "Jinix";
              }
            }

            const chatData = cp.chat as any;

            return {
              id: chatData.id,
              name: chatData.name,
              description: chatData.description,
              questionnaireId: chatData.questionnaireId,
              chatType: chatData.chatType,
              lastMessageAt: chatData.lastMessageAt,
              updatedAt: chatData.updatedAt,
              metadata: chatData.metadata || null,
              messages: chatData.messages,
              chatParticipants: chatData.chatParticipants,
              lastMessage: lastMessage
                ? {
                    ...lastMessage,
                    senderName: lastMessageSenderName,
                  }
                : null,
            } as Chat;
          })
          .filter((chat): chat is Chat => chat !== null && chat !== undefined);

        setUserChats((prevChats) => {
          if (reset || !token) {
            return validChats;
          } else {
            return [...prevChats, ...validChats];
          }
        });

        setHasMoreChats(!!newNextToken);
        setNextToken(newNextToken || null);
      } catch (error) {
        console.error("Error fetching user chats:", error);
        setError(`Failed to fetch user chats: ${error}`);
      } finally {
        setIsLoadingChats(false);
        setIsLoadingMore(false);
      }
    },
    []
  );

  const loadMoreChats = useCallback(() => {
    if (
      userId &&
      nextToken &&
      hasMoreChats &&
      !isLoadingMore &&
      !isLoadingChats
    ) {
      fetchUserChats(userId, nextToken);
    }
  }, [
    userId,
    nextToken,
    hasMoreChats,
    isLoadingMore,
    isLoadingChats,
    fetchUserChats,
  ]);

  const findEmptyChat = useCallback(
    async (currentUserId: string): Promise<string | null> => {
      if (!currentUserId) return null;

      try {
        const { data: chatParticipants } =
          await client.models.ChatParticipant.listChatParticipantsByUser(
            { userId: currentUserId },
            {
              sortDirection: "DESC",
              limit: 1,
              selectionSet: ["chatId", "chat.id", "chat.messages.userId"],
            }
          );

        if (!chatParticipants || chatParticipants.length === 0) {
          return null;
        }

        const latestChat = chatParticipants[0].chat;

        if (latestChat) {
          const hasNoUserMessages =
            !latestChat.messages ||
            latestChat.messages.length === 0 ||
            !latestChat.messages.some(
              (message) => message.userId !== AI_ASSISTANT_ID
            );

          if (hasNoUserMessages && latestChat.id) {
            console.log("Found empty chat to reuse:", latestChat.id);
            return latestChat.id;
          }
        }

        return null;
      } catch (error) {
        console.error("Error checking for empty chats:", error);
        return null;
      }
    },
    []
  );

  const findHealthChat = useCallback(
    async (currentUserId: string, questId: string): Promise<string | null> => {
      if (!currentUserId || !questId) return null;

      try {
        if (questId === "2") {
          const determinisiticChatId = `${currentUserId}-health`;

          const { data: healthChat } = await client.models.Chat.get(
            { id: determinisiticChatId },
            { selectionSet: ["id"] }
          );

          if (healthChat) {
            console.log(`Found existing health chat: ${healthChat.id}`);
            return healthChat.id;
          }

          return null;
        } else {
          const { data: chatParticipants } =
            await client.models.ChatParticipant.listChatParticipantsByUser(
              { userId: currentUserId },
              {
                sortDirection: "DESC",
                limit: 10,
                selectionSet: ["chatId", "chat.id", "chat.questionnaireId"],
              }
            );

          if (!chatParticipants || chatParticipants.length === 0) {
            return null;
          }

          const healthChat = chatParticipants
            .map((cp) => cp.chat)
            .find((chat) => chat?.questionnaireId === questId);

          if (healthChat) {
            return healthChat.id;
          }

          return null;
        }
      } catch (error) {
        console.error("Error finding health chat:", error);
        return null;
      }
    },
    []
  );

  const createNewChat = useCallback(
    async (
      currentUserId: string,
      customQuestionnaireId?: string,
      chatType: "AI" | "DIRECT" | "GROUP" = "AI"
    ): Promise<string | null> => {
      if (!currentUserId) {
        setError(ERROR_MISSING_USER_ID);
        return null;
      }

      try {
        setIsLoading(true);

        const effectiveQuestionnaireId =
          customQuestionnaireId || questionnaireId;

        if (isHealthTab && effectiveQuestionnaireId === "2") {
          const existingHealthChatId = await findHealthChat(
            currentUserId,
            effectiveQuestionnaireId
          );
          if (existingHealthChatId) {
            setChatId(existingHealthChatId);
            setIsLoading(false);
            return existingHealthChatId;
          }
        } else if (!isHealthTab) {
          const emptyChat = await findEmptyChat(currentUserId);
          if (emptyChat) {
            setChatId(emptyChat);
            setIsLoading(false);
            return emptyChat;
          }
        }

        const chatQuestionnaireId = effectiveQuestionnaireId || "1";
        const chatName = isHealthTab ? "Health Assessment" : DEFAULT_CHAT_NAME;
        const chatDescription = isHealthTab
          ? "Health tracking and assessment"
          : DEFAULT_CHAT_DESCRIPTION;

        console.log(
          `Creating new ${isHealthTab ? "health" : ""} chat with questionnaireId: ${chatQuestionnaireId}`
        );

        const { data: createdChat } =
          isHealthTab && effectiveQuestionnaireId === "2"
            ? await client.mutations.createHealthChat({})
            : await client.mutations.createChatWithQuestionnaire({
                name: chatName,
                description: chatDescription,
                questionnaireId: chatQuestionnaireId,
              });

        if (!createdChat || !createdChat.id) {
          throw new Error(ERROR_FAILED_CREATE_CHAT);
        }

        console.log(`Successfully created chat with ID: ${createdChat.id}`);

        const newChatId = createdChat.id;
        const newChat: Chat = {
          id: createdChat.id,
          name: createdChat.name,
          description: createdChat.description,
          questionnaireId: createdChat.questionnaireId,
          chatType: chatType,
          metadata: createdChat.metadata,
        };

        setChatId(newChatId);

        setUserChats((prevChats) => {
          if (prevChats.find((chat) => chat.id === newChatId)) {
            return prevChats;
          }
          return [newChat, ...prevChats];
        });

        if (isHealthTab && effectiveQuestionnaireId === "2") {
          healthChatInitializedRef.current = true;
        }

        return newChatId;
      } catch (err) {
        console.error(ERROR_FAILED_CREATE_CHAT, err);
        setError(ERROR_FAILED_CREATE_CHAT);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [findEmptyChat, findHealthChat, isHealthTab, questionnaireId]
  );

  const switchToChat = useCallback(async (selectedChatId: string) => {
    if (!selectedChatId) return;

    try {
      setIsLoading(true);
      setError(null);

      setChatId(selectedChatId);
    } catch (error) {
      console.error("Error switching chats:", error);
      setError(`Failed to switch to the selected chat: ${error}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const startNewChat = useCallback(async () => {
    if (!userId) {
      setError(ERROR_MISSING_USER_ID);
      return null;
    }

    setIsLoadingChats(true);

    try {
      const newChatId = await createNewChat(userId);
      setChatId(newChatId);
      return newChatId;
    } catch (error) {
      console.error("Failed to create new chat:", error);
      return null;
    } finally {
      setIsLoadingChats(false);
    }
  }, [userId, createNewChat]);

  const deleteChat = useCallback(
    async (chatIdToDelete: string) => {
      if (!userId) {
        setError(ERROR_MISSING_USER_ID);
        return;
      }

      try {
        setIsLoading(true);

        setUserChats((prevChats) =>
          prevChats.filter((chat) => chat.id !== chatIdToDelete)
        );

        if (chatId === chatIdToDelete) {
          setChatId(null);
        }
      } catch (error) {
        console.error("Error deleting chat:", error);
        setError(`Failed to delete chat: ${error}`);
      } finally {
        setIsLoading(false);
      }
    },
    [userId, chatId]
  );

  useEffect(() => {
    if (userId) {
      fetchUserChats(userId, null, true);
    }
  }, [userId, fetchUserChats]);

  useEffect(() => {
    if (!userId || !isHealthTab || questionnaireId !== "2") {
      return;
    }

    if (isHealthChatInitializing || healthChatInitializedRef.current) {
      return;
    }

    const initHealthChat = async () => {
      try {
        setIsHealthChatInitializing(true);
        console.log("Initializing health chat...");

        const existingHealthChatId = await findHealthChat(userId, "2");

        if (existingHealthChatId) {
          console.log(
            `Found existing health chat: ${existingHealthChatId}, setting as active`
          );
          setChatId(existingHealthChatId);
          healthChatInitializedRef.current = true;
          return;
        }

        console.log("No existing health chat found, creating new one");
        const newChatId = await createNewChat(userId, "2");

        if (newChatId) {
          console.log(`Successfully initialized health chat: ${newChatId}`);
          healthChatInitializedRef.current = true;
        } else {
          console.error("Failed to initialize health chat for questionnaire 2");
        }
      } catch (err) {
        console.error("Error in initHealthChat:", err);
      } finally {
        setIsHealthChatInitializing(false);
      }
    };

    initHealthChat();
  }, [
    userId,
    isHealthTab,
    questionnaireId,
    findHealthChat,
    createNewChat,
    isHealthChatInitializing,
  ]);

  return {
    chatId,
    userChats,
    isLoading: isLoadingChats || isLoading || isHealthChatInitializing,
    isLoadingChats,
    isLoadingMore,
    hasMoreChats,
    error,
    fetchUserChats,
    loadMoreChats,
    createNewChat,
    switchToChat,
    startNewChat,
    deleteChat,
  };
}
