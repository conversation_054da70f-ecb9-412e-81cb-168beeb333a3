"use client"


import useRegister from "@/hooks/useRegister"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Amplify } from "aws-amplify"
import outputs from "@/amplify_outputs.json"
import { registrationSchema } from "@/lib/validation_schema/schema"
import { FormData } from "@/types/auth"

import { DesktopLayout } from "@/components/auth/DesktopLayout"
import { MobileLayout } from "@/components/auth/MobileLayout"
import { useWindowSize } from "@/hooks/useWindowSize"

Amplify.configure(outputs)

export default function Register() {
  const isMobile = useWindowSize();
  const form = useForm<FormData>({
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
    },
    resolver: zodResolver(registrationSchema),
  })

  const {
    loading,
    errorMessage,
    isCodeSent,
    confirmationCode,
    setConfirmationCode,

    handleRegister,
    handleConfirmSignUp,
    handleResendCode,
    handleGoogleSignIn,
    handleFacebookSignIn,
  } = useRegister()



  return (
    <main className="h-full flex flex-col md:flex-row">
      {isMobile ? (
        
        <DesktopLayout
        isCodeSent={isCodeSent}
        form={form}
        handleRegister={handleRegister}
        loading={loading}
        errorMessage={errorMessage}
        confirmationCode={confirmationCode}
        setConfirmationCode={setConfirmationCode}
        handleConfirmSignUp={() => handleConfirmSignUp(confirmationCode)}
        handleResendCode={handleResendCode}
        handleGoogleSignIn={handleGoogleSignIn}
        handleFacebookSignIn={handleFacebookSignIn}
      />
      ) : (
        <MobileLayout
          
          form={form}
          handleRegister={handleRegister}
          loading={loading}
          errorMessage={errorMessage}
          handleGoogleSignIn={handleGoogleSignIn}
          handleFacebookSignIn={handleFacebookSignIn}
        />
      )}
    </main>
  );
}