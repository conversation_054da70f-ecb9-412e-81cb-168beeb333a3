import { Button } from "@/components/ui/button";
import { useEffect, useRef } from "react";

type ContactRequestConfirmationProps = {
  onClose: () => void;
};

export default function ContactRequestConfirmation({ onClose }: ContactRequestConfirmationProps) {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Disable scrolling on body when modal is open
    document.body.style.overflow = 'hidden';
    
    // Focus the button when modal opens
    buttonRef.current?.focus();
    
    // Handle escape key
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
      // Prevent tab navigation and any other keyboard interactions
      if (e.key === 'Tab' || e.key !== 'Escape') {
        e.preventDefault();
        buttonRef.current?.focus();
      }
    };
    
    // Prevent wheel/scroll events
    const preventScroll = (e: WheelEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };
    
    window.addEventListener('keydown', handleKeyDown, { capture: true });
    window.addEventListener('wheel', preventScroll, { passive: false });
    
    // Cleanup function
    return () => {
      document.body.style.overflow = '';
      window.removeEventListener('keydown', handleKeyDown, { capture: true });
      window.removeEventListener('wheel', preventScroll);
    };
  }, [onClose]);

  // Prevent click propagation on the overlay
  const handleOverlayClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Only allow closing via the Got it button
  };

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center z-[9999]"
      onClick={handleOverlayClick}
      style={{ pointerEvents: 'all', touchAction: 'none' }}
      ref={modalRef}
    >
      <div 
        className="bg-white p-6 rounded-lg max-w-sm w-full mx-4 shadow-lg"
        onClick={(e) => e.stopPropagation()}
        aria-modal="true"
        role="dialog"
      >
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Your contact request has been sent.</h2>
          
          <Button 
            ref={buttonRef}
            onClick={onClose}
            className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-full py-2"
          >
            Got it
          </Button>
        </div>
      </div>
    </div>
  );
}
