import Link from "next/link";
import Image from "next/image";
import { ChevronRight, Clock } from "lucide-react";
import { useState, useEffect } from "react";

interface MenuItemProps {
  href: string;
  icon: string;
  label: string;
  iconColor: string;
  comingSoon?: boolean;
}

export function MenuItem({ href, icon, label, iconColor, comingSoon = false }: MenuItemProps) {
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    if (showComingSoon) {
      // First make it visible
      setIsVisible(true);
      
      // Set timeout to hide it
      timeout = setTimeout(() => {
        setIsVisible(false);
        
        // After transition completes, remove from DOM
        setTimeout(() => {
          setShowComingSoon(false);
        }, 300); // Match this with the transition duration
      }, 2000);
    }
    
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [showComingSoon]);
  
  const handleClick = (e: React.MouseEvent) => {
    if (comingSoon) {
      e.preventDefault();
      setShowComingSoon(true);
    }
  };
  
  return (
    <div className="relative overflow-hidden">
      {showComingSoon && (
        <div className={`absolute inset-0 flex items-center justify-center z-10 backdrop-blur-sm transition-all duration-300 ease-in-out ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
          <div className="bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 px-5 py-3 rounded-full shadow-xl flex items-center space-x-2 transition-all duration-300 ease-in-out transform ${isVisible ? 'scale-100' : 'scale-95'}">
            <Clock className="text-white h-5 w-5" />
            <div className="text-white font-semibold whitespace-nowrap">
              Coming Soon
            </div>
          </div>
        </div>
      )}
      
      <Link href={comingSoon ? "#" : href} className="block" onClick={handleClick}>
        <div className="bg-white rounded-xl p-2 flex items-center justify-between shadow-sm ">
          <div className="flex items-center">
            <div className={`w-10 h-10 flex items-center justify-center ${iconColor}`}>
              <Image src={icon} alt={label} width={24} height={24} />
            </div>
            <span className="ml-3 text-lg font-medium">{label}</span>
          </div>
          <ChevronRight className="text-gray-400 w-6 h-6" />
        </div>
      </Link>
    </div>
  );
}
