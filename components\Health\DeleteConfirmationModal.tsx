import React from 'react';

interface DeleteConfirmationModalProps {
  onConfirm: () => void;
  onCancel: () => void;
}

const DeleteConfirmationModal = ({ onConfirm, onCancel }: DeleteConfirmationModalProps) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[3000]">
      <div className="bg-white rounded-[12px] p-5 w-[300px] h-[186px] text-center flex flex-col justify-between">
        <div className="mb-2">
          <h2 className="text-[16px] font-semibold mb-1">You are about to delete the goal.</h2>
          <p className="text-[#171717] text-[16px] font-semibold">This action cannot be undone.</p>
        </div>
        
        <div className="flex flex-col gap-2 mb-1">
          <button
            onClick={onConfirm}
            className="w-full bg-[#DC2625] text-white font-semibold h-[34px] px-4 rounded-full text-[16px]"
          >
            Delete
          </button>
          <button
            onClick={onCancel}
            className="w-full bg-[#E5E5E5] text-black font-semibold h-[34px] px-4 rounded-full text-[16px]"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
