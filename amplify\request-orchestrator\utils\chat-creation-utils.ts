import { generateClient } from "aws-amplify/api";
import type { Schema } from "../../data/resource";
import { AI_ASSISTANT_ID, MESSAGE_TYPE_SYSTEM } from "../../../constants/chat";

const client = generateClient<Schema>();

export interface CreateChatParams {
  id?: string;
  name: string;
  description?: string;
  chatType?: "AI" | "DIRECT" | "GROUP";
  questionnaireId?: string;
  metadata?: Record<string, any>;
  participantUserIds: string[];
  systemMessage?: string;
}

export async function createStandardChat({
  id,
  name,
  description,
  chatType = "AI",
  questionnaireId,
  metadata = {},
  participantUserIds,
  systemMessage,
}: CreateChatParams) {
  if (!participantUserIds || participantUserIds.length === 0) {
    throw new Error("At least one participant is required to create a chat");
  }

  try {
    if (id) {
      console.log(`Checking if chat with ID ${id} already exists`);
      const { data: existingChat } = await client.models.Chat.get({ id });
      if (existingChat) {
        console.log(
          `Chat already exists with ID ${id}, returning existing chat`
        );
        return existingChat;
      }
    }

    const standardMetadata = {
      createdBy: participantUserIds[0],
      isArchived: false,
      isQuestionnaireComplete: false,
      currentQuestionIndex: 0,
      isGroup: chatType === "GROUP",
      ...metadata,
    };

    console.log(
      `Creating new chat: ${name}, type: ${chatType}, with the following data:`,
      {
        id: id || "auto-generated",
        name,
        description,
        chatType,
        questionnaireId,
        metadata: standardMetadata,
      }
    );

    const { data: chat } = await client.models.Chat.create({
      ...(id && { id }),
      name,
      description,
      chatType,
      questionnaireId,
      metadata: standardMetadata,
    });

    if (!chat) {
      console.error(
        `Failed to create ${chatType} chat, received null response`
      );
      throw new Error(`Failed to create ${chatType} chat`);
    }

    console.log(`Chat created successfully with ID: ${chat.id}`);

    console.log(
      `Adding ${participantUserIds.length} participants to chat ${chat.id}`
    );

    const participantPromises = participantUserIds.map(async (userId) => {
      try {
        const result = await client.models.ChatParticipant.create({
          id: `${chat.id}:${userId}`,
          chatId: chat.id,
          userId,
          joinedAt: new Date().toISOString(),
        });
        console.log(`Added participant ${userId} to chat ${chat.id}`);
        return result;
      } catch (err) {
        console.error(
          `Failed to add participant ${userId} to chat ${chat.id}:`,
          err
        );
        throw err;
      }
    });

    await Promise.all(participantPromises);

    if (systemMessage) {
      console.log(`Adding system message to chat ${chat.id}`);
      try {
        await client.models.ChatMessage.create({
          chatId: chat.id,
          userId: AI_ASSISTANT_ID,
          message: systemMessage,
          messageType: MESSAGE_TYPE_SYSTEM,
          attachments: [],
          createdAt: new Date().toISOString(),
        });
        console.log(`System message added to chat ${chat.id}`);
      } catch (err) {
        console.error(`Failed to add system message to chat ${chat.id}:`, err);
      }
    }

    return chat;
  } catch (error) {
    console.error("Error in createStandardChat:", error);
    throw error;
  }
}
