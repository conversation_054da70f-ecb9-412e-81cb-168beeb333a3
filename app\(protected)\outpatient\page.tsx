"use client";

import Header from "./components/Header";
import { useMedicalRecord } from "@/hooks/useMedicalRecord";
import OutpatientContent from "../medical-record/components/OutpatientContent";
import MedicationContent from "../medical-record/components/MedicationContent";
import SymptomContent from "../medical-record/components/SymptomContent";
import MedicationReactionContent from "../medical-record/components/MedicationReactionContent";
import { useState } from "react";

export default function OutpatientVisit() {
  const { selectedRecord } = useMedicalRecord();

  // Автоматичне визначення типу контенту
  const getContentComponent = () => {
    if (!selectedRecord || !selectedRecord.rawData) {
      return <OutpatientContent record={selectedRecord} />;
    }

    const raw = selectedRecord.rawData;

    // Якщо це FHIR Condition (симптом)
    if (
      typeof raw === "object" &&
      raw !== null &&
      "resourceType" in raw &&
      raw.resourceType === "Condition"
    ) {
      return <SymptomContent record={selectedRecord} />;
    }

    // Якщо це FHIR MedicationRequest (медикація)
    if (
      typeof raw === "object" &&
      raw !== null &&
      "resourceType" in raw &&
      raw.resourceType === "MedicationRequest"
    ) {
      return <MedicationContent record={selectedRecord} />;
    }

    // Якщо це FHIR AllergyIntolerance (реакція на медикацію)
    if (
      typeof raw === "object" &&
      raw !== null &&
      "resourceType" in raw &&
      raw.resourceType === "AllergyIntolerance"
    ) {
      return <MedicationReactionContent record={selectedRecord} />;
    }

    // Якщо це FHIR Bundle, спробувати знайти перший відомий тип
    if (
      typeof raw === "object" &&
      raw !== null &&
      "resourceType" in raw &&
      raw.resourceType === "Bundle" &&
      Array.isArray(raw.entry)
    ) {
      for (const entry of raw.entry) {
        if (
          entry &&
          entry.resource &&
          typeof entry.resource === "object" &&
          "resourceType" in entry.resource
        ) {
          if (entry.resource.resourceType === "Condition") {
            return <SymptomContent record={selectedRecord} />;
          }
          if (entry.resource.resourceType === "MedicationRequest") {
            return <MedicationContent record={selectedRecord} />;
          }
          if (entry.resource.resourceType === "AllergyIntolerance") {
            return <MedicationReactionContent record={selectedRecord} />;
          }
        }
      }
    }

    // За замовчуванням — Encounter summary
    return <OutpatientContent record={selectedRecord} />;
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      <Header />
      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-4">
        {getContentComponent()}
      </div>
    </div>
  );
}
