export interface MomentProps {
  moment: {
    id: string;
    userName: string;
    groupTitle: string;
    groupText: string;
    avatar: string;
    text: string;
    content: string;
    image?: string | null;
    imageGrid?: string[];
    likes: number;
    comments: number;
    timeAgo: string;
    isSaved?: boolean;
    isLiked?: boolean;
  };
  onCommentAdd?: (postId: string) => void;
  onBookmarkChange?: () => void;
}

export interface UpdatePostLikesResponse {
  updatePost: {
    id: string;
    likes: number;
  }
}
