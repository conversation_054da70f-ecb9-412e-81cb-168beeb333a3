"use client";
import { useState, useEffect } from "react";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { communityService } from "@/services/communityService";
import { fetchAuthSession } from "aws-amplify/auth";

export default function CreateCommunity() {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    icon: null,
    banner: null,
    permission: "open",
    rules: [""]
  });
  const [errors, setErrors] = useState({
    name: "",
    description: ""
  });
  const [currentUser, setCurrentUser] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  const router = useRouter();
  
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          setCurrentUser(String(session.tokens.idToken.payload.email));
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      }
    };
    
    fetchUser();
  }, []);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Validate input length
    if (value.length > 50) {
      setErrors(prev => ({
        ...prev,
        [name]: "Maximum length is 50 characters"
      }));
    } else {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
    
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleRuleChange = (index: number, value: string) => {
    const updatedRules = [...formData.rules];
    updatedRules[index] = value;
    setFormData(prev => ({ ...prev, rules: updatedRules }));
  };
  
  const addRule = () => {
    setFormData(prev => ({ ...prev, rules: [...prev.rules, ""] }));
  };
  
  const handlePermissionChange = (value: string) => {
    setFormData(prev => ({ ...prev, permission: value }));
  };
  
  const nextStep = () => {
    if (step === 1) {
      // Prevent proceeding if name or description is empty or too long
      if (
        !formData.name.trim() ||
        !formData.description.trim() ||
        formData.name.length > 50 ||
        formData.description.length > 50
      ) {
        return;
      }
    }
    setStep(prev => prev + 1);
  };
  
  const prevStep = () => {
    if (step > 1) {
      setStep(prev => prev - 1);
    } else {
      router.back();
    }
  };
  
  const skipStep = () => {
    nextStep();
  };
  
  const handleSubmit = async () => {
    if (!currentUser) {
      console.error("No user logged in");
      return;
    }
    
    if (!formData.name.trim()) {
      alert("Community name is required");
      return;
    }
    
    if (!formData.description.trim()) {
      alert("Community description is required");
      return;
    }
    
    if (formData.name.length > 50 || formData.description.length > 50) {
      alert("Name and description must be 50 characters or less");
      return;
    }
    
    setIsLoading(true);
    
    try {
      console.log("Creating community with data:", {
        ...formData,
        createdBy: currentUser
      });
      

      const filteredRules = formData.rules.filter(rule => rule.trim() !== "");
      
      const result = await communityService.createCommunity({
        name: formData.name,
        description: formData.description,
        icon: formData.icon,
        banner: formData.banner,
        permission: formData.permission as "open" | "request",
        rules: filteredRules,
        createdBy: currentUser,
      });
      
      console.log("Community created successfully:", result);
      

      router.push("/comunity");
    } catch (error) {
      console.error("Error creating community:", error);
      alert("Failed to create community. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-white ">
      {/* Header */}
      <div className="p-4 flex items-center border-b">
        <button onClick={prevStep} className="mr-4">
          <ArrowLeft className="w-5 h-5" />
        </button>
        <h1 className="text-center flex-1 font-medium">Create a Community</h1>
        <div className="w-5"></div> 
      </div>
      
      {/* Step 1: Tell us about your community */}
      {step === 1 && (
        <div className="p-5">
          <h2 className="text-xl font-bold mb-2">Tell us about your community</h2>
          <p className="text-gray-600 mb-5 text-sm">
            Give your community a name and description to help others understand what its about.
          </p>
          
          <div className="mb-4">
            <label className="block mb-1 font-medium text-sm">
              Group Name <span className="text-red-500">*</span>
            </label>
            <input 
              type="text" 
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter Name"
              className={`w-full p-3 border rounded-lg ${errors.name ? 'border-red-500' : ''}`}
              maxLength={50}
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
            <p className="text-gray-500 text-xs mt-1">{formData.name.length}/50 characters</p>
          </div>
          
          <div className="mb-4">
            <label className="block mb-1 font-medium text-sm">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea 
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter Description"
              className={`w-full p-3 border rounded-lg h-24 ${errors.description ? 'border-red-500' : ''}`}
              maxLength={50}
            />
            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
            <p className="text-gray-500 text-xs mt-1">{formData.description.length}/50 characters</p>
          </div>
          
          <div className=" bottom-5 left-5 right-5">
            <button 
              onClick={nextStep}
              className={`w-full py-3 rounded-lg ${
                !!errors.name ||
                !!errors.description ||
                !formData.name.trim() ||
                !formData.description.trim()
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-blue-500 text-white"
              }`}
              disabled={
                !!errors.name ||
                !!errors.description ||
                !formData.name.trim() ||
                !formData.description.trim()
              }
            >
              Next
            </button>
          </div>
        </div>
      )}
      
      {/* {step === 2 && (
        <div className="p-5">
          <h2 className="text-xl font-bold mb-2">Style Your Community</h2>
          <p className="text-gray-600 mb-5 text-sm">
            Customize your communitys look.
          </p>
          
          <div className="bg-blue-100 rounded-lg p-5 mb-6">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gray-300 rounded-full mr-3"></div>
              <div>
                <p className="font-medium">Group Name</p>
                <p className="text-sm text-gray-600">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                  Lorem ipsum dolor sit amet.
                </p>
              </div>
            </div>
          </div>
          
          <div className="mb-4 flex justify-between items-center">
            <div>
              <h3 className="font-medium">Community Icon</h3>
            </div>
            <button className="bg-gray-200 px-3 py-1 rounded-lg text-sm">Add</button>
          </div>
          
          <div className="mb-4 flex justify-between items-center">
            <div>
              <h3 className="font-medium">Community Banner</h3>
              <p className="text-xs text-gray-500">3:1 Ratio</p>
            </div>
            <button className="bg-gray-200 px-3 py-1 rounded-lg text-sm">Add</button>
          </div>
          
          <div className=" bottom-5 left-5 right-5 flex space-x-3">
            <button 
              onClick={skipStep}
              className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg"
            >
              Skip
            </button>
            <button 
              onClick={nextStep}
              className="flex-1 bg-blue-500 text-white py-3 rounded-lg"
            >
              Next
            </button>
          </div>
        </div>
      )}
      
     
      {step === 3 && (
        <div className="p-5">
          <h2 className="text-xl font-bold mb-2">Permission</h2>
          <p className="text-gray-600 mb-5 text-sm">
            Decide the permissions in your community.
          </p>
          
          <div className="mb-4">
            <label className="flex items-center space-x-2">
              <input 
                type="radio" 
                name="permission" 
                value="open"
                checked={formData.permission === "open"}
                onChange={() => handlePermissionChange("open")}
                className="w-5 h-5 text-blue-600"
              />
              <div>
                <p className="font-medium">Open</p>
                <p className="text-sm text-gray-500">Anyone can join freely</p>
              </div>
            </label>
          </div>
          
          <div className="mb-4">
            <label className="flex items-center space-x-2">
              <input 
                type="radio" 
                name="permission" 
                value="request"
                checked={formData.permission === "request"}
                onChange={() => handlePermissionChange("request")}
                className="w-5 h-5 text-blue-600"
              />
              <div>
                <p className="font-medium">Request to Join</p>
                <p className="text-sm text-gray-500">Must be approved by a moderator/admin.</p>
              </div>
            </label>
          </div>
          
          <div className=" bottom-5 left-5 right-5">
            <button 
              onClick={nextStep}
              className="w-full bg-blue-500 text-white py-3 rounded-lg"
            >
              Next
            </button>
          </div>
        </div>
      )}
       */}
  
      {step === 2 && (
        <div className="p-5">
          <h2 className="text-xl font-bold mb-2">Create Rules</h2>
          <p className="text-gray-600 mb-5 text-sm">
            Set clear guidelines to keep your community respectful and supportive.
          </p>
          
          {formData.rules.map((rule, index) => (
            <div key={index} className="mb-3">
              <input 
                type="text" 
                value={rule}
                onChange={(e) => handleRuleChange(index, e.target.value)}
                placeholder="Enter Rule"
                className="w-full p-3 border rounded-lg"
              />
            </div>
          ))}
          
          <button 
            onClick={addRule}
            className="text-blue-500 mb-10"
          >
            + Add another rule
          </button>
          
          <div className=" bottom-5 left-5 right-5 flex space-x-3">
            
            <button 
              onClick={handleSubmit}
              disabled={isLoading}
              className="flex-1 bg-blue-500 text-white py-3 rounded-lg disabled:bg-blue-300"
            >
              {isLoading ? "Creating..." : "Create Community"}
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
