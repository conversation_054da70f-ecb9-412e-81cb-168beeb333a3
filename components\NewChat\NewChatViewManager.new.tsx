"use client";

import { useState, useContext } from "react";
import { useViewState } from "../Chat/context/ViewStateContext";
import NewChatHeader from "./NewChatHeader.new";
import ConfirmNewGroupChat from "./ConfirmNewGroupChat";

const NewChatViewManager = () => {
  const { activeView } = useViewState();

  // Show the appropriate component based on view state
  const renderContent = () => {
    switch (activeView) {
      case "CONFIRM_GROUP_CHAT":
        return <ConfirmNewGroupChat />;
      case "NEW_CHAT":
      default:
        return <NewChatHeader />;
    }
  };

  return <div className="flex flex-col h-full">{renderContent()}</div>;
};

export default NewChatViewManager;
