import { generateClient } from "aws-amplify/data";
import { type Schema } from "../../amplify/data/resource";
import questionnaireMessages from "../../amplify/request-orchestrator/mocks/questionnaire-messages.json";

const client = generateClient<Schema>();

/**
 * Generic function to seed a questionnaire with provided questions
 */
async function seedQuestionnaireGeneric({
  id,
  name,
  messages,
  categoryDeterminer,
}: {
  id: string;
  name: string;
  messages: string[];
  categoryDeterminer: (message: string) => string;
}) {
  try {
    // Check if questionnaire with given ID already exists
    const { data: existingQuestionnaire } =
      await client.models.Questionnaire.get({
        id,
      });

    let questionnaire;

    if (existingQuestionnaire) {
      console.log(
        `Questionnaire with ID '${id}' already exists:`,
        existingQuestionnaire
      );
      questionnaire = existingQuestionnaire;
    } else {
      // Create a questionnaire with explicit ID
      const { data: createdQuestionnaire, errors: questionnaireErrors } =
        await client.models.Questionnaire.create({
          id,
          name,
        });

      if (!createdQuestionnaire || questionnaireErrors) {
        console.error("Error creating questionnaire:", questionnaireErrors);
        return;
      }

      console.log("Created questionnaire:", createdQuestionnaire);
      questionnaire = createdQuestionnaire;
    }

    // Create health questions from messages
    const healthQuestions = await Promise.all(
      messages.map(async (message, index) => {
        // Determine a category based on the provided category determiner function
        const category = categoryDeterminer(message);

        const { data: healthQuestion, errors: healthQuestionErrors } =
          await client.models.HealthQuestion.create({
            prompt: message,
            category: category,
          });

        if (healthQuestionErrors) {
          console.error(
            `Error creating health question ${index}:`,
            healthQuestionErrors
          );
          return null;
        }

        console.log(`Created health question ${index}:`, healthQuestion);
        return healthQuestion;
      })
    );

    // Filter out any null values from failed creations
    const validHealthQuestions = healthQuestions.filter((q) => q !== null);

    // Create QuestionnaireQuestion records to link Questionnaire and HealthQuestion
    await Promise.all(
      validHealthQuestions.map(async (healthQuestion, index) => {
        if (!healthQuestion) return;

        // Check if this link already exists
        const { data: existingLinks } =
          await client.models.QuestionnaireQuestion.listQuestionnaireQuestionsBySequence(
            { questionnaireId: questionnaire.id },
            { filter: { healthQuestionId: { eq: healthQuestion.id } } }
          );

        if (existingLinks && existingLinks.length > 0) {
          console.log(
            `Link already exists for health question ${healthQuestion.id}`
          );
          return;
        }

        const { data: questionnaireQuestion, errors: linkErrors } =
          await client.models.QuestionnaireQuestion.create({
            questionnaireId: questionnaire.id,
            healthQuestionId: healthQuestion.id,
            sequence: index + 1, // 1-based sequence
          });

        if (linkErrors) {
          console.error(
            `Error creating questionnaire question link ${index}:`,
            linkErrors
          );
          return;
        }

        console.log(
          `Created questionnaire question link ${index}:`,
          questionnaireQuestion
        );
      })
    );

    console.log(`${name} seeding completed successfully`);
    return questionnaire;
  } catch (error) {
    console.error(`Error seeding ${name} data:`, error);
    throw error;
  }
}

/**
 * Creates a mock health assessment questionnaire with questions from the questionnaire-messages.json file
 */
export async function seedQuestionnaire() {
  return seedQuestionnaireGeneric({
    id: "1",
    name: "Health Assessment Questionnaire",
    messages: questionnaireMessages.messages,
    categoryDeterminer: (message) => {
      return message.toLowerCase().includes("neuron")
        ? "Neurological"
        : message.toLowerCase().includes("muscle")
          ? "Muscular"
          : "General";
    },
  });
}

/**
 * Creates a mock health profile questionnaire with profile-related questions
 */
export async function seedHealthProfileQuestionnaire() {
  // Ensure that profileMessages exists in the questionnaire-messages.json
  if (
    !questionnaireMessages.profileMessages ||
    !Array.isArray(questionnaireMessages.profileMessages)
  ) {
    throw new Error(
      "profileMessages is not properly defined in questionnaire-messages.json"
    );
  }

  return seedQuestionnaireGeneric({
    id: "2",
    name: "Health Profile Questionnaire",
    messages: questionnaireMessages.profileMessages,
    categoryDeterminer: (message) => {
      // Categorize profile questions based on content
      if (message.toLowerCase().includes("age")) return "Demographics";
      if (message.toLowerCase().includes("gender")) return "Demographics";
      if (message.toLowerCase().includes("country")) return "Demographics";
      if (message.toLowerCase().includes("height")) return "Physical";
      if (message.toLowerCase().includes("weight")) return "Physical";
      if (
        message.toLowerCase().includes("medical") ||
        message.toLowerCase().includes("condition") ||
        message.toLowerCase().includes("medication") ||
        message.toLowerCase().includes("surgeries") ||
        message.toLowerCase().includes("allergies")
      )
        return "Medical History";
      return "General";
    },
  });
}
