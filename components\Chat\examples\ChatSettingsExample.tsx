"use client";

import React from "react";
import { useViewState } from "@/components/Chat/context/ViewStateContext";
import { Button } from "@/components/ui/button";
import { Settings, Camera, Bell, Shield, Moon, HelpCircle } from "lucide-react";

/**
 * Example of a new view component that can be added to the chat interface
 */
export const ChatSettingsPanel = () => {
  const { setActiveView, goBack } = useViewState();

  const handleBack = () => {
    goBack();
  };

  const menuItems = [
    {
      icon: <Camera className="w-5 h-5 text-blue-500" />,
      title: "Media & Photos",
      description: "Manage shared media",
      onClick: () => console.log("Media settings clicked"),
    },
    {
      icon: <Bell className="w-5 h-5 text-orange-500" />,
      title: "Notifications",
      description: "Customize notification settings",
      onClick: () => console.log("Notification settings clicked"),
    },
    {
      icon: <Shield className="w-5 h-5 text-green-500" />,
      title: "Privacy & Security",
      description: "Manage who can contact you",
      onClick: () => console.log("Privacy settings clicked"),
    },
    {
      icon: <Moon className="w-5 h-5 text-purple-500" />,
      title: "Appearance",
      description: "Change theme and display options",
      onClick: () => console.log("Appearance settings clicked"),
    },
    {
      icon: <HelpCircle className="w-5 h-5 text-gray-500" />,
      title: "Help & Support",
      description: "Get help with using the app",
      onClick: () => console.log("Help clicked"),
    },
  ];

  return (
    <div className="flex flex-col h-full w-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={handleBack}>
            Back
          </Button>
          <h2 className="text-xl font-semibold">Settings</h2>
          <div className="w-16"></div> {/* Spacer for alignment */}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {menuItems.map((item, index) => (
            <div
              key={index}
              className="flex items-center p-3 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
              onClick={item.onClick}
            >
              <div className="mr-4">{item.icon}</div>
              <div className="flex-1">
                <h3 className="font-medium">{item.title}</h3>
                <p className="text-sm text-gray-500">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// To use this component in the layout, you would:
// 1. Add a new view state in ViewStateContext.tsx:
//    export type ChatViewState =
//      | ...existing types...
//      | "CHAT_SETTINGS";
//
// 2. Update the renderDetailsPanel function in ChatsLayout.tsx:
//    const renderDetailsPanel = () => {
//      if (activeView === "CHAT_SETTINGS") {
//        return <ChatSettingsPanel />;
//      } else if (activeView === "USER_DETAILS" && selectedUserDetail) {
//        ...
//      }
//    };
//
// 3. Add a button or menu item to navigate to the settings:
//    <Button onClick={() => setActiveView("CHAT_SETTINGS")}>
//      <Settings /> Settings
//    </Button>
