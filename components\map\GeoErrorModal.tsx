import React from "react";

interface GeoErrorModalProps {
  geoError: string | null;
  country: string;
  handleCountryChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  showZipInput: boolean;
  zipCode: string;
  handleZipChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const GeoErrorModal: React.FC<GeoErrorModalProps> = ({
  geoError,
  country,
  handleCountryChange,
  showZipInput,
  zipCode,
  handleZipChange,
}) => {
  const countries = [
    { code: "", name: "Select a country" },
    { code: "USA", name: "United States" },
    { code: "Ukraine", name: "Ukraine" },
    { code: "Canada", name: "Canada" },
    { code: "Germany", name: "Germany" },
    { code: "France", name: "France" },
    { code: "Japan", name: "Japan" },
    { code: "Hong Kong", name: "Hong Kong" },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-gray-900 text-white p-6 rounded-xl shadow-2xl max-w-md w-full border border-gray-800 transform transition-all duration-300 scale-95 hover:scale-100">
        <h2 className="text-2xl font-bold mb-4 tracking-tight">Location issue</h2>
        {geoError && (
          <p className="text-red-400 mb-4 font-mono text-sm">{geoError}</p>
        )}
        <div className="space-y-4">
          <div>
            <label htmlFor="country" className="block text-sm font-semibold text-gray-300">
              Country
            </label>
            <select
              id="country"
              value={country}
              onChange={handleCountryChange}
              className="mt-1 block w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
            >
              {countries.map((c) => (
                <option key={c.code} value={c.code} className="bg-gray-800 text-gray-200">
                  {c.name}
                </option>
              ))}
            </select>
          </div>
          {showZipInput && (
            <div>
              <label htmlFor="zipCode" className="block text-sm font-semibold text-gray-300">
                Postal Code
              </label>
              <input
                id="zipCode"
                type="text"
                value={zipCode}
                onChange={handleZipChange}
                className="mt-1 block w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 text-gray-200 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
                placeholder="Enter your postal code"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GeoErrorModal;