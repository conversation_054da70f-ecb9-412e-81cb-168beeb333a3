import { useState, useEffect } from "react"
import { UseFormReturn } from "react-hook-form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Eye, EyeOff } from "lucide-react"
import { FormData } from "@/types/auth"
import CustomButton from "../ui/CustomButton"

interface RegistrationFormProps {
  form: UseFormReturn<FormData>
  handleRegister: (data: FormData) => void
  loading: boolean
  errorMessage: string
}

export default function RegistrationForm({ form, handleRegister, loading, errorMessage }: RegistrationFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = form
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showPasswordHint, setShowPasswordHint] = useState(true)

  const password = watch("password") || ""

  useEffect(() => {
    if (password.length > 0) {
      setShowPasswordHint(false)
    } else {
      setShowPasswordHint(true)
    }
  }, [password])

  return (
    <form onSubmit={handleSubmit(handleRegister)} className="space-y-4">
     
      <div>
        <label htmlFor="email" className="block font-semibold text-[16px] text-[#000000] mb-2">
          Email
        </label>
        <Input
          id="email"
          type="email"
          placeholder="Email"
          className="w-full p-3 rounded-2xl bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
          {...register("email")}
        />
        {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
      </div>

    
      <div>
      <label htmlFor="password" className="block font-semibold text-[16px] text-[#000000] mb-2">
          Password
        </label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            className="w-full p-3 rounded-2xl bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
            {...register("password")}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-gray-400 hover:text-gray-600"
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
        </div>
        {/* {showPasswordHint && (
          <p className="text-gray-500 text-sm mt-1">
            Password must be at least 8 characters with uppercase, lowercase letters, numbers, and special characters.
          </p>
        )} */}
        {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
      </div>

     
      <div>
        <label htmlFor="confirmPassword" className="block font-semibold text-[16px] text-[#000000] mb-2">
          Confirm Password
        </label>
        <div className="relative">
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? "text" : "password"}
            placeholder="Confirm Password"
            className="w-full p-3 rounded-2xl bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
            {...register("confirmPassword")}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="text-gray-400 hover:text-gray-600"
            >
              {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
        </div>
        {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</p>}
      </div>

     
      {errorMessage && <p className="text-red-500 text-sm ">{errorMessage}</p>}

      <div style={{ marginTop: "24px" }}>
        <CustomButton
              type="submit"
              loading={loading}
             
            >
              Continue
        </CustomButton>
      </div>
    </form>
  )
}