"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import { useState, useEffect } from "react";
import DeleteConfirmationModal from "@/components/Health/DeleteConfirmationModal";
import MedicationCard from "@/components/Health/MedicationCard";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";
import { useUser } from "@/hooks/useUsers";

export default function Medication() {
  const router = useRouter();
  const { currentUser } = useUser();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [selectedMedicationId, setSelectedMedicationId] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [medicationToDelete, setMedicationToDelete] = useState<string | null>(null);
  const [medications, setMedications] = useState<MedicalRecord[]>([]);
  const [reactions, setReactions] = useState<MedicalRecord[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchMedications() {
      if (!currentUser) return;
      setLoading(true);
      const service = new OneUpHealthService({ userId: currentUser.userId });
      try {
        const records = await service.fetchMedicalRecordsFromDatabase();
        setMedications(records.filter(r => r.type === "Medication"));
        setReactions(records.filter(r => r.type === "Medication Reaction"));
      } catch {
        setMedications([]);
        setReactions([]);
      } finally {
        setLoading(false);
      }
    }
    fetchMedications();
  }, [currentUser]);

  const handleOpenMenu = (medicationId: string) => {
    setSelectedMedicationId(selectedMedicationId === medicationId ? null : medicationId);
  };

  const handleDeleteClick = (medicationId: string) => {
    setMedicationToDelete(medicationId);
    setShowDeleteModal(true);
    setSelectedMedicationId(null);
  };

  const handleConfirmDelete = () => {
    // TODO: Реалізуй видалення з бази
    setShowDeleteModal(false);
    setMedicationToDelete(null);
  };

  const menuVariants = {
    hidden: {
      opacity: 0,
      transform: "translateY(-8px)",
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      transform: "translateY(0px)",
      scale: 1,
      transition: {
        duration: 0.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      transform: "translateY(-8px)",
      scale: 0.95,
      transition: { duration: 0.15 },
    },
  };

  // Helper to format date as "Month day, year"
  const formatDate = (dateString: string) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF] relative">
      {/* Header with back button and title */}
      <div className="flex items-center justify-between pt-[51px] pl-6 pr-6 py-4 overflow-hidden">
        <div className="flex items-center">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <div className="ml-4 flex items-center">
            <h1 className="text-[16px] font-medium mr-2">Medication </h1>
          </div>
        </div>

        {/* Reaction button in top right */}
        <button
          className="flex items-center gap-1 bg-white px-4 py-2 h-[40px] w-[109px] rounded-full shadow-md"
          onClick={() => router.push("/add-medication-reaction")}
        >
          <Image
            src="/plus.svg"
            alt="Plus"
            width={20}
            height={20}
            className="w-5 h-5 flex-shrink-0"
          />
          <span className="text-[14px] font-medium ">Reaction</span>
        </button>
      </div>

      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-4">
        {/* Related Prescription */}
        <div className="bg-white rounded-3xl">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <div className="text-[#27A8E4] mr-2">
                <Image
                  src="/outpatient/bluecapsule.svg"
                  alt="Related Prescription"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <h2 className="text-[#27A8E4] text-lg font-medium">
                Current Medication
              </h2>
            </div>

            {/* Add More button */}
            <button
              className="text-black text-[16px] font-semibold"
              onClick={() => router.push("/add-medication")}
            >
              Add More
            </button>
          </div>

          {loading ? (
            <div className="text-center text-gray-400 py-8">Loading...</div>
          ) : medications.length === 0 ? (
            <div className="text-center text-gray-400 py-8">No medications found.</div>
          ) : (
            medications.map((med) => {
              // Safe access helpers
              const dosageInstruction = Array.isArray(med.rawData?.dosageInstruction)
                ? med.rawData.dosageInstruction[0]
                : undefined;
              const doseAndRate = Array.isArray(dosageInstruction?.doseAndRate)
                ? dosageInstruction.doseAndRate[0]
                : undefined;
              const timing = dosageInstruction?.timing;
              const frequency = timing?.repeat?.frequency;
              const frequencyUnit = timing?.code?.text;
              const route = dosageInstruction?.route?.text;
              const dosageValue = doseAndRate?.doseQuantity?.value;
              const dosageUnit = doseAndRate?.doseQuantity?.unit;

              let administrator = "Self";
              const recorder = med.rawData?.recorder;
              if (recorder && typeof recorder === "object" && "display" in recorder && typeof recorder.display === "string") {
                administrator = recorder.display;
              }
              return (
                <MedicationCard
                  key={med.id}
                  medicationId={med.id}
                  name={med.description}
                  dateRange={formatDate(med.date)}
                  dosage={dosageValue !== undefined ? dosageValue.toString() : ""}
                  dosageUnit={dosageUnit || ""}
                  frequency={frequency !== undefined ? frequency.toString() : ""}
                  frequencyUnit={frequencyUnit || ""}
                  route={route || ""}
                  administrator={administrator}
                  physician={med.doctor || ""}
                  selectedMedicationId={selectedMedicationId}
                  onOpenMenu={handleOpenMenu}
                  onDeleteClick={handleDeleteClick}
                  menuVariants={menuVariants}
                />
              );
            })
          )}
        </div>

        {/* Past Medication Card */}
        <div
          className="bg-white border rounded-2xl p-4 flex justify-between items-center h-[56px] cursor-pointer"
          onClick={() => router.push("/post-medication")}
        >
          <span className="text-black font-medium">Past Medication</span>
          <Image
            src="/health/Arrow-right.svg"
            alt="View"
            width={24}
            height={24}
          />
        </div>

        {/* Bottom spacer to ensure content isn't hidden by the reaction sheet */}
        <div className="h-32"></div>
      </div>

      {/* Swipeable bottom sheet for Medication Reaction */}
      <div
        className={`fixed bottom-0 left-0 right-0 bg-white rounded-t-[32px] shadow-lg transition-transform duration-300 transform z-50 ${isSheetOpen ? "translate-y-0" : "translate-y-[80%]"}`}
        style={{ height: "70%" }}
      >
        {/* Handle for swiping */}
        <div
          className="w-full flex justify-center py-4 cursor-pointer"
          onClick={() => setIsSheetOpen(!isSheetOpen)}
        >
          <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* Content */}
        <div className="px-6 pb-4 overflow-auto h-[calc(100%-30px)] bg-[#FFFFFF]">
          {/* Timeline with reaction entries */}
          <div className="border-l border-gray-300 ml-4 space-y-6">
            {loading ? (
              <div className="text-center text-gray-400 py-8">Loading...</div>
            ) : reactions.length === 0 ? (
              <div className="text-center text-gray-400 py-8">No medication reactions found.</div>
            ) : (
              reactions.map((reaction, idx) => (
                <div className="relative" key={reaction.id}>
                  <div className="absolute -left-[5px] top-2 w-[10px] h-[10px] bg-white border border-gray-300 rounded-full"></div>
                  <div className="ml-6">
                    <span className="bg-[#EDF2FD] text-[#4285F4] text-sm px-3 py-1 rounded-full">
                      {formatDate(reaction.date)}
                    </span>
                    <div className="bg-white rounded-2xl border border-gray-100 p-4 mt-2">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-medium">
                          Medication Reaction
                        </span>
                        <Image
                          src="/health/Arrow-right.svg"
                          alt="View details"
                          width={24}
                          height={24}
                          className="cursor-pointer"
                        />
                      </div>
                      <div className="flex mt-2 items-center text-gray-600">
                        <Image
                          src="/medical-record/note.svg"
                          alt="Description"
                          width={16}
                          height={16}
                          className="mr-2"
                        />
                        <span className="text-sm">{reaction.description}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <DeleteConfirmationModal
          onConfirm={handleConfirmDelete}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
    </div>
  );
}
