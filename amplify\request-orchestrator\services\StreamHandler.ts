import { AiService } from "./AiService";
import { EventManager } from "./EventManager";
import { generateClient } from "aws-amplify/api";
import type { Schema } from "../../data/resource";
import { AI_ASSISTANT_ID } from "../../../constants/chat";
import { TokenQueue } from "./TokenQueue";

const client = generateClient<Schema>();
const MESSAGE_TYPE_TEXT = "TEXT";

export interface StreamState {
  completeResponse: string;
  isStreamingComplete: boolean;
  lastTokenTime: number;
  tokenCount: number;
}

export interface StreamProcessOptions {
  prompt: string;
  formattedChatHistory: any[];
  chatId: string;
  eventManager: EventManager;
  onSuccess: (
    messageId: string,
    chatMessage: any,
    completeResponse: string
  ) => Promise<any>;
  showMap?: boolean;
}

export class StreamHandler {
  private aiService: AiService;

  constructor(aiService: AiService) {
    this.aiService = aiService;
  }

  async processStream(options: StreamProcessOptions): Promise<any> {
    const {
      prompt,
      formattedChatHistory,
      chatId,
      eventManager,
      onSuccess,
      showMap = false,
    } = options;

    const streamState: StreamState = {
      completeResponse: "",
      isStreamingComplete: false,
      lastTokenTime: Date.now(),
      tokenCount: 0,
    };

    const tokenQueue = new TokenQueue(async (text) => {
      await eventManager.sendToken(text);
      streamState.completeResponse += text;
    });

    try {
      const result = await this.startAiStream(
        prompt,
        formattedChatHistory,
        tokenQueue,
        streamState,
        async (fullText: string) => {
          let finalMessage =
            fullText ||
            streamState.completeResponse ||
            "Processing your question...";

          if (showMap) {
            if (finalMessage.includes("?")) {
              console.warn(
                "[StreamHandler] Diagnosis message contains questions, which should be avoided"
              );

              finalMessage = finalMessage
                .split(".")
                .filter((sentence) => !sentence.includes("?"))
                .join(".");

              if (finalMessage.trim().length === 0) {
                finalMessage =
                  "Based on the information provided, a preliminary assessment has been prepared. Please review the diagnostic map.";
              }

              console.log(
                "[StreamHandler] Filtered question content from diagnosis message"
              );
            }
          }

          const { data: chatMessage } = await client.models.ChatMessage.create({
            chatId: chatId,
            userId: AI_ASSISTANT_ID,
            message: finalMessage,
            messageType: MESSAGE_TYPE_TEXT,
            attachments: [],
            showMap,
          });

          const messageId = chatMessage?.id || "";
          await eventManager.sendComplete(messageId, finalMessage);

          return await onSuccess(
            messageId,
            chatMessage,
            streamState.completeResponse
          );
        }
      );

      return result;
    } catch (error) {
      if (!tokenQueue.isEmpty()) {
        await tokenQueue.process();
      }

      await eventManager.sendError(
        "Sorry, something went wrong. Please try again."
      );
      throw error;
    }
  }

  private async startAiStream(
    prompt: string,
    formattedChatHistory: any[],
    tokenQueue: TokenQueue,
    streamState: StreamState,
    onComplete: (fullText: string) => Promise<any>
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.aiService.getChatCompletionStream(prompt, formattedChatHistory, {
        onToken: async (token) => {
          if (!token) return;

          streamState.tokenCount++;
          streamState.lastTokenTime = Date.now();

          tokenQueue.add(token);

          // Process the queue periodically instead of every token
          if (streamState.tokenCount % 20 === 0) {
            // Log before processing the batch
            console.log(
              `[StreamHandler] Triggering batch process at ${streamState.tokenCount} tokens (Queue size: ${tokenQueue.size()})`
            );
            tokenQueue.process().catch((err) => {
              console.error(
                "[StreamHandler] Periodic token processing error:",
                err
              );
            });
          }

          // Keep the overall progress log if desired
          // if (streamState.tokenCount % 20 === 0) {
          //   console.log(
          //     `[StreamHandler] Processed ${streamState.tokenCount} tokens, queue size: ${tokenQueue.size()}`
          //   );
          // }
        },
        onComplete: async (fullText) => {
          console.log(
            `[StreamHandler] Stream complete: Queue has ${tokenQueue.size()} tokens to process before final check.`
          );

          // Ensure any remaining tokens are processed
          if (!tokenQueue.isEmpty()) {
            console.log(
              `[StreamHandler] Processing final ${tokenQueue.size()} tokens from queue.`
            );
            await tokenQueue.process(); // Process remaining tokens before the final check
          }

          // This check might still be useful as a fallback, but ideally shouldn't be needed often
          if (fullText && fullText !== streamState.completeResponse) {
            console.warn(
              `[StreamHandler] Discrepancy detected. Full text length: ${fullText.length}, Accumulated length: ${streamState.completeResponse.length}. Adding missing content.`
            );
            const missingContent = fullText.slice(
              streamState.completeResponse.length
            );
            // Add missing content and process immediately
            await tokenQueue.add(missingContent);
            await tokenQueue.process();
          }

          // Update completeResponse *after* all processing attempts
          streamState.completeResponse =
            fullText || streamState.completeResponse;

          console.log(
            `[StreamHandler] Stream completed successfully. Final token count: ${streamState.tokenCount}. Final accumulated length: ${streamState.completeResponse.length}`
          );
          streamState.isStreamingComplete = true;

          try {
            const result = await onComplete(streamState.completeResponse); // Pass the potentially corrected response
            resolve(result);
          } catch (error) {
            console.error(
              "[StreamHandler] Error during onComplete callback:",
              error
            );
            reject(error);
          }
        },
        onError: async (error) => {
          console.error(
            "[StreamHandler] Received error from AiService:",
            error
          );
          // Attempt to process any remaining tokens in the queue on error
          if (!tokenQueue.isEmpty()) {
            console.log(
              `[StreamHandler] Processing ${tokenQueue.size()} tokens from queue after error.`
            );
            try {
              await tokenQueue.process();
            } catch (processError) {
              console.error(
                "[StreamHandler] Error processing final tokens after stream error:",
                processError
              );
            }
          }
          reject(error); // Reject the promise to signal the error upstream
        },
      });
    });
  }
}
