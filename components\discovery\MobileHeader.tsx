"use client";

import { useEffect, useRef } from "react";

import Image from "next/image";
import Link from "next/link";
import Moment from "./Moment";
import { useRouter } from "next/navigation";

interface MobileHeaderProps {
  title: string;
  tabs: {
    id: string;
    label: string;
  }[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  posts?: any[];
  loadMorePosts: () => void;
  isLoadingPosts: boolean;
  nextToken: string | null;
}

export default function MobileHeader({
  title,
  tabs,
  activeTab,
  onTabChange,
  posts = [],
  loadMorePosts,
  isLoadingPosts,
  nextToken,
}: MobileHeaderProps) {
  const router = useRouter();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  const navigateToCommunity = () => {
    router.push("/comunity");
  };

  useEffect(() => {
    console.log("MobileHeader: nextToken =", nextToken, "isLoadingPosts =", isLoadingPosts); // Debug log
    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && nextToken && !isLoadingPosts) {
          console.log("MobileHeader: Triggering loadMorePosts"); // Debug log
          loadMorePosts();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      console.log("MobileHeader: Observing loadMoreRef"); // Debug log
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current && loadMoreRef.current) {
        observerRef.current.unobserve(loadMoreRef.current);
      }
    };
  }, [nextToken, isLoadingPosts, loadMorePosts]);

  return (
    <div className="relative block md:hidden">
      {/* Mobile Header */}
      <div className="bg-[#E9F0FF] h-[120px] flex flex-col justify-end">
        <div className="px-4 pb-[31px] flex justify-between items-center">
          <h1 className="text-[24px] font-normal text-gray-800">{title}</h1>
          <div className="flex space-x-4">
            <Link
              href="/comunity/create"
              className="w-[40px] h-[40px] bg-[#FFFFFF] rounded-2xl flex items-center mt-[5px] justify-center"
            >
              <Image src={"/grayplus.svg"} width={24} height={24} alt="ds" />
            </Link>
            <button
              onClick={navigateToCommunity}
              className="w-[40px] h-[40px] bg-[#FFFFFF] rounded-2xl mt-[5px] flex items-center justify-center"
            >
              <Image src={"/menu-deepg.svg"} width={24} height={24} alt="ds" />
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white p-4 flex justify-center space-x-4 rounded-t-2xl -mt-4 relative z-0">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`flex-1 py-2 px-4 text-center rounded-full relative ${
              activeTab === tab.id
                ? "bg-[#E9F0FF] text-[#5185FF]"
                : "bg-gray-100 text-gray-800"
            }`}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Moments/Content Section */}
      {activeTab === "moments" && (
        <div className="bg-white rounded-b-2xl shadow-sm overflow-auto h-[calc(100vh-260px)]">
          {posts.map((post) => (
            <Moment
              key={post.id}
              moment={post}
              onCommentAdd={post.onCommentAdd}
              onBookmarkChange={post.onBookmarkChange}
            />
          ))}
          {posts.length > 0 && (
            <div ref={loadMoreRef} className="h-10">
              {isLoadingPosts && (
                <div className="text-center py-4">Loading more posts...</div>
              )}
              {!isLoadingPosts && !nextToken && (
                <div className="text-center py-4"></div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}