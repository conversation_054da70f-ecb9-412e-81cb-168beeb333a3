interface CommunityRulesModalProps {
  showRules: boolean;
  setShowRules: (show: boolean) => void;
  rules: string[];
}

export default function CommunityRulesModal({
  showRules,
  setShowRules,
  rules
}: CommunityRulesModalProps) {
  if (!showRules) return null;
  
  return (
    <div className="md:hidden fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white w-full max-w-md rounded-lg p-5 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">Community Rules</h3>
          <button onClick={() => setShowRules(false)} className="text-gray-500">
            ✕
          </button>
        </div>
        {rules && rules.length > 0 ? (
          <ul className="space-y-2">
            {rules.map((rule, i) => (
              <li key={i} className="pb-2 border-b border-gray-100">
                <span className="font-medium">Rule {i+1}:</span> {rule}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500">No rules have been set for this community.</p>
        )}
      </div>
    </div>
  );
}
