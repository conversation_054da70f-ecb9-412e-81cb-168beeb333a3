# 1upHealth API Integration

This document provides information on how the 1upHealth API has been integrated into the Jinix application to connect to patient health records.

## Overview

The integration with 1upHealth allows users to:

1. Connect to their healthcare providers
2. Import their medical records
3. View their health data including:
   - Patient demographics
   - Medical conditions
   - Observations (vitals, lab results)
   - Medications

## Architecture

The integration is built using:

- AWS Lambda functions for secure API interaction
- Amplify GraphQL API for client-server communication
- React components for user interface

### Lambda Functions

Two Lambda functions have been created:

1. **oneup-health-connect**: Handles the connection flow to healthcare providers

   - Creating 1upHealth users
   - Generating authorization URLs
   - Handling OAuth callbacks
   - Managing user connections

2. **oneup-health-data**: Extracts and processes health data
   - Fetching patient demographics
   - Retrieving observations (vitals, lab results)
   - Accessing condition information
   - Getting medication data

### Data Models

The following data models have been added to store 1upHealth data:

- `PatientProfile`: Stores patient demographic information
- `HealthObservation`: Stores vital signs and lab results
- `MedicalCondition`: Stores diagnoses and problems
- `Medication`: Stores medication information
- `HealthDataConnection`: Tracks provider connections

## Usage

### Authentication Flow

1. User initiates a connection to a healthcare provider
2. User is redirected to the provider's login page
3. After successful authentication, user is redirected back with an authorization code
4. The code is exchanged for an access token
5. The connection is saved in the database

### Data Retrieval Flow

1. For a connected user, initiate data retrieval
2. Lambda functions query the 1upHealth FHIR API endpoints
3. Data is processed and stored in appropriate database tables
4. Data is made available to the frontend for display

## API Reference

### GraphQL Mutations

#### Connect to 1upHealth

```graphql
mutation ConnectOneUpHealth(
  $userId: String!
  $action: String!
  $clientId: String!
  $clientSecret: String!
  $code: String
  $data: AWSJSON
) {
  connectOneUpHealth(
    userId: $userId
    action: $action
    clientId: $clientId
    clientSecret: $clientSecret
    code: $code
    data: $data
  )
}
```

#### Fetch Health Data

```graphql
mutation FetchOneUpHealthData(
  $userId: String!
  $action: String!
  $clientId: String!
  $clientSecret: String!
  $patientId: String
  $resourceType: String
) {
  fetchOneUpHealthData(
    userId: $userId
    action: $action
    clientId: $clientId
    clientSecret: $clientSecret
    patientId: $patientId
    resourceType: $resourceType
  )
}
```

## Configuration

To use this integration, you need:

1. A 1upHealth developer account
2. Client ID and Client Secret from 1upHealth
3. Configure these as secrets in your Amplify backend:

```bash
npx ampx secrets set ONEUP_CLIENT_ID
npx ampx secrets set ONEUP_CLIENT_SECRET
```

## Security Considerations

- Client credentials are never exposed in frontend code
- All API calls to 1upHealth are made from secure Lambda functions
- OAuth tokens are securely stored in the database
- User health data is protected by appropriate access controls

## Limitations

- FHIR support is currently for DSTU2
- Some healthcare providers may have limited data available
- Real-time data synchronization is not supported in this version

## FHIR Resources Supported

- Patient
- Observation
- Condition
- Medication
- MedicationOrder

## Future Enhancements

- Add support for additional FHIR resources
- Implement real-time data synchronization
- Add data visualization tools
- Enhance user permissions and data sharing options
