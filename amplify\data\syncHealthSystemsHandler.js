import { util } from '@aws-appsync/utils';

export function request(ctx) {
  const now = util.time.nowISO8601();

  return {
    operation: 'BatchPutItem',
    tables: {
      [`HealthSystem-${ctx.stash.awsAppsyncApiId}-${ctx.stash.amplifyApiEnvironmentName}`]: ctx.args.contents.map((systemJson) => {
        const system = JSON.parse(systemJson);
        return util.dynamodb.toMapValues({
          id: system.id,
          name: system.name,
          logo: system.logo,
          ehr: system.ehr,
          status: system.status,
          locations: system.locations || [],
          createdAt: system.createdAt,
          updatedAt: system.updatedAt,
        });
      }),
    },
  };
}

export function response(ctx) {
  if (ctx.error) {
    util.error(ctx.error.message, ctx.error.type);
  }
  return true;
}