/**
 * Context interface for sharing state between QuestionnaireService and strategies
 */
export interface Context {
  /**
   * Metadata containing questionnaire state and configuration
   * This exactly matches the Chat metadata schema in the database
   */
  metadata: {
    chatId?: string;
    createdBy?: string;
    isArchived?: boolean | null;
    category?: string | null;
    configuration?: any;
    currentQuestionIndex: number;
    isQuestionnaireComplete?: boolean;
    questionnaireCompletedAt?: string | null;
    diagnosisMessageSent?: boolean | null;
    questionnaireId?: string;

    earlyCompletion?: boolean;
  };

  /**
   * Chat history for the current session
   */
  chatHistory: any[];

  /**
   * Loaded questionnaire questions
   */
  questions: { prompt: string }[];
}
