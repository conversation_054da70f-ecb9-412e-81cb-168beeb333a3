import { generateClient } from "@aws-amplify/api";
import { AI_ASSISTANT_ID } from "../../../constants/chat";
import { Context } from "../interfaces/QuestionnaireContext";
import { QuestionnaireStrategy } from "../interfaces/QuestionnaireStrategy";
import { AiService } from "../services/AiService";
import { EventManager } from "../services/EventManager";
import { StreamHandler } from "../services/StreamHandler";
import { MedicalProfileService } from "../services/MedicalProfileService";
import { formatChatHistory } from "../utils/chat-utils";
import type { Schema } from "../../data/resource";

const client = generateClient<Schema>();

export class MedicalProfileStrategy implements QuestionnaireStrategy {
  private aiService: AiService;
  private streamHandler: StreamHandler;
  private medicalProfileService: MedicalProfileService;

  constructor(aiService: AiService) {
    this.aiService = aiService;
    this.streamHandler = new StreamHandler(aiService);
    this.medicalProfileService = new MedicalProfileService(aiService);
  }

  async initialize(context: Context): Promise<void> {
    // Ensure this is a medical profile questionnaire (ID 2)
    const questionnaireId = context.metadata.questionnaireId || "2";
    if (questionnaireId !== "2") {
      console.log(
        `[MedicalProfileStrategy] This strategy is for medical profile only (ID 2), received ${questionnaireId}`
      );
      context.questions = [];
      return;
    }

    // Set questionnaireId to ensure proper tracking
    context.metadata.questionnaireId = "2";

    // Similar implementation as HealthQuestionnaireStrategy
    if (context.metadata.isQuestionnaireComplete) {
      console.log(
        "[MedicalProfileStrategy] Questionnaire is complete - skipping question load"
      );
      context.questions = [];
      return;
    }

    try {
      // Ensure this strategy only handles medical profile questionnaires (ID 2)
      const questionnaireId = context.metadata.questionnaireId || "2";
      if (questionnaireId !== "2") {
        console.log(
          `[MedicalProfileStrategy] This strategy is for medical profile questionnaires only (ID 2), received ${questionnaireId}`
        );
        context.questions = [];
        return;
      }

      console.log(
        `[MedicalProfileStrategy] Loading questions for questionnaire ID: ${questionnaireId}`
      );

      const { data: questionnaireQuestions } =
        await client.models.QuestionnaireQuestion.listQuestionnaireQuestionsBySequence(
          { questionnaireId },
          { sortDirection: "ASC" }
        );

      if (!questionnaireQuestions || questionnaireQuestions.length === 0) {
        console.error(
          "[MedicalProfileStrategy] No questionnaire questions found"
        );
        return;
      }

      console.log(
        `[MedicalProfileStrategy] Found ${questionnaireQuestions.length} questionnaire questions`
      );

      const questions = [];
      for (const item of questionnaireQuestions) {
        const { data: healthQuestion } = await client.models.HealthQuestion.get(
          {
            id: item.healthQuestionId,
          }
        );

        if (healthQuestion && healthQuestion.prompt) {
          questions.push({ prompt: healthQuestion.prompt });
        }
      }

      console.log(
        `[MedicalProfileStrategy] Successfully loaded ${questions.length} health questions`
      );
      context.questions = questions;

      context.metadata.currentQuestionIndex =
        context.metadata.currentQuestionIndex || 0;

      if (
        context.metadata.currentQuestionIndex >= context.questions.length &&
        context.questions.length > 0
      ) {
        context.metadata.isQuestionnaireComplete = true;
      }
    } catch (error) {
      console.error("[MedicalProfileStrategy] Error loading questions:", error);
      throw error;
    }
  }

  async handleResponse(
    userResponse: string,
    context: Context,
    eventManager: EventManager
  ): Promise<{
    nextQuestion: string | null;
    isComplete: boolean;
    messageId?: string;
    chatMessage?: any;
    profileData?: any;
  }> {
    // Similar implementation as HealthQuestionnaireStrategy
    const currentQuestionIndex = context.metadata.currentQuestionIndex || 0;

    // Check if questionnaire is complete
    if (
      currentQuestionIndex >= context.questions.length ||
      context.metadata.isQuestionnaireComplete
    ) {
      if (!context.metadata.chatId) {
        console.error(
          "[MedicalProfileStrategy] No chatId found in context metadata"
        );
        throw new Error("No chatId found in context metadata");
      }

      console.log(
        "[MedicalProfileStrategy] Questionnaire complete. Generating medical profile"
      );
      try {
        const profileData = await this.medicalProfileService.generateProfile(
          context.metadata.chatId
        );

        // Inform the user that their profile has been created
        const summaryMessage = `Thank you for providing your information. I've created a medical profile based on our conversation. This will help us provide better care for you in future interactions.`;

        await this.streamHandler.processStream({
          prompt: summaryMessage,
          formattedChatHistory: formatChatHistory(context.chatHistory),
          chatId: context.metadata.chatId,
          eventManager,
          onSuccess: async (messageId, chatMessage) => {
            console.log(
              `[MedicalProfileStrategy] Profile summary sent with ID: ${messageId}`
            );
            return {
              nextQuestion: null,
              isComplete: true,
              messageId,
              chatMessage,
            };
          },
        });

        return {
          nextQuestion: null,
          isComplete: true,
          profileData,
        };
      } catch (error) {
        console.error(
          "[MedicalProfileStrategy] Error generating profile:",
          error
        );
        await eventManager.sendError(
          "Sorry, there was a problem creating your medical profile. Our team will look into this."
        );
        throw error;
      }
    }

    // Process the next question
    const nextQuestion = context.questions[currentQuestionIndex];
    if (!nextQuestion) {
      await eventManager.sendError(
        "Sorry, there was a problem with the questionnaire. Please try again."
      );
      throw new Error(`No question found at index ${currentQuestionIndex}`);
    }

    console.log(
      `[MedicalProfileStrategy] Processing question #${currentQuestionIndex + 1}: ${nextQuestion.prompt}`
    );

    const prompt = this.formatQuestionPrompt(nextQuestion.prompt);
    const formattedChatHistory = formatChatHistory(context.chatHistory);
    if (!context.metadata.chatId) {
      console.error(
        "[MedicalProfileStrategy] No chatId found in context metadata"
      );
      throw new Error("No chatId found in context metadata");
    }

    try {
      const result = await this.streamHandler.processStream({
        prompt,
        formattedChatHistory,
        chatId: context.metadata.chatId,
        eventManager,
        onSuccess: async (messageId, chatMessage) => {
          console.log(
            `[MedicalProfileStrategy] Question sent with ID: ${messageId}`
          );
          return {
            nextQuestion: prompt,
            isComplete: false,
            messageId,
            chatMessage,
          };
        },
      });

      context.metadata.currentQuestionIndex = currentQuestionIndex + 1;

      if (context.metadata.currentQuestionIndex >= context.questions.length) {
        console.log("[MedicalProfileStrategy] That was the final question");
        context.metadata.isQuestionnaireComplete = true;
      }

      return result;
    } catch (error) {
      console.error(
        "[MedicalProfileStrategy] Error processing question:",
        error
      );
      throw error;
    }
  }

  async onQuestionnaireComplete(
    chatId: string,
    context: Context,
    prompt: string,
    eventManager: EventManager
  ): Promise<{ enhancedPrompt: string; showSpecialUI?: boolean }> {
    console.log(
      "[MedicalProfileStrategy] Generating medical profile after questionnaire completion"
    );

    try {
      const profileData =
        await this.medicalProfileService.generateProfile(chatId);
      console.log(
        "[MedicalProfileStrategy] Medical profile generated successfully"
      );

      // Create a summary message instead of a diagnosis
      const enhancedPrompt =
        "Thank you for providing your information. I've created a medical profile based on our conversation. This will help us provide better care for you in future interactions.";

      return {
        enhancedPrompt,
        showSpecialUI: false, // No special UI for profile completion
      };
    } catch (error) {
      console.error(
        "[MedicalProfileStrategy] Error generating profile:",
        error
      );
      return {
        enhancedPrompt: prompt, // Fall back to original prompt
        showSpecialUI: false,
      };
    }
  }

  private formatQuestionPrompt(questionText: string): string {
    return `You are a medical assistant collecting information for a patient's medical profile. Your goal is to gather comprehensive information about the patient's health and medical history.

Please rephrase the following profile question in a friendly, conversational manner:

Question concept: "${questionText}"

Your response must:
1. Sound natural, as if a doctor is speaking directly to the patient
2. Be phrased as a clear question ending with a question mark
3. Be concise (maximum 2-3 sentences)
4. Not contain medical jargon unless necessary
5. Not include explanatory text or additional comments

Format your response as a direct question only, with no other text or context.`;
  }
}
