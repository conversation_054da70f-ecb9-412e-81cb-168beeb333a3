"use client";

import React, { Suspense, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { confirmResetPassword } from "@aws-amplify/auth";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useWindowSize } from "@/hooks/useWindowSize";
import CustomButton from "@/components/ui/CustomButton";
import { EyeOff, Eye, ArrowLeft } from "lucide-react";
import RightSide from "@/components/auth/RightSide";

const resetPasswordSchema = z
  .object({
    code: z
      .string()
      .min(6, "Verification code must be at least 6 characters")
      .nonempty("Verification code is required")
      .optional(),
    newPassword: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[0-9]/, "Password must contain at least one number")
      .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character"),
    confirmPassword: z.string().nonempty("Confirm password is required"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

interface ResetPasswordForm {
  code?: string;
  newPassword: string;
  confirmPassword: string;
}

const ResetPassword = () => {
  const isDesktop = useWindowSize();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isCodeVerified, setIsCodeVerified] = useState(false);
  const [isInvalidCode, setIsInvalidCode] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const router = useRouter();
  const searchParams = useSearchParams();
  const emailFromParams = searchParams?.get("email") || "";

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<ResetPasswordForm>({
    resolver: zodResolver(resetPasswordSchema),
    mode: "onSubmit",
  });

  const verifyCode = async () => {
    const code = inputRefs.current
      .map((input) => input?.value || "")
      .join("");

    if (!code || code.length !== 6) {
      setIsInvalidCode(true);
      setErrorMessage("Please enter a valid 6-digit verification code");
      return;
    }

    setLoading(true);
    setErrorMessage("");
    setIsInvalidCode(false);

    try {
      setTimeout(() => {
        setIsCodeVerified(true);
        setLoading(false);
        setValue("code", code);
        
      }, 1000);
    } catch (error) {
      setLoading(false);
      setIsInvalidCode(true);
      setErrorMessage("Invalid verification code");
    }
  };

  const handleCodeChange = (index: number, value: string) => {
    if (value.length === 1 && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    const newCode = inputRefs.current
      .map((input) => input?.value || "")
      .join("");

    const event = {
      target: { value: newCode },
    } as React.ChangeEvent<HTMLInputElement>;
    register("code").onChange(event);
    setValue("code", newCode);
  };

  const handlePaste = (startIndex: number, e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").slice(0, 6);

    [...pastedData].forEach((char, index) => {
      const currentIndex = startIndex + index;
      if (currentIndex < 6 && inputRefs.current[currentIndex]) {
        inputRefs.current[currentIndex]!.value = char;
        if (currentIndex < 5) {
          inputRefs.current[currentIndex + 1]?.focus();
        }
      }
    });

    const event = {
      target: { value: pastedData },
    } as React.ChangeEvent<HTMLInputElement>;
    register("code").onChange(event);
    setValue("code", pastedData);
  };

  const onSubmit = async (data: ResetPasswordForm) => {
  

    if (!isCodeVerified) {
      setErrorMessage("Please verify your code first");
      return;
    }

    if (!emailFromParams) {
      setErrorMessage("Email is missing. Please provide an email to reset the password.");
      return;
    }

    try {
      setLoading(true);
      setErrorMessage("");
      setSuccessMessage("");



      await confirmResetPassword({
        username: emailFromParams,
        confirmationCode: data.code!,
        newPassword: data.newPassword,
      });

      setLoading(false);
      setSuccessMessage("Password has been successfully reset. Redirecting to login...");
      setTimeout(() => router.push("/login"), 2000);
    } catch (error) {
      setLoading(false);
      setIsCodeVerified(false);
     
      if (error instanceof Error) {
        setErrorMessage("Error resetting password: " + error.message);
      } else {
        setErrorMessage("An unexpected error occurred. Please try again.");
      }
    }
  };

  return (
    <main className="h-full flex flex-col">
      {isDesktop ? (
        // Desktop Layout
        <div className="h-full flex">
          {/* Left side */}
          <div className="hidden md:flex w-full md:w-1/2 flex-col justify-start h-full px-8 lg:px-16 xl:px-28 2xl:px-[140px] py-8 lg:pt-32 lg:pb-[258px] bg-white overflow-hidden rounded-3xl">
            <div className="flex">
              <p className="text-[#FFB300] text-lg font-medium mb-2">jin</p>
              <p className="text-[#4187FF] text-lg font-medium mb-2">I</p>
              <p className="text-[#D93C85] text-lg font-medium mb-2">X</p>
            </div>
            
            {!isCodeVerified ? (
              <div>
                <h2 className="text-start text-3xl text-gray-900 mb-2">Verify Email</h2>
                <p className="text-start text-sm text-gray-600 mb-8 leading-relaxed   max-w-[494px]  w-full">
                  For your security, we’ve sent a verification code to your email address. Enter the code below to continue with resetting your password.
                </p>
              </div>
            ) : (
              <div>
                <h2 className="text-start text-3xl text-gray-900 mb-2">Reset Password</h2>
                <p className="text-start text-sm text-gray-600 mb-8 leading-relaxed">
                  Use at least 8 characters, with one uppercase letter, one number, and one special character (like ! @ # $)
                </p>
              </div>
            )}
            {errorMessage && <p className="text-red-500 text-sm mb-4">{errorMessage}</p>}
            {successMessage && <p className="text-green-500 text-sm mb-4">{successMessage}</p>}

            <form onSubmit={handleSubmit(onSubmit)}>
              {!isCodeVerified ? (
                <div>
                  <div className="mb-4 flex justify-start space-x-1 sm:space-x-2">
                    {[...Array(6)].map((_, index) => (
                      <input
                        key={index}
                        type="text"
                        maxLength={1}
                        className={`w-full max-w-[60px] h-[60px] sm:max-w-[70px] sm:h-[70px] md:max-w-[76px] md:h-[76px] text-center text-lg border rounded-xl focus:outline-none bg-white shadow-sm
                          ${loading ? "bg-gray-100 border-gray-200 text-gray-400" : isInvalidCode ? "border-2 border-red-500 rounded-md" : "border-gray-200 focus:border-blue-600"}`}
                        ref={(el) => {
                          inputRefs.current[index] = el;
                        }}
                        onChange={(e) => handleCodeChange(index, e.target.value)}
                        onPaste={(e) => handlePaste(index, e)}
                        onKeyDown={(e) => {
                          if (e.key === "Backspace" && !inputRefs.current[index]?.value && index > 0) {
                            inputRefs.current[index - 1]?.focus();
                          }
                        }}
                        disabled={loading}
                      />
                    ))}
                    <input type="hidden" {...register("code")} />
                  </div>
                  <CustomButton 
                    type="button" 
                    onClick={verifyCode} 
                    loading={loading} 
                    className="max-w-[494px] h-[46px] w-full"
                    style={{ minHeight: "46px" }}>
                    Verify Code
                  </CustomButton>
                </div>
              ) : (
                <>
                  <div className="mb-4">
                    <div className="relative">
                      <input
                        id="newPassword"
                        placeholder="Password"
                        type={showPassword ? "text" : "password"}
                        className="w-full p-3 rounded-full bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900 placeholder-black"
                        {...register("newPassword")}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" onClick={() => setShowPassword(!showPassword)} />
                        ) : (
                          <Eye className="h-5 w-5" onClick={() => setShowPassword(!showPassword)} />
                        )}
                      </div>
                    </div>
                    {errors.newPassword && <p className="text-red-500 text-sm mt-1">{errors.newPassword.message}</p>}
                  </div>

                  <div className="mb-6">
                    <div className="relative">
                      <input
                        id="confirmPassword"
                        placeholder="Confirm Password"
                        type={showConfirmPassword ? "text" : "password"}
                        className="w-full p-3 rounded-full bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900 placeholder-black"
                        {...register("confirmPassword")}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        {showConfirmPassword ? (
                          <EyeOff className="h-5 w-5" onClick={() => setShowConfirmPassword(!showConfirmPassword)} />
                        ) : (
                          <Eye className="h-5 w-5" onClick={() => setShowConfirmPassword(!showConfirmPassword)} />
                        )}
                      </div>
                    </div>
                    {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</p>}
                  </div>

                  <CustomButton type="submit" loading={loading || isSubmitting} disabled={loading || isSubmitting}>
                    Reset Password
                  </CustomButton>
                </>
              )}
            </form>
          </div>

          {/* Right side */}
          <RightSide />
        </div>
      ) : (
        // Mobile Layout
        <div className="md:hidden flex flex-col items-center justify-start min-h-screen h-screen max-h-screen p-5 pt-[68px] overflow-hidden bg-white">
          <div className="absolute top-5 left-5 mb-8">
              <ArrowLeft className="w-6 h-6 cursor-pointer" onClick={() => router.back()} />
          </div>
          <div className="w-full max-w-md">
            {!isCodeVerified ? (
              <div>
                <h2 className="text-start text-3xl text-gray-900 mb-2">Verify Email</h2>
                <p className="text-start text-xs text-gray-600 mb-8 leading-relaxed max-w-[390px] w-full">
                  Vorem ipsum dolor sit amet, consectetur adipiscing elit. Vorem ipsum dolor sit amet, consectetur adipiscing elit
                </p>
              </div>
            ) : (
              <div>
                <h2 className="text-start text-3xl text-gray-900 mb-2">Reset Password</h2>
                <p className="text-start text-sm text-gray-600 mb-8 leading-relaxed">
                  Use at least 8 characters, with one uppercase letter, one number, and one special character (like ! @ # $)
                </p>
              </div>
            )}
            {errorMessage && <p className="text-red-500 text-sm mb-4 text-center">{errorMessage}</p>}
            {successMessage && <p className="text-green-500 text-sm mb-4 text-center">{successMessage}</p>}

            <form onSubmit={handleSubmit(onSubmit)}>
              {!isCodeVerified ? (
                <div>
                  <div className="mb-4 flex justify-start space-x-2">
                    {[...Array(6)].map((_, index) => (
                      <input
                        key={index}
                        type="text"
                        maxLength={1}
                        className={`w-full max-w-[58px] h-[61px] xs:max-w-[52px] xs:h-[52px] sm:max-w-[58px] sm:h-[61px] text-center text-lg border rounded-xl focus:outline-none bg-white shadow-sm
                          ${loading ? "bg-gray-100 border-gray-200 text-gray-400" : isInvalidCode ? "border-2 border-red-500 rounded-md" : "border-gray-200 focus:border-blue-600"}`}
                        ref={(el) => {
                          inputRefs.current[index] = el;
                        }}
                        onChange={(e) => handleCodeChange(index, e.target.value)}
                        onPaste={(e) => handlePaste(index, e)}
                        onKeyDown={(e) => {
                          if (e.key === "Backspace" && !inputRefs.current[index]?.value && index > 0) {
                            inputRefs.current[index - 1]?.focus();
                          }
                        }}
                        disabled={loading}
                      />
                    ))}
                    <input type="hidden" {...register("code")} />
                  </div>
                  <CustomButton 
                    type="button" 
                    onClick={verifyCode} 
                    loading={loading} 
                    className="max-w-[390px] h-[46px] w-full"
                    style={{ minHeight: "46px" }}>
                    Verify Code
                  </CustomButton>
                </div>
              ) : (
                <>
                  <div className="mb-4">
                    <div className="relative">
                      <input
                        id="newPassword"
                        placeholder="Password"
                        type={showPassword ? "text" : "password"}
                        className="w-full p-3 h-[43px] rounded-2xl bg-[#F5F5F54D] border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900 placeholder-black"
                        {...register("newPassword")}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" onClick={() => setShowPassword(!showPassword)} />
                        ) : (
                          <Eye className="h-5 w-5" onClick={() => setShowPassword(!showPassword)} />
                        )}
                      </div>
                    </div>
                    {errors.newPassword && <p className="text-red-500 text-sm mt-1">{errors.newPassword.message}</p>}
                  </div>

                  <div className="mb-6">
                    <div className="relative">
                      <input
                        id="confirmPassword"
                        placeholder="Confirm Password"
                        type={showConfirmPassword ? "text" : "password"}
                        className="w-full p-3 h-[43px] rounded-2xl bg-[#F5F5F54D] border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900 placeholder-black"
                        {...register("confirmPassword")}
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        {showConfirmPassword ? (
                          <EyeOff className="h-5 w-5" onClick={() => setShowConfirmPassword(!showConfirmPassword)} />
                        ) : (
                          <Eye className="h-5 w-5" onClick={() => setShowConfirmPassword(!showConfirmPassword)} />
                        )}
                      </div>
                    </div>
                    {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword.message}</p>}
                  </div>

                  <CustomButton type="submit" loading={loading || isSubmitting} disabled={loading || isSubmitting}>
                    Reset Password
                  </CustomButton>
                </>
              )}
            </form>
          </div>
        </div>
      )}
    </main>
  );
};

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<p>Loading...</p>}>
      <ResetPassword />
    </Suspense>
  );
}