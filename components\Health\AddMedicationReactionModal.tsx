'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import CustomButton from '@/components/ui/CustomButton';

interface AddMedicationReactionModalProps {
  onClose: () => void;
  onSave: (reactionData: any) => void;
}

const AddMedicationReactionModal = ({ onClose, onSave }: AddMedicationReactionModalProps) => {
  const [showMedicationOptions, setShowMedicationOptions] = useState(false);
  const [formData, setFormData] = useState({
    relatedMedication: '',
    reaction: '',
    date: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectMedication = (medication: string) => {
    setFormData(prev => ({ ...prev, relatedMedication: medication }));
    setShowMedicationOptions(false);
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl w-[600px] max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-5 border-b">
          <h2 className="text-lg font-medium">Medication Reaction</h2>
          <button onClick={onClose}>
            <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
          </button>
        </div>

        {/* Form fields */}
        <div className="p-6">
          {/* Related Medication */}
          <div className="mb-6">
            <label className="block mb-2 font-medium">Related Medication</label>
            <div className="relative">
              <Input
                type="text"
                name="relatedMedication"
                value={formData.relatedMedication}
                onChange={handleChange}
                placeholder="e.g., Hydroxychloroquine"
                className="w-full bg-[#F5F5F5] border-none pr-10"
                onClick={() => setShowMedicationOptions(!showMedicationOptions)}
              />
              <Image
                src="/health/chevron-down.svg"
                alt="Dropdown"
                width={24}
                height={24}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                onClick={() => setShowMedicationOptions(!showMedicationOptions)}
              />
              
              {showMedicationOptions && (
                <div className="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg border p-2">
                  <div className="p-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleSelectMedication('Medication 1')}>
                    Medication 1
                  </div>
                  <div className="p-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleSelectMedication('Medication 2')}>
                    Medication 2
                  </div>
                  <div className="p-2 hover:bg-gray-100 cursor-pointer" onClick={() => handleSelectMedication('Medication 3')}>
                    Medication 3
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Reaction */}
          <div className="mb-6">
            <label className="block mb-2 font-medium">Reaction</label>
            <textarea
              name="reaction"
              value={formData.reaction}
              onChange={handleChange}
              placeholder="e.g., 500 mmg"
              className="w-full bg-[#F5F5F5] border-none rounded-3xl p-3 h-32 resize-none"
            />
          </div>

          {/* Date */}
          <div className="mb-6">
            <label className="block mb-2 font-medium">Date</label>
            <div className="relative">
              <Input
                type="text"
                name="date"
                value={formData.date}
                onChange={handleChange}
                placeholder="MM / DD / YYYY"
                className="w-full bg-[#F5F5F5] border-none"
              />
              <Image
                src="/health/calender.svg"
                alt="Calendar"
                width={24}
                height={24}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
              />
            </div>
          </div>

          {/* Save Button */}
          <div className="mt-6">
            <CustomButton onClick={handleSubmit}>
              Save
            </CustomButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddMedicationReactionModal;
