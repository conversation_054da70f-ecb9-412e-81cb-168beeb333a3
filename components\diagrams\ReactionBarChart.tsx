import Image from "next/image";
import React, { useEffect, useState } from "react";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";

interface ReactionCount {
	name: string;
	value: number;
}

export default function ReactionBarChart({ userId }: { userId: string }) {
	const [reactionBarData, setReactionBarData] = useState<ReactionCount[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchData = async () => {
			setLoading(true);
			const service = new OneUpHealthService({ userId });
			try {
				const records: MedicalRecord[] = await service.fetchMedicalRecordsFromDatabase();
				const reactions = records.filter(r => r.type === "Medication Reaction");
				const freqMap: Record<string, number> = {};
				reactions.forEach(r => {
					const name = r.description.split(" - ")[1] || r.description || "Unknown";
					freqMap[name] = (freqMap[name] || 0) + 1;
				});
				const result: ReactionCount[] = Object.entries(freqMap)
					.map(([name, value]) => ({ name, value }))
					.sort((a, b) => b.value - a.value)
					.slice(0, 20);
				setReactionBarData(result);
			} catch (e) {
				setReactionBarData([]);
			}
			setLoading(false);
		};
		fetchData();
	}, [userId]);

	return (
		<div className="w-full md:w-[100%] mx-auto mb-4 bg-gray-100 p-3 rounded-[28px] border border-gray-200">
			<div className="p-4 bg-white rounded-[28px] shadow-sm border border-gray-200 flex flex-col">
				<div className="flex items-center mb-2">
					<Image
						src="/outpatient/heart-rate-monitor-blue.svg"
						alt="Reaction Bar Icon"
						width={24}
						height={24}
						className="mr-2"
					/>
					<span className="font-semibold text-lg text-[#4285F4] flex items-center">
						<span className="mr-2">
							Frequency of Different Medication Reactions
						</span>
					</span>
				</div>
				<div className="w-full pt-2 pb-2 overflow-x-auto">
					<div className="min-w-max">
						{loading ? (
							<div className="text-gray-400 text-center py-4">Loading...</div>
						) : reactionBarData.length === 0 ? (
							<div className="text-gray-400 text-center py-4">No reaction data found</div>
						) : (
							reactionBarData.map((item, idx) => (
								<div
									key={item.name + idx}
									className="flex items-center"
									style={{ position: "relative", height: 32 }}
								>
									<div
										style={{
											position: "absolute",
											left: 0,
											right: 0,
											top: "50%",
											borderTop: "1px dashed #E5E7EB",
											zIndex: 0,
										}}
									/>
									<span
										className="w-48 text-sm text-gray-600 z-10"
										style={{ background: "white" }}
									>
										{item.name}
									</span>
									<div
										style={{
											height: 16,
											width: `${item.value * 30}px`,
											background: "#2196F3",
											borderRadius: 8,
											marginLeft: 8,
											zIndex: 10,
										}}
									/>
									<span
										className="ml-2 text-sm text-gray-500 z-10"
										style={{ background: "white" }}
									>
										{item.value}
									</span>
								</div>
							))
						)}
						<div className="overflow-x-auto">
							<div className="flex justify-start text-xs text-gray-400 mt-2 pl-48 min-w-max">
								<span>0</span>
								<span className="ml-[60px]">1</span>
								<span className="ml-[50px]">2</span>
								<span className="ml-[50px]">3</span>
								<span className="ml-[50px]">4</span>
								<span className="ml-[50px]">5</span>
								<span className="ml-[50px]">10</span>
								<span className="ml-[50px]">20</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
