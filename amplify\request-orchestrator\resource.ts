import { defineFunction, secret } from "@aws-amplify/backend";

export const requestOrchestrator = defineFunction({
  name: "requestOrchestrator",
  entry: "./handler.ts",
  environment: {
    OPENAI_API_KEY: secret("OPENAI_API_KEY"),
    EVENTS_ENDPOINT: secret("EVENT_API_ENDPOINT"),
    EVENTS_API_KEY: secret("EVENTS_API_KEY"),
    GRAPHQL_ENDPOINT: secret("GRAPHQL_ENDPOINT"),
    GRAPHQL_API_KEY: secret("GRAPHQL_API_KEY"),
  },
});
