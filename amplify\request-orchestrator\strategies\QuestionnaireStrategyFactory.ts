import { AiService } from "../services/AiService";
import { QuestionnaireStrategy } from "../interfaces/QuestionnaireStrategy";
import { HealthQuestionnaireStrategy } from "./HealthQuestionnaireStrategy";
import { MedicalProfileStrategy } from "./MedicalProfileStrategy";

/**
 * Factory class for creating questionnaire strategy instances
 */
export class QuestionnaireStrategyFactory {
  /**
   * Creates a questionnaire strategy based on the questionnaire type
   * @param aiService The AI service instance to use
   * @param questionnaireType The type of questionnaire to create a strategy for
   * @returns A suitable strategy instance
   */
  static createStrategy(
    aiService: AiService,
    questionnaireId?: string
  ): QuestionnaireStrategy {
    console.log(
      `[QuestionnaireStrategyFactory] Creating strategy for questionnaire ID: ${questionnaireId}`
    );

    if (questionnaireId === "1") {
      return new HealthQuestionnaireStrategy(aiService);
    }
    if (questionnaireId === "2") {
      return new MedicalProfileStrategy(aiService);
    }
    return new HealthQuestionnaireStrategy(aiService);
  }
}
