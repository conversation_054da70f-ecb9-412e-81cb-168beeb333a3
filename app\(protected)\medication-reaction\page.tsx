"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import { useMedicalRecord } from "@/hooks/useMedicalRecord";
import MedicationReactionContent from "@/app/(protected)/medical-record/components/MedicationReactionContent";

export default function MedicationReaction() {
  const router = useRouter();
  const { selectedRecord } = useMedicalRecord();

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      {/* Header with back button and title */}
      <div className="flex items-center  pt-[51px] pl-6 py-4 overflow-hidden">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <div className="ml-4 flex items-center">
          <h1 className="text-[16px] font-medium mr-2">Medication Reaction</h1>
          <p className="text-[14px] text-gray-500">
            {selectedRecord?.date || "March 3, 2025"}
          </p>
        </div>
      </div>

      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-4">
        {/* Display medical reaction content if record is available */}
        {selectedRecord && selectedRecord.type === "Medication Reaction" && (
          <div className="bg-white rounded-3xl p-4 border border-gray-100 shadow-sm">
            <MedicationReactionContent record={selectedRecord} />
          </div>
        )}

        {/* Fallback Reaction Summary if no record */}
        {(!selectedRecord || selectedRecord.type !== "Medication Reaction") && (
          <div className="bg-white rounded-3xl p-4 border border-gray-100 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="text-[#FF4901] mr-2">
                <Image
                  src="/outpatient/heart-rate-monitor.svg"
                  alt="Medication Reaction"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <h2 className="text-[#FF4901] text-[16px] font-semibold">
                Medication Reaction
              </h2>
            </div>

            <p className="text-gray-700 text-[14px]">
              Patient reported occasional fatigue. Vitals within normal range.
              Routine labs ordered. Flu vaccine administered.
            </p>
          </div>
        )}

        {/* Related Prescription */}
        <div className="bg-white rounded-3xl p-4 border border-gray-100 shadow-sm">
          <div className="flex items-center mb-4">
            <div className="text-[#27A8E4] mr-2">
              <Image
                src="/outpatient/bluecapsule.svg"
                alt="Related Prescription"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-[#27A8E4] text-lg font-medium">
              Related Prescription
            </h2>
          </div>

          {/* Medication Card 1 */}
          <div className="bg-gray-100 rounded-xl p-4 mb-4">
            <h3 className="font-medium text-lg ">Hydroxychloroquine</h3>
            <p className="text-sm text-gray-500 mb-3">
              March 14, 2025 - April 15, 2025
            </p>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <p className="text-sm text-[#EC4899]">Dosage</p>
                <p className="font-medium text-[20px]">
                  500 <span className="text-sm text-gray-500">mmg</span>
                </p>
              </div>
              <div>
                <p className="text-sm text-[#6466F1]">Frequency</p>
                <p className="font-medium text-[20px]">
                  2<span className="text-sm text-gray-500">/day</span>
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <p className="text-sm text-[#27A8E4]">Route</p>
                <p className="font-medium text-[20px]">Oral</p>
              </div>
              <div>
                <p className="text-sm text-[#19C5BB]">Administrator</p>
                <p className="font-medium text-[20px]">Self</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-[#FF6D85]">Prescribing Physician</p>
              <p className="font-medium text-[20px]">Dr. Sarah Lee</p>
            </div>
          </div>

          {/* Medication Card 2 (duplicate from screenshot) */}
          <div className="bg-gray-100 rounded-xl p-4">
            <h3 className="font-medium text-lg ">Hydroxychloroquine</h3>
            <p className="text-sm text-gray-500 mb-3">
              March 14, 2025 - April 15, 2025
            </p>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <p className="text-sm text-[#EC4899]">Dosage</p>
                <p className="font-medium text-[20px]">
                  500 <span className="text-sm text-gray-500">mmg</span>
                </p>
              </div>
              <div>
                <p className="text-sm text-[#6466F1]">Frequency</p>
                <p className="font-medium text-[20px]">
                  2<span className="text-sm text-gray-500">/day</span>
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3 mb-3">
              <div>
                <p className="text-sm text-[#27A8E4]">Route</p>
                <p className="font-medium text-[20px]">Oral</p>
              </div>
              <div>
                <p className="text-sm text-[#19C5BB]">Administrator</p>
                <p className="font-medium text-[20px]">Self</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-[#FF6D85]">Prescribing Physician</p>
              <p className="font-medium text-[20px]">Dr. Sarah Lee</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
