import { LogOut } from "lucide-react";
import { useState } from "react";
import { signOut } from "aws-amplify/auth";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface HeaderProps {
  onBack: () => void;
}

export function Header({ onBack }: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <div className="flex justify-between items-center p-4  relative">
      
      <h1 className="text-xl font-bold">Me</h1>
      <button onClick={()=>setIsMenuOpen(!isMenuOpen)} className="w-[40px] h-[40px] bg-[#FFFFFF] rounded-2xl  flex items-center justify-center">
          <Image src={"/menu-deepg.svg"} width={24} height={24} alt="ds"/>
      </button>

      {isMenuOpen && (
        <div className="absolute right-4 top-16 bg-white shadow-lg  py-2 z-10  rounded-3xl w-[160px]">
          <button 
            onClick={handleLogout}
            className="w-full px-4 py-2 text-left flex items-center gap-2 hover:bg-gray-100"
          >
            <Image src="/log-out.svg" alt="Logout" width={24} height={24} />
            <span className="font-normal text-[16px]">Sign Out</span>
          </button>
        </div>
      )}
    </div>
  );
}
