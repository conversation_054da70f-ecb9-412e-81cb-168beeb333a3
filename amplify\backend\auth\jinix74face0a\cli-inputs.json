{"version": "1", "cognitoConfig": {"identityPoolName": "jinix74face0a_identitypool_74face0a", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "jinix74face0a", "userPoolName": "jinix74face0a_userpool_74face0a", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "jinix774face0a_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "74face0a", "resourceName": "jinix74face0a", "authSelections": "identityPoolAndUserPool", "useDefault": "default", "usernameAttributes": ["email"], "triggers": {}, "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true}}