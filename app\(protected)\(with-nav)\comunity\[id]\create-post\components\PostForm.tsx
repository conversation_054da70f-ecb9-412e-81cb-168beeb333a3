interface PostFormProps {
  postTitle: string;
  setPostTitle: (title: string) => void;
  postContent: string;
  setPostContent: (content: string) => void;
}

export default function PostForm({ postTitle, setPostTitle, postContent, setPostContent }: PostFormProps) {
  return (
    <>
      <div className="mb-4">
        <input
          type="text"
          value={postTitle}
          onChange={(e) => setPostTitle(e.target.value)}
          className="w-full p-2 text-xl border-b border-black focus:outline-none focus:border-blue-500 placeholder-gray-400"
          placeholder="Title"
        />
      </div>

      <div className="mb-4">
        <textarea
          value={postContent}
          onChange={(e) => setPostContent(e.target.value)}
          className="w-full p-2 text-sm border-b border-gray-300 focus:outline-none focus:border-blue-500 placeholder-gray-400 resize-none"
          rows={6}
          placeholder="Content"
        />
      </div>
    </>
  );
}
