import { ButtonHTMLAttributes, FC } from "react";

interface CustomButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  loading?: boolean;
}

const CustomButton: FC<CustomButtonProps> = ({ loading, children, ...props }) => {
  return (
    <button
      {...props}
      className={`w-full h-[46px] bg-[#5185FF] text-white font-medium rounded-2xl hover:bg-blue-600 transition-colors disabled:opacity-50 ${props.className}`}
      disabled={props.disabled || loading}
    >
      {loading ? "Loading..." : children}
    </button>
  );
};

export default CustomButton;
