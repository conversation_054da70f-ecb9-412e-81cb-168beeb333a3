"use client"

import * as React from "react"
import { <PERSON><PERSON>, Popup } from "react-leaflet"
import L from "leaflet"
import Image from "next/image"
import { useWindowSize } from "@/hooks/useWindowSize"

const haversineDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 3958.8 
  const toRadians = (degrees: number) => (degrees * Math.PI) / 180

  const dLat = toRadians(lat2 - lat1)
  const dLon = toRadians(lon1 - lon2)
  const lat1Rad = toRadians(lat1)
  const lat2Rad = toRadians(lat2)

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  const distance = R * c

  return Math.round(distance) 
}

const createMarkerIcon = (isActive: boolean) =>
  L.divIcon({
    html: `
      <img 
        src="${isActive ? "/MarkerActive.svg" : "/Marker.svg"}" 
        class="marker-icon w-[36px] h-[48px] ${isActive ? "animate-scale-up" : ""}" 
      />
    `,
    className: "custom-marker",
    iconSize: [isActive ? 38 : 36, isActive ? 50 : 48], 
    iconAnchor: [isActive ? 19 : 18, isActive ? 50 : 48], 
    popupAnchor: [0, isActive ? -50 : -48], 
  })

interface MapMarkersProps {
  lat: number;
  long: number;
  filteredEntities: any[];
  setSelectedEntity: (entity: any) => void;
  selectedEntity: any;
  setIsListExpanded: (value: boolean) => void;  // Add this line
}

const MapMarkers: React.FC<MapMarkersProps> = ({
  lat,
  long,
  filteredEntities,
  setSelectedEntity,
  selectedEntity,
  setIsListExpanded  // Add this line
}) => {
  const isDesktop = useWindowSize();
  const [popupOpen, setPopupOpen] = React.useState<number | null>(null);

  const handleMarkerClick = (entity: any, index: number) => {
    if (isDesktop) {
      // Toggle marker state on desktop
      if (selectedEntity && selectedEntity.lat === entity.lat && selectedEntity.long === entity.long) {
        setSelectedEntity(null);
        setPopupOpen(null);
      } else {
        setSelectedEntity(entity);
        setPopupOpen(index);
      }
    } else {
      // Mobile behavior remains the same
      if (selectedEntity && selectedEntity.lat === entity.lat && selectedEntity.long === entity.long) {
        setSelectedEntity(null);
      } else {
        setSelectedEntity(entity);
      }
    }
  };

  React.useEffect(() => {
    const styleSheet = document.createElement("style")
    document.head.appendChild(styleSheet)
    return () => {
      document.head.removeChild(styleSheet)
    }
  }, [])

  return (
    <>
      

      {filteredEntities.map((entity, index) => {
        const isActive = selectedEntity && selectedEntity.lat === entity.lat && selectedEntity.long === entity.long

       
        const distance = haversineDistance(lat, long, entity.lat, entity.long)

        return (
          <Marker
            key={index}
            position={[entity.lat, entity.long]}
            icon={createMarkerIcon(isActive)}
            eventHandlers={{
              click: () => handleMarkerClick(entity, index),
            }}
          >
            {isDesktop && (
              <Popup 
                className="w-[400px] h-[242px] relative"
                closeButton={false}
                eventHandlers={{
                  popupclose: () => {
                    setSelectedEntity(null);
                    setPopupOpen(null);
                  }
                }}
              >
  <div>
    <div>
      
      <div className="flex items-start">
        
        <div className="flex-shrink-0">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-r from-purple-400 to-blue-400">
            <Image
              src={entity.avatar || "/1.svg"}
              alt={`Avatar of ${entity.name}`}
              width={64}
              height={64}
              className="rounded-full object-cover"
            />
          </div>
        </div>

       
        <div className="ml-3 flex-1">
        
          <div className="flex items-center justify-between">
            <h3 className="text-base font-bold text-gray-900">{entity.name || "Doctor Name"}</h3>
          </div>

         
          <div className="text-sm text-gray-700 mt-1">
            {entity.Specialties || "Specialization"}
            <div className="text-sm text-blue-500 font-medium">{entity.phone || "************"}</div>
            <div className="text-sm text-gray-500 mt-1">
              {entity.address || "322 W North River Drive Spokane WA 99201-3203"}
            </div>
          </div>
        </div>
      </div>

    
      <span className="absolute top-0 right-0 mt-3 mr-3 text-sm font-medium text-gray-900">
        {distance} miles
      </span>

    
      <div className="mt-4">
        {entity.websiteURL ? (
          <a href={entity.websiteURL} target="_blank" rel="noopener noreferrer" className="block w-full">
            <button
              className="w-[352px] h-[48px] py-3 px-4 rounded-full text-center font-medium text-gray-800 border-2 border-transparent bg-clip-border"
              style={{
                backgroundImage: "linear-gradient(to right, #facc15, #c084fc)",
                backgroundClip: "border-box",
              }}
            >
              Visit Website <span className="ml-1">→</span>
            </button>
          </a>
        ) : (
          <a
            href={entity.WebsiteURL}
            target="_blank"
            rel="noopener noreferrer"
            className="mt-4 inline-flex items-center justify-center text-base font-medium text-gray-800 bg-white transition-colors rounded-full relative w-[352px] h-[48px]"
            style={{
              background: "white",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <span className="relative z-10">
              Visit Website
              <span className="ml-2">→</span>
            </span>
            <span
              className="absolute inset-0 rounded-full"
              style={{
                padding: "1px",
                background: "linear-gradient(to right, #FFB300, #0051FF, #FF569F)",
                mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                maskComposite: "exclude",
                WebkitMaskComposite: "xor",
              }}
            ></span>
          </a>
        )}
      </div>
    </div>
  </div>
</Popup>
            )}
          </Marker>
        )
      })}
    </>
  )
}

export default MapMarkers;