'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import AddMedicationModal from './AddMedicationModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import AddMedicationReactionModal from './AddMedicationReactionModal';
import { motion, AnimatePresence } from 'framer-motion';
import { OneUpHealthService, MedicalRecord } from '@/lib/services/oneUpHealthService';

interface MedicationDetailsListProps {
  onClose: () => void;
}

const MedicationDetailsList = ({ onClose }: MedicationDetailsListProps) => {
  const [activeTab, setActiveTab] = useState<'Medication' | 'Reaction'>('Medication');
  const [activeMedicationView, setActiveMedicationView] = useState<'current' | 'past'>('past');
  const [selectedReactionId, setSelectedReactionId] = useState<string | null>(null);
  const [selectedMedicationId, setSelectedMedicationId] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showAddReactionModal, setShowAddReactionModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [medicationToDelete, setMedicationToDelete] = useState<string | null>(null);

  // Real medication and reaction data
  const [medications, setMedications] = useState<any[]>([]);
  const [reactions, setReactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Format date as "day month year" in English (e.g. "14 March 2025")
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-';
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  useEffect(() => {
    // Fetch medical records from the service
    const fetchData = async () => {
      setLoading(true);
      try {
        const service = new OneUpHealthService({ userId: 'current' }); // Replace with actual userId
        const records: MedicalRecord[] = await service.fetchMedicalRecords();

        // Extract medications
        const meds = records
          .filter(r => r.type === 'Medication')
          .map(r => {
            const raw = r.rawData || {};
            const medConcept = raw.medicationCodeableConcept;
            const dosageInstruction = Array.isArray(raw.dosageInstruction) ? raw.dosageInstruction[0] : undefined;
            const doseQuantity = dosageInstruction && typeof dosageInstruction === 'object' && 'doseQuantity' in dosageInstruction ? dosageInstruction.doseQuantity : undefined;
            const timing = dosageInstruction && typeof dosageInstruction === 'object' && 'timing' in dosageInstruction ? dosageInstruction.timing : undefined;
            const timingRepeat = timing && typeof timing === 'object' && 'repeat' in timing ? timing.repeat : undefined;
            const route = dosageInstruction && typeof dosageInstruction === 'object' && 'route' in dosageInstruction ? dosageInstruction.route : undefined;
            const requester = raw.requester && typeof raw.requester === 'object' ? raw.requester : undefined;
            const recorder = raw.recorder && typeof raw.recorder === 'object' ? raw.recorder : undefined;

            return {
              id: r.id,
              name:
                (medConcept && typeof medConcept === 'object' && 'text' in medConcept && (medConcept as any).text)
                || (medConcept && typeof medConcept === 'object' && 'coding' in medConcept && Array.isArray((medConcept as any).coding) && (medConcept as any).coding[0]?.display)
                || r.description,
              dateRange: formatDate(r.date),
              dosage: doseQuantity && typeof doseQuantity === 'object' && 'value' in doseQuantity && (doseQuantity as any).value?.toString() || '',
              unit: doseQuantity && typeof doseQuantity === 'object' && 'unit' in doseQuantity && (doseQuantity as any).unit || '',
              frequency: timingRepeat && typeof timingRepeat === 'object' && 'frequency' in timingRepeat && (timingRepeat as any).frequency?.toString() || '',
              period: timingRepeat && typeof timingRepeat === 'object' && 'periodUnit' in timingRepeat && (timingRepeat as any).periodUnit || '',
              route: route && typeof route === 'object' && 'text' in route && (route as any).text || '',
              administrator: requester && 'display' in requester && (requester as any).display || '',
              physician: recorder && 'display' in recorder && (recorder as any).display || '',
            };
          });

        setMedications(meds);

        // Extract reactions
        const reacts = records
          .filter(r => r.type === 'Medication Reaction')
          .map(r => {
            const raw = r.rawData || {};
            const code = raw.code;
            return {
              id: r.id,
              date: formatDate(r.date),
              description:
                (code && typeof code === 'object' && 'text' in code && (code as any).text)
                || (code && typeof code === 'object' && 'coding' in code && Array.isArray((code as any).coding) && (code as any).coding[0]?.display)
                || r.description,
              details: (
                raw.reaction
                && Array.isArray(raw.reaction)
                && raw.reaction[0]
                && typeof raw.reaction[0] === 'object'
                && 'manifestation' in raw.reaction[0]
                && Array.isArray((raw.reaction[0] as any).manifestation)
                && (raw.reaction[0] as any).manifestation[0]
                && typeof (raw.reaction[0] as any).manifestation[0] === 'object'
                && 'text' in (raw.reaction[0] as any).manifestation[0]
                && (raw.reaction[0] as any).manifestation[0].text
              ) || r.description,
              relatedMedication: (
                raw.medicationReference
                && typeof raw.medicationReference === 'object'
                && 'display' in raw.medicationReference
                && (raw.medicationReference as any).display
              ) || '',
            };
          });

        setReactions(reacts);
        if (reacts.length > 0) setSelectedReactionId(reacts[0].id);
      } catch (error) {
        setMedications([]);
        setReactions([]);
      }
      setLoading(false);
    };

    fetchData();
  }, []);

  const handleOpenMenu = (medicationId: string) => {
    setSelectedMedicationId(selectedMedicationId === medicationId ? null : medicationId);
  };

  const handleDeleteClick = (medicationId: string) => {
    setMedicationToDelete(medicationId);
    setShowDeleteModal(true);
    setSelectedMedicationId(null); // Close the menu
  };

  const handleConfirmDelete = () => {
    if (medicationToDelete) {
      // Filter out the medication to delete
      const updatedMedications = medications.filter(med => med.id !== medicationToDelete);
      setMedications(updatedMedications);
      console.log(`Deleted medication: ${medicationToDelete}`);
    }
    setShowDeleteModal(false);
    setMedicationToDelete(null);
  };

  const handleAddMedication = (medicationData: any) => {
    // Generate a new ID for the medication
    const newId = `med${medications.length + 1}`;
    
    // Format date range
    const dateRange = medicationData.endDate 
      ? `${medicationData.startDate} - ${medicationData.endDate}`
      : `${medicationData.startDate} - Ongoing`;
    
    // Create new medication object
    const newMedication = {
      id: newId,
      name: medicationData.medicationName,
      dateRange: dateRange,
      dosage: medicationData.dosage.split(' ')[0] || '',
      unit: medicationData.dosage.split(' ')[1] || 'mmg',
      frequency: '2', // Default or could be added to form
      period: '/day', // Default or could be added to form
      route: medicationData.route,
      administrator: medicationData.administrator,
      physician: medicationData.prescribingPhysician,
    };
    
    // Add to medications state
    setMedications([...medications, newMedication]);
  };

  // Handler to add a new reaction
  const handleAddReaction = (reactionData: any) => {
    // Generate a new ID for the reaction
    const newId = `reaction${reactions.length + 1}`;
    
    // Create new reaction object
    const newReaction = {
      id: newId,
      date: reactionData.date,
      description: 'Medication Reaction',
      details: reactionData.reaction || 'No details provided',
      relatedMedication: reactionData.relatedMedication,
    };
    
    // Add to reactions state
    setReactions([...reactions, newReaction]);
    
    // Switch to reaction tab to show the new reaction
    setActiveTab('Reaction');
  };

  // Find the selected reaction
  const selectedReaction = reactions.find(reaction => reaction.id === selectedReactionId);

  // Handler for closing the modal
  const handleClose = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 flex justify-end items-stretch z-50 py-6 pr-6">
      <div className="bg-white w-[940px] h-full rounded-2xl shadow-lg flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b h-[56px]">
          <h2 className="text-xl font-medium">Medication</h2>
          <div className="flex items-center gap-4">
            <button 
              className="flex items-center text-black"
              onClick={() => setShowAddReactionModal(true)}
            >
              <span className="text-xl mr-1">+</span> Reaction
            </button>
            <button onClick={handleClose}>
             <Image
              src="/xrestuk.svg"
              alt="Close"
              width={24}
              height={24}
              className="w-6 h-6"
            />
            </button>
          </div>
        </div>
        
        {/* Content Area - Remove top tabs and integrate into the left panel */}
        <div className="flex flex-1 overflow-hidden">
          {/* Left sidebar with tabs and navigation */}
          <div className="w-[360px] border-r overflow-hidden flex flex-col">
            {/* Tabs moved to left sidebar */}
            <div className="border-b">
              <div className="flex">
                <button 
                  className={`py-4 flex-1 relative font-medium ${
                    activeTab === 'Medication' ? 'text-blue-500' : 'text-gray-500'
                  }`}
                  onClick={() => setActiveTab('Medication')}
                >
                  Medication
                  {activeTab === 'Medication' && (
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-500"></div>
                  )}
                </button>
                <button 
                  className={`py-4 flex-1 relative font-medium ${
                    activeTab === 'Reaction' ? 'text-blue-500' : 'text-gray-500'
                  }`}
                  onClick={() => setActiveTab('Reaction')}
                >
                  Reaction
                  {activeTab === 'Reaction' && (
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-500"></div>
                  )}
                </button>
              </div>
            </div>
            
            {/* Content specific to active tab */}
            <div className="overflow-y-auto p-4 flex-1">
              {activeTab === 'Medication' ? (
                <>
                  <div 
                    className={`flex justify-between items-center p-4 rounded-3xl cursor-pointer shadow-sm ${
                      activeMedicationView === 'current' ? 'bg-gray-100 border' : 'bg-white'
                    }`}
                    onClick={() => setActiveMedicationView('current')}
                  >
                    <span className="font-medium">Current Medication</span>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 12L10 8L6 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  
                  <div 
                    className={`mt-4 flex justify-between items-center p-4 rounded-3xl cursor-pointer border ${
                      activeMedicationView === 'past' ? 'bg-gray-100 border' : 'bg-white'
                    }`}
                    onClick={() => setActiveMedicationView('past')}
                  >
                    <span className="font-medium">Past Medication</span>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6 12L10 8L6 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </>
              ) : (
                // Reaction navigation
                <div className="relative">
                  {loading ? (
                    <div>Loading...</div>
                  ) : reactions.length === 0 ? (
                    <div>No reactions found.</div>
                  ) : (
                    reactions.map((reaction, index) => (
                      <div key={reaction.id} className="mb-6 relative pl-5">
                        {/* Timeline dot and line */}
                        <div className="absolute left-0 top-2 w-3 h-3 bg-white rounded-full border-2 border-blue-500"></div>
                        {index !== reactions.length - 1 && (
                          <div className="absolute left-1.5 top-4 w-[1px] h-full bg-gray-200"></div>
                        )}
                        
                        {/* Date bubble */}
                        <div className="inline-block bg-blue-100 text-blue-600 text-sm px-3 py-1 rounded-full mb-2">
                          {reaction.date}
                        </div>
                        
                        {/* Reaction card */}
                        <div 
                          className={`flex justify-between items-center p-4 rounded-[20px] cursor-pointer border ${
                            selectedReactionId === reaction.id ? 'bg-gray-100 shadow-sm' : 'border-gray-200'
                          }`}
                          onClick={() => setSelectedReactionId(reaction.id)}
                        >
                          <span className="font-medium text-gray-800">{reaction.description}</span>
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 12L10 8L6 4" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                          </svg>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Right panel with details */}
          <AnimatePresence mode="wait">
            {activeTab === 'Medication' && (
              <motion.div
                key="medication"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="flex-1 p-6 overflow-y-auto"
              >
                <>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                      <Image
                        src="/outpatient/bluecapsule.svg"
                        alt="Medication Icon"
                        width={24}
                        height={24}
                        className="mr-2"
                      />
                      <span className="text-blue-500 font-medium">
                        {activeMedicationView === 'current' ? 'Current Medication' : 'Past Medication'}
                      </span>
                    </div>
                    
                    {/* Add More button - only show for current medications */}
                    {activeMedicationView === 'current' && (
                      <button 
                        onClick={() => setShowAddModal(true)}
                        className="text-black font-medium"
                      >
                        Add More
                      </button>
                    )}
                  </div>

                  {/* Medication Cards */}
                  {loading ? (
                    <div>Loading...</div>
                  ) : medications.length === 0 ? (
                    <div>No medications found.</div>
                  ) : (
                    medications.map(medication => (
                      <div 
                        key={medication.id} 
                        className="bg-gray-50 rounded-xl p-4 mb-4"
                      >
                        <h3 className="text-lg font-medium">{medication.name}</h3>
                        <p className="text-sm text-gray-500 mb-3">{medication.dateRange}</p>
                        
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-xs text-pink-500">Dosage</p>
                            <p className="text-lg font-medium">
                              {medication.dosage}
                              <span className="text-xs text-gray-500 ml-1">{medication.unit}</span>
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-blue-500">Frequency</p>
                            <p className="text-lg font-medium">
                              {medication.frequency}
                              <span className="text-xs text-gray-500">{medication.period}</span>
                            </p>
                          </div>
                          
                          <div>
                            <p className="text-xs text-blue-500">Route</p>
                            <p className="font-medium">{medication.route}</p>
                          </div>
                          <div>
                            <p className="text-xs text-green-500">Administrator</p>
                            <p className="font-medium">{medication.administrator}</p>
                          </div>
                          
                          <div className="col-span-2">
                            <p className="text-xs text-pink-500">Prescribing Physician</p>
                            <p className="font-medium">{medication.physician}</p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </>
              </motion.div>
            )}
            {activeTab === 'Reaction' && selectedReaction && (
              <motion.div
                key={`reaction-${selectedReaction.id}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="flex-1 p-6 overflow-y-auto"
              >
                <>
                  <div className="text-gray-500 mb-2">
                    Medication Reaction <span className="text-black">{selectedReaction?.date || '-'}</span>
                  </div>
                  <div className="bg-white rounded-3xl p-4 border mb-6">
                    <div className="flex items-center mb-3">
                      <div className="text-orange-500 mr-2">
                        <Image
                          src="/outpatient/heart-rate-monitor.svg"
                          alt="Reaction Icon"
                          width={24}
                          height={24}
                        />
                      </div>
                      <span className="text-orange-500 font-medium">Medication Reaction</span>
                      <span className="ml-auto text-sm text-gray-500">{selectedReaction?.date || '-'}</span>
                    </div>
                    <p className="text-gray-700">
                      {selectedReaction.details}
                    </p>
                  </div>
                  {/* Related Prescriptions */}
                  <div className='border rounded-3xl p-4'>
                    <div className="flex items-center mb-3">
                      <div className="text-blue-500 mr-2">
                        <Image
                          src="/outpatient/bluecapsule.svg"
                          alt="Related Prescription Icon"
                          width={24}
                          height={24}
                        />
                      </div>
                      <span className="text-blue-500 font-medium">Related Prescription</span>
                    </div>
                    {medications.slice(0, 2).map((medication, index) => (
                      <div key={`related-${medication.id}`} className="bg-gray-50 rounded-xl p-4 mb-4">
                        <h3 className="font-medium">{medication.name}</h3>
                        <p className="text-xs text-gray-500 mb-4">{medication.dateRange}</p>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-pink-500">Dosage</p>
                            <p className="font-medium">{medication.dosage} <span className="text-xs text-gray-500">{medication.unit}</span></p>
                          </div>
                          <div>
                            <p className="text-xs text-blue-500">Frequency</p>
                            <p className="font-medium">{medication.frequency}<span className="text-xs text-gray-500">{medication.period}</span></p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs text-blue-500">Route</p>
                            <p className="font-medium">{medication.route}</p>
                          </div>
                          <div>
                            <p className="text-xs text-green-500">Administrator</p>
                            <p className="font-medium">{medication.administrator}</p>
                          </div>
                        </div>
                        <div>
                          <p className="text-xs text-pink-500">Prescribing Physician</p>
                          <p className="font-medium">{medication.physician}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
      
      {/* Modals */}
      {showAddModal && (
        <AddMedicationModal 
          onClose={() => setShowAddModal(false)} 
          onSave={handleAddMedication}
        />
      )}

      {showAddReactionModal && (
        <AddMedicationReactionModal
          onClose={() => setShowAddReactionModal(false)}
          onSave={handleAddReaction}
        />
      )}

      {showDeleteModal && (
        <DeleteConfirmationModal
          onConfirm={handleConfirmDelete}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
    </div>
  );
};

export default MedicationDetailsList;
