import React from "react";
import Image from "next/image";
import { ChevronRightIcon } from "lucide-react";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";
import TabSelector from "./TabSelector";
import RecordTimeline from "./RecordTimeline";

interface TimelinePanelProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  displayRecords: MedicalRecord[];
  isLoading: boolean;
  onRecordClick: (record: MedicalRecord) => void;
  selectedRecord: MedicalRecord | null;
  toggleGeneralInfo: () => void;
  isGeneralInfoActive?: boolean;
  isModal?: boolean;
}

const TimelinePanel: React.FC<TimelinePanelProps> = ({
  activeTab,
  onTabChange,
  displayRecords,
  isLoading,
  onRecordClick,
  selectedRecord,
  toggleGeneralInfo,
  isGeneralInfoActive = false,
  isModal = false,
}) => {
  if (isModal) {
    return (
      <div className="w-[378px] border-r flex flex-col h-full min-h-0">
        {/* General Info Button */}
        <div className="p-6 ">
          <button
            onClick={toggleGeneralInfo}
            className={`flex border h-[56px] items-center w-full rounded-3xl px-6 py-3 transition-colors ${
              isGeneralInfoActive
                ? "bg-[#F5F5F5]"
                : "bg-white"
            }`}
          >
            <div className="text-blue-500 flex items-center">
              <Image
                src="/medical-record/inputt.svg"
                alt="Info"
                width={24}
                height={24}
                className="mr-3"
              />
              <span className="font-medium text-blue-500">General Info</span>
            </div>
            <ChevronRightIcon className="h-5 w-5 ml-auto text-black" />
          </button>
        </div>

        {/* Tabs */}
        <TabSelector
          activeTab={activeTab}
          onTabChange={onTabChange}
          isModal={true}
        />

        {/* Timeline */}
        <div className=" min-h-0 overflow-y-auto px-5 py-3" style={{ maxHeight: "100vh", height: "70vh" }}>
         
          <RecordTimeline
            records={displayRecords}
            isLoading={isLoading}
            selectedRecord={selectedRecord}
            onRecordClick={onRecordClick}
            isModal={true}
          />
        </div>
      </div>
    );
  }

  // Regular timeline view
  return (
    <RecordTimeline
      records={displayRecords}
      isLoading={isLoading}
      selectedRecord={selectedRecord}
      onRecordClick={onRecordClick}
    />
  );
};

export default TimelinePanel;
