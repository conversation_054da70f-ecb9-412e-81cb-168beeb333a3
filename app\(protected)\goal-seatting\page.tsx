'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';
import CustomButton from '@/components/ui/CustomButton';
import DeleteConfirmationModal from '@/components/Health/DeleteConfirmationModal';
import GoalCard from '@/components/Health/GoalCard'; // додано імпорт

export default function GoalSetting() {
  const router = useRouter();
  const [selectedGoalId, setSelectedGoalId] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [goalToDelete, setGoalToDelete] = useState<string | null>(null);

  const handleOpenMenu = (goalId: string) => {
    setSelectedGoalId(selectedGoalId === goalId ? null : goalId);
  };

  const handleDeleteClick = (goalId: string) => {
    setGoalToDelete(goalId);
    setShowDeleteModal(true);
    setSelectedGoalId(null); // Close the menu
  };

  const handleConfirmDelete = () => {
    console.log(`Deleting goal: ${goalToDelete}`);
    // Here you would implement the actual deletion logic
    setShowDeleteModal(false);
    setGoalToDelete(null);
  };

  // Animation variants for the popup menu
  const menuVariants = {
    hidden: {
      opacity: 0,
      transform: "translateY(-8px)",
      scale: 0.95
    },
    visible: {
      opacity: 1,
      transform: "translateY(0px)",
      scale: 1,
      transition: {
        duration: 0.2,
        ease: [0.4, 0, 0.2, 1],
      },
    },
    exit: {
      opacity: 0,
      transform: "translateY(-8px)",
      scale: 0.95,
      transition: { duration: 0.15 },
    },
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF] relative">
      {/* Header with back button and title */}
      <div className="flex items-center justify-between pt-[51px] pl-6 pr-6 py-4 overflow-hidden">
        <div className="flex items-center">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <div className="ml-4 flex items-center">
            <h1 className="text-[22px] font-medium mr-2">Goal Setting</h1>
          </div>
        </div>
      </div>

      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-6">
        {/* Current Goals Section */}
        <div>
          <div className="flex items-center mb-4">
            <div className="text-[#27A8E4] mr-2">
              <Image
                src="/health/target.svg" 
                alt="Current Goals"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-[#27A8E4] text-lg font-medium">Current Goals</h2>
          </div>

          {/* Goal 1 */}
          <GoalCard
            goalId="goal1"
            title="Get More Walk"
            dateRange="March 14, 2025 - April 15, 2025"
            targetLabel="Target"
            targetValue="Walk 3 times, 20 mins each"
            selectedGoalId={selectedGoalId}
            onOpenMenu={handleOpenMenu}
            onDeleteClick={handleDeleteClick}
            menuVariants={menuVariants}
          />

          {/* Goal 2 */}
          <GoalCard
            goalId="goal2"
            title="Get Better Sleep"
            dateRange="March 14, 2025 - April 15, 2025"
            targetLabel="Target"
            targetValue={<>8 hours of sleep <span className="text-gray-400">per day</span></>}
            selectedGoalId={selectedGoalId}
            onOpenMenu={handleOpenMenu}
            onDeleteClick={handleDeleteClick}
            menuVariants={menuVariants}
          />

          {/* Add More Button */}
          <button 
            onClick={() => router.push('/goal')}
            className="flex items-center justify-center w-[160px] h-[50px] bg-white rounded-full shadow-md mt-4"
          >
            <Image
              src="/plus.svg"
              alt="Add More"
              width={20}
              height={20}
              className="mr-2"
            />
            <span className="text-[16px] font-medium">Add More</span>
          </button>
        </div>
        
        {/* Recommended Section */}
        <div className="mt-8">
          <div className="flex items-center mb-4">
            <div className="text-[#27A8E4] mr-2">
              <Image
                src="/health/sparkles.svg" 
                alt="Recommended"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-[#27A8E4] text-lg font-medium">Recommended</h2>
          </div>

          {/* Recommended Goal Card */}
          <div className="bg-[#EDF2FD] rounded-xl p-4 mb-4">
            <div className="flex items-center mb-2">
              <Image
                src="/health/sparkles.svg"
                alt="Sparkles"
                width={20}
                height={20}
                className="mr-2"
              />
              <h3 className="font-medium text-lg">Get Better Sleep</h3>
            </div>
            <p className="text-sm text-gray-500 mb-3">March 14, 2025 - April 15, 2025</p>
            
            <div className="mb-4">
              <p className="text-sm text-[#4285F4]">Target</p>
              <p className="font-medium text-[20px]">8 hours of sleep <span className="text-gray-400">per day</span></p>
            </div>
            
            <CustomButton
              onClick={() => router.push('/goal')}
              className="rounded-2xl py-3"
            >
              Add Goal
            </CustomButton>
          </div>
        </div>
      </div>

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <DeleteConfirmationModal
          onConfirm={handleConfirmDelete}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
    </div>
  );
}
