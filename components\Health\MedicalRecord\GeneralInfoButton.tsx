import Image from "next/image";
import { ChevronRightIcon } from "lucide-react";

interface GeneralInfoButtonProps {
  onClick: () => void;
  isSelected?: boolean;
}

const GeneralInfoButton: React.FC<GeneralInfoButtonProps> = ({
  onClick,
  isSelected = false
}) => {
  return (
    <div 
      className={`rounded-3xl flex items-center border border-gray-200 cursor-pointer h-[56px] ${
        isSelected ? "bg-[#F5F5F5]" : "bg-white"
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between w-full px-5">
        <div className="flex items-center">
          <Image
            src="/medical-record/inputt.svg"
            alt="Info"
            width={24}
            height={24}
            className="mr-2"
          />
          <h2 className="text-blue-500 font-medium">General Info</h2>
        </div>
        <ChevronRightIcon className="h-5 w-5 text-black" />
      </div>
    </div>
  );
};

export default GeneralInfoButton;
