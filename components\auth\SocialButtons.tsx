import { Button } from "@/components/ui/button"
import Image from "next/image"

interface SocialButtonsProps {
  onGoogleSignIn: () => void;
  onFacebookSignIn: () => void;
  loading: boolean;
  className?: string;
}

export function SocialButtons({ onGoogleSignIn, onFacebookSignIn, loading, className = "" }: SocialButtonsProps) {
  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      <Button
        variant="outline"
        onClick={onFacebookSignIn}
        className="flex items-center justify-between gap-2 rounded-2xl border border-gray-300 py-2 px-4 pl-3 h-[48px]"
        disabled={loading}
      >
        <Image src="/facebookk.svg" width={16} height={16} alt="Facebook" className="w-[28px] h-[28px] "/>
        <span className="flex-1 text-center">Continue with Facebook</span>
      </Button>
      <Button
        variant="outline"
        onClick={onGoogleSignIn}
        className="flex items-center justify-between gap-2 rounded-2xl border border-gray-300 py-2 px-4 h-[48px]"
        disabled={loading}
      >
        <Image src="/Group.svg" width={16} height={16} alt="Google" className="w-5 h-5"/>
        <span className="flex-1 text-center">Continue with Google</span>
      </Button>
    </div>
  );
}
