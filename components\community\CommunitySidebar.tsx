interface CommunitySidebarProps {
  community: any;
}

export default function CommunitySidebar({ community }: CommunitySidebarProps) {
  return (
    <div className="hidden md:block md:w-[30%] lg:w-[25%] xl:w-[32%] mx-auto md:mr-3 lg:mr-4">
      <div className="bg-[#F5F5F5] rounded-lg border border-gray-200 p-4 md:p-5 lg:p-6 mb-4 w-full">
        <h2 className="font-bold text-base md:text-lg mb-2 md:mb-3">Community Info</h2>
        <div className="mb-3 bg-white rounded-lg p-3 md:p-4 block items-center">
          <p className="text-lg md:text-xl font-medium md:text-[21px]">{community?.membersCount || 0}</p>
          <p className="text-gray-500 text-xs md:text-sm">members</p>
        </div>
        
        <div className="mb-4 md:mb-6">
          <h3 className="font-bold text-sm md:text-base mb-1 md:mb-2">Description</h3>
          <p className="text-gray-600 text-xs md:text-sm whitespace-pre-wrap break-words overflow-hidden">
            {community?.description}
          </p>
        </div>
        
        {community?.rules && community.rules.length > 0 && (
          <div>
            <h3 className="font-bold text-sm md:text-base mb-1 md:mb-2">Community Rules</h3>
            <ul className="text-gray-600 text-xs md:text-sm">
              {community.rules.map((rule: string, i: number) => (
                <li key={i} className="mb-1 md:mb-2 whitespace-pre-wrap break-words">{rule}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}
