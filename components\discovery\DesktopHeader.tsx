// import Image from "next/image";

// interface DesktopHeaderProps {
//   userEmail: string;
//   avatarSrc?: string;
//   headerSrc?: string;
// }

// export default function DesktopHeader({ userEmail, avatarSrc = "", headerSrc="" }: DesktopHeaderProps) {
//   return (
//     <div className="relative w-full h-32 lg:h-48">
//       {headerSrc ? (
//         <Image
//           src={headerSrc}
//           alt="Header"
//           fill
//           className="object-cover"
//           priority
//         />
//       ) : (
//         <div className="absolute inset-0 bg-[#D3E1FF]" />
//       )}
      
//       {/* Mobile header content */}
//       <div className="md:hidden w-full p-2 flex justify-between items-center absolute top-0">
//         {/* Left side: Back arrow */}
//         <button className="p-2">
//           <Image
//             src="/arrow-left.svg" // Replace with the actual path to your back arrow icon
//             alt="Back"
//             width={24}
//             height={24}
//             className="object-cover"
//           />
//         </button>

//         {/* Right side: Two buttons */}
//         <div className="flex items-center gap-2">
//           <button className="p-2">
//             <Image
//               src="/Search.svg" // Replace with the actual path to your magnifying glass icon
//               alt="Search"
//               width={24}
//               height={24}
//               className="object-cover"
//             />
//           </button>
//           <button className="p-2">
//             <Image
//               src="/discovery/smile.svg" // Replace with the actual path to your smiley plus icon
//               alt="Add"
//               width={24}
//               height={24}
//               className="object-cover"
//             />
//           </button>
//         </div>
//       </div>

//       {/* Mobile avatar and email */}
//       <div className="md:hidden absolute -bottom-[32px] right-4">
//         <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-white">
//           <Image
//             src={avatarSrc}
//             alt="User"
//             width={64}
//             height={64}
//             className="object-cover"
//           />
//         </div>
//       </div>
//       <div className="md:hidden absolute bottom-[1px] right-24">
//         <span className="text-black font-semibold text-[16px]">
//           {userEmail}
//         </span>
//       </div>

//       {/* Desktop header content */}
//       <div className="hidden md:flex absolute -bottom-[30px] right-4 items-center">
//         <span className="text-white font-medium mr-2 mb-6 text-sm lg:text-base">
//           {userEmail}
//         </span>
//         <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-white">
//           <Image
//             src={avatarSrc}
//             alt="User"
//             width={64}
//             height={64}
//             className="object-cover"
//           />
//         </div>
//       </div>
//     </div>
//   );
// }