"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useUser } from "@/hooks/useUsers";
import { UserType, FriendRequest } from "../../types/contacts/types";
import SearchBar from "./SearchBar";
import UsersList from "./UsersList";
import {
  fetchUsers,
  fetchFriendRequests,
  sendFriendRequest as sendRequest,
  respondToFriendRequest as respondToRequest,
} from "@/services/contactService";

interface ContactsPageContentProps {
  isModal?: boolean;
  onClose?: () => void;
}

export default function ContactsPageContent({
  isModal,
  onClose,
}: ContactsPageContentProps) {
  const router = useRouter();
  const [searchValue, setSearchValue] = useState("");
  const [allUsers, setAllUsers] = useState<UserType[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>([]);
  const [pendingRequests, setPendingRequests] = useState<FriendRequest[]>([]);
  const [acceptedFriends, setAcceptedFriends] = useState<UserType[]>([]);
  const [loading, setLoading] = useState(true);
  const { currentUser } = useUser();

  useEffect(() => {
    if (currentUser?.userId) {
      const fetchAllData = async () => {
        setLoading(true);
        try {
          const users = await fetchUsers(currentUser.userId);
          setAllUsers(users);
          setFilteredUsers(users);

          const {
            pendingRequests: pending,
            acceptedFriends: accepted,
            updatedAllUsers,
          } = await fetchFriendRequests(currentUser.userId, users);

          setPendingRequests(pending);
          setAcceptedFriends(accepted);
          setAllUsers(updatedAllUsers);
          setFilteredUsers(updatedAllUsers);
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setLoading(false);
        }
      };

      fetchAllData();
    } else {
      setLoading(false);
    }
  }, [currentUser?.userId]);

  useEffect(() => {
    if (searchValue.trim() === "") {
      setFilteredUsers(allUsers);
      return;
    }

    const filtered = allUsers.filter(
      (user) =>
        user.name?.toLowerCase().includes(searchValue.toLowerCase()) ||
        false ||
        user.email?.toLowerCase().includes(searchValue.toLowerCase()) ||
        false
    );
    setFilteredUsers(filtered);
  }, [searchValue, allUsers]);

  const handleSendFriendRequest = async (userId: string) => {
    if (!currentUser?.userId) return;

    const { success, requestId } = await sendRequest(
      currentUser.userId,
      userId
    );

    if (success && requestId) {
      const updateUserStatus = (prevUsers: UserType[]) => {
        return prevUsers.map((user) => {
          if (user.userId === userId) {
            return {
              ...user,
              friendRequestStatus: {
                type: "sent" as const,
                status: "PENDING" as const, // Add 'as const' to ensure correct typing
                requestId,
              },
            };
          }
          return user;
        });
      };

      setAllUsers((prevUsers) => updateUserStatus(prevUsers));
      setFilteredUsers((prevUsers) => updateUserStatus(prevUsers));

      const {
        pendingRequests: pending,
        acceptedFriends: accepted,
        updatedAllUsers,
      } = await fetchFriendRequests(currentUser.userId, allUsers);

      setPendingRequests(pending);
      setAcceptedFriends(accepted);
      setAllUsers(updatedAllUsers);
      setFilteredUsers((prevUsers) => {
        return prevUsers.map((user) => {
          const updatedUser = updatedAllUsers.find(
            (u) => u.userId === user.userId
          );
          return updatedUser || user;
        });
      });
    }
  };

  const handleRespondToFriendRequest = async (
    requestId: string,
    status: "ACCEPTED" | "DECLINED"
  ) => {
    if (!currentUser?.userId) return;

    const success = await respondToRequest(requestId, status);

    if (success) {
      const {
        pendingRequests: pending,
        acceptedFriends: accepted,
        updatedAllUsers,
      } = await fetchFriendRequests(currentUser.userId, allUsers);

      setPendingRequests(pending);
      setAcceptedFriends(accepted);
      setAllUsers(updatedAllUsers);
      setFilteredUsers((prevUsers) => {
        return prevUsers.map((user) => {
          const updatedUser = updatedAllUsers.find(
            (u) => u.userId === user.userId
          );
          return updatedUser || user;
        });
      });
    }
  };

  return (
    <div
      className={`flex flex-col ${isModal ? "h-full" : "min-h-full"} w-full   bg-white backdrop-blur-md rounded-3xl md:rounded-none`}
    >
      <div className="sticky top-0 z-10 p-5 hidden md:flex items-center">
        <Image
          src="/xrestuk.svg"
          alt="Close"
          height={24}
          width={24}
          className="absolute top-4 right-4 z-10 cursor-pointer hover:opacity-80 transition-opacity"
          onClick={onClose || (() => router.back())}
        />
        <h1 className="text-xl font-semibold flex-1 text-center pr-8">
          New Friends
        </h1>
      </div>

      <div className="py-5 px-[14px]">
        <SearchBar searchValue={searchValue} setSearchValue={setSearchValue} />
      </div>

      <div className="flex-grow flex flex-col bg-white m-[6px] rounded-3xl overflow-y-auto max-h-[calc(100vh-200px)]">
        <h1 className="pl-5 text-[#929292] sticky top-0 bg-white z-10">
          Friends requests
        </h1>

        <UsersList
          users={filteredUsers}
          loading={loading}
          onSendRequest={handleSendFriendRequest}
          onRespondToRequest={handleRespondToFriendRequest}
        />
      </div>
    </div>
  );
}
