import Image from "next/image";

interface FooterProps {
  communityName: string | undefined;
  handleImageChange: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  imageLoading: boolean;
}

export default function Footer({ communityName, handleImageChange, imageLoading }: FooterProps) {
  return (
    <div className="flex items-center justify-between mt-4">
      <div className="flex items-center text-sm text-[#5185FF]">
        <span className="mr-2">🌐</span>
        <span>Post to {communityName}</span>
      </div>
      <div className="flex items-center gap-4">
        <label htmlFor="image-upload" className="cursor-pointer">
          <div className="text-gray-500">
            <Image src="/Utility.svg" alt="Add Images" width={24} height={24} />
          </div>
          <input
            id="image-upload"
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={handleImageChange}
            disabled={imageLoading}
          />
        </label>
      </div>
    </div>
  );
}
