"use client";
import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { communityService } from "@/services/communityService";
import { fetchAuthSession } from "aws-amplify/auth";

type RecommendedGroupProps = {
  group: {
    id?: string;
    name: string;
    description: string;
    members: number;
    avatar: string;
  };
  onJoin?: () => void;
};

export default function RecommendedGroup({ group, onJoin }: RecommendedGroupProps) {
  const [isJoining, setIsJoining] = useState(false);
  const [isMember, setIsMember] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();
  
  useEffect(() => {
    // Check if user is a member when component mounts
    const checkMembership = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email && group.id) {
          const userId = String(session.tokens.idToken.payload.email);
          setUserId(userId);
          
          console.log(`Checking membership for user ${userId} in community ${group.id}`);
          const isMember = await communityService.checkMembershipStatus(userId, group.id);
          setIsMember(isMember);
        }
      } catch (error) {
        console.error("Error checking membership:", error);
      }
    };
    
    if (group.id) {
      checkMembership();
    }
  }, [group.id]);
  
  const handleJoin = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent navigation when clicking join
    if (isMember || !group.id) return;
    
    try {
      setIsJoining(true);
      if (userId) {
        await communityService.joinCommunity(userId, group.id);
        setIsMember(true);
        
        if (onJoin) {
          onJoin();
        }
      } else {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          const userId = String(session.tokens.idToken.payload.email);
          setUserId(userId);
          await communityService.joinCommunity(userId, group.id);
          setIsMember(true);
          
          if (onJoin) {
            onJoin();
          }
        }
      }
    } catch (error) {
      console.error("Error joining community:", error);
    } finally {
      setIsJoining(false);
    }
  };
  
  const handleGroupClick = () => {
    if (group.id) {
      router.push(`/comunity/${group.id}`);
    }
  };

  const truncateText = (text: string, maxLength: number = 30) => {
    if (text.length <= maxLength) return text;
    return `${text.substring(0, maxLength)}...`;
  };
  
  return (
    <div 
      className="w-full bg-white md:bg-gray-100 flex flex-col px-0 pb-4 md:px-4 md:pb-4 lg:px-4 lg:pb-6 cursor-pointer hover:shadow-md transition-shadow relative after:content-[''] after:absolute after:left-0 after:right-0 md:after:left-4 md:after:right-4 after:bottom-0 after:h-[1px] after:bg-gray-300"
      onClick={handleGroupClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex flex-col max-w-[calc(100%-90px)]">
          <div className="flex items-center">
            <div className="relative w-12 h-12 md:w-9 md:h-9 lg:w-10 lg:h-10 rounded-full overflow-hidden flex-shrink-0">
              <Image 
                src={group.avatar || "/groupavatar.svg"} 
                alt="Group icon" 
                width={48} 
                height={48} 
                className="object-cover" 
              />
            </div>
            <div className="ml-2 md:ml-3 min-w-0">
              <h3 className="text-gray-800 font-medium text-[16px] break-all md:break-normal md:text-base md:truncate" style={{ wordBreak: 'break-word', overflowWrap: 'break-word', display: 'block', width: '100%' }}>{group.name}</h3>
              <p className="text-gray-500 text-[14px] md:text-xs">{group.members} members</p>
            </div>
          </div>
        </div>
        {isMember ? (
          <button 
            className="border border-blue-500 text-blue-500 bg-gray-100 text-xs md:text-sm font-medium px-3 md:px-4 py-1 h-[36px] w-auto rounded-full flex-shrink-0"
            onClick={(e) => e.stopPropagation()}
          >
            Joined
          </button>
        ) : (
          <button 
            className="bg-blue-500 text-white text-xs md:text-sm font-medium px-3 md:px-4 py-1 h-[36px] w-auto rounded-full disabled:bg-blue-300 flex-shrink-0"
            onClick={handleJoin}
            disabled={isJoining}
          >
            {isJoining ? "Joining..." : "Join"}
          </button>
        )}
      </div>
      <p className="text-gray-600 text-[14px] md:text-xs mt-2 break-words whitespace-pre-wrap word-break" style={{ wordWrap: 'break-word', hyphens: 'auto' }}>{truncateText(group.description)}</p>
    </div>
  );
}

