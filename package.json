{"name": "aws-amplify-gen2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "toolpad-studio:dev": "toolpad-studio dev ./my-toolpad-app", "toolpad-studio:build": "toolpad-studio build ./my-toolpad-app", "toolpad-studio:start": "toolpad-studio start ./my-toolpad-app"}, "dependencies": {"@aws-amplify/api": "^6.3.5", "@aws-amplify/ui-react": "^6.9.1", "@aws-amplify/ui-vue": "^4.2.28", "@aws-sdk/client-apigatewaymanagementapi": "^3.758.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@parcel/watcher": "^2.5.1", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@react-google-maps/api": "^2.20.6", "@shadcn/ui": "^0.0.4", "@toolpad/core": "^0.12.1", "@toolpad/studio": "^0.12.1", "@types/lodash": "^4.17.16", "@types/react-leaflet": "^2.8.3", "ai": "^4.1.46", "aws-amplify": "^6.13.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "data-fns": "^1.1.0", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "motion": "^12.10.4", "next": "14.2.10", "next-themes": "^0.4.5", "openai": "^4.85.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-leaflet": "^4.2.1", "react-voice-visualizer": "^2.0.8", "recharts": "^2.15.3", "shadcn": "^2.4.0-canary.9", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@aws-amplify/backend": "^1.5.1", "@aws-amplify/backend-cli": "^1.3.0", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/leaflet": "^1.9.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/testing-library__jest-dom": "^5.14.9", "autoprefixer": "^10.4.20", "aws-cdk": "^2", "aws-cdk-lib": "^2", "babel-jest": "^29.7.0", "constructs": "^10.3.0", "esbuild": "^0.23.1", "eslint": "^9", "eslint-config-next": "15.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.0", "typescript": "^5.6.2"}}