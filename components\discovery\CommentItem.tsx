import { useState, useEffect } from "react";
import Image from "next/image";
import { Heart, MessageCircle } from "lucide-react";
import { GroupedComment } from "@/types/comment";
import { getRelativeTime } from "@/utils/relativeTime";
import { DesktopReplyForm } from "./DesktopReplyForm";
import { generateClient } from "aws-amplify/api";
import { fetchAuthSession } from "aws-amplify/auth";
import { Schema } from "@/amplify/data/resource";
import CommentLikesPopup from "./CommentLikesPopup";

interface CommentLikeResponse {
  listLikesByComment: {
    items: Array<{
      id: string;
      userId: string;
    }>;
  };
}

interface CommentLikeWithUser {
  id: string;
  userId: string;
  user: {
    name: string;
    profilePicture?: string;
  };
}

interface CommentItemProps {
  comment: GroupedComment;
  level?: number;
  onRefresh?: () => void;
  onMobileReply?: (comment: GroupedComment) => void;
  isReplying?: boolean;
}

export function CommentItem({
  comment,
  level = 0,
  onRefresh,
  onMobileReply,
  isReplying,
}: CommentItemProps) {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [liked, setLiked] = useState(comment.isLiked || false);
  const [likesCount, setLikesCount] = useState(comment.likes || 0);
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(
    null
  );
  const [showLikesPopup, setShowLikesPopup] = useState(false);
  const [likesList, setLikesList] = useState<CommentLikeWithUser[]>([]);
  const client = generateClient<Schema>();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email);
          setCurrentUser({ email });
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      }
    };
    fetchUser();
  }, []);

  useEffect(() => {
    setLiked(comment.isLiked || false);
  }, [comment.isLiked]);

  useEffect(() => {
    setLikesCount(comment.likes || 0);
  }, [comment.likes]);

  const handleLike = async () => {
    if (!currentUser?.email) return;

    try {
      if (!liked) {
        // Create new like
        await client.graphql({
          query: `
            mutation CreateCommentLikeAndUpdateCount($input: CreateCommentLikeInput!, $updateInput: UpdateCommentInput!) {
              createCommentLike(input: $input) {
                id
              }
              updateComment(input: $updateInput) {
                id
                likes
              }
            }
          `,
          variables: {
            input: {
              userId: currentUser.email,
              commentId: comment.id,
              createdAt: new Date().toISOString(),
            },
            updateInput: {
              id: comment.id,
              likes: likesCount + 1,
            }
          },
        });

        setLikesCount((prev) => prev + 1);
        setLiked(true);
      } else {
        // Find and delete existing like in a single operation
        const likeResult = await client.graphql<CommentLikeResponse>({
          query: `
            query GetCommentLike($commentId: String!, $userId: String!) {
              listLikesByComment(commentId: $commentId, filter: { userId: { eq: $userId } }) {
                items {
                  id
                }
              }
            }
          `,
          variables: {
            commentId: comment.id,
            userId: currentUser.email,
          },
        });

        if (
          "data" in likeResult &&
          likeResult.data?.listLikesByComment?.items?.[0]
        ) {
          const likeId = likeResult.data.listLikesByComment.items[0].id;

          await client.graphql({
            query: `
              mutation DeleteLikeAndUpdateCount($deleteInput: DeleteCommentLikeInput!, $updateInput: UpdateCommentInput!) {
                deleteCommentLike(input: $deleteInput) {
                  id
                }
                updateComment(input: $updateInput) {
                  id
                  likes
                }
              }
            `,
            variables: {
              deleteInput: { id: likeId },
              updateInput: {
                id: comment.id,
                likes: likesCount - 1,
              }
            },
          });

          setLikesCount((prev) => prev - 1);
          setLiked(false);
        }
      }
      
      // Оновлюємо список коментарів після зміни лайка
      onRefresh?.();
    } catch (error) {
      console.error("Error updating like:", error);
    }
  };

  const fetchLikes = async () => {
    if (!comment.id) return;

    try {
      const result = await client.graphql({
        query: `
          query GetCommentLikes($commentId: String!) {
            listLikesByComment(commentId: $commentId) {
              items {
                id
                userId
                user {
                  name
                  profilePicture
                }
              }
            }
          }
        `,
        variables: {
          commentId: comment.id,
        },
      });

      if ("data" in result && result.data?.listLikesByComment?.items) {
        setLikesList(result.data.listLikesByComment.items);
      }
    } catch (error) {
      console.error("Error fetching likes:", error);
    }
  };

  const handleReplyClick = () => {
    if (window.innerWidth < 768) {
      onMobileReply?.(comment);
    } else {
      setShowReplyInput(!showReplyInput);
    }
  };

  return (
    <div
      className={`${level > 0 ? "pl-4 border-l border-[#E5E5E5]" : ""} mb-0 pb-4`}
    >
      <div className="flex items-start mt-0">
        <div className="flex flex-col w-full">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-green-100 flex-shrink-0">
              <Image
                src={comment.user?.profilePicture || "/Avatar.png"}
                alt={comment.user?.name || "User"}
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
            <div className="flex flex-1 justify-between items-center">
              <h3 className="text-black font-medium">
                {comment.user?.name || comment.userId}
              </h3>
              <span className="text-gray-500 text-sm">
                {getRelativeTime(comment.createdAt)}
              </span>
            </div>
          </div>

          <p className="text-[#737373] font-medium text-[14px] mt-3">
            {comment.content}
          </p>

          <div className="pt-3 flex items-center justify-end space-x-2">
            <button
              className="flex items-center text-gray-500"
              onClick={handleReplyClick}
            >
              <MessageCircle className="w-5 h-5 mr-1" />
              <span>Reply</span>
            </button>
            <button
              className="flex items-center text-gray-500"
              onClick={handleLike}
            >
              <Heart
                className={`w-5 h-5 mr-1 ${liked ? "fill-current text-red-500" : ""}`}
              />
              <span
                className="cursor-pointer hover:underline"
                onClick={(e) => {
                  e.stopPropagation();
                  fetchLikes();
                  setShowLikesPopup(true);
                }}
              >
                {likesCount}
              </span>
            </button>
          </div>
        </div>
      </div>

      {comment.replies?.length > 0 && (
        <div className="mt-4">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              level={level + 1}
              onRefresh={onRefresh}
              onMobileReply={onMobileReply}
            />
          ))}
        </div>
      )}

      {showReplyInput && (
        <DesktopReplyForm
          parentComment={comment}
          onClose={() => setShowReplyInput(false)}
          onRefresh={onRefresh}
        />
      )}

      <CommentLikesPopup
        isOpen={showLikesPopup}
        onClose={() => setShowLikesPopup(false)}
        likes={likesList}
      />
    </div>
  );
}
