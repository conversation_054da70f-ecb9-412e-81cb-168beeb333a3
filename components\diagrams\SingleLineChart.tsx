import Image from "next/image";
import React, { useEffect, useState } from "react";
import { ResponsiveContainer, LineChart, CartesianGrid, XAxis, YAxis, Tooltip, Line } from "recharts";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";

export default function SingleLineChart({ userId }: { userId: string }) {
  const [lineChartData, setLineChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const service = new OneUpHealthService({ userId });
      try {
        const records: MedicalRecord[] = await service.fetchMedicalRecordsFromDatabase();
        const visits = records.filter(r => r.type === "Outpatient Visit");
        // Group by month
        const monthMap: Record<string, number> = {};
        visits.forEach(v => {
          const month = v.date.substring(0, 7);
          monthMap[month] = (monthMap[month] || 0) + 1;
        });
        const chartData = Object.entries(monthMap).map(([date, value]) => ({ date, value }));
        setLineChartData(chartData);
      } catch (e) {
        setLineChartData([]);
      }
      setLoading(false);
    };
    fetchData();
  }, [userId]);

  return (
    <div className="w-full md:w-[100%] mx-auto mb-4 bg-gray-100 p-3 rounded-[28px] border border-gray-200">
      <div className="p-4 bg-white rounded-[28px] shadow-sm border border-gray-200 flex flex-col">
        <div className="flex items-center text-[#008CFF] mb-2">
          <Image
            src="/outpatient/heart-rate-monitor-blue.svg"
            alt="Line Chart Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-semibold text-lg">
            # of Outpatient Visits Per Month
          </span>
        </div>
        <ResponsiveContainer width="100%" height={120}>
          <LineChart data={lineChartData}>
            <CartesianGrid strokeDasharray="6 6" vertical={false} />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tickFormatter={(value, idx) => {
                const date = new Date(value);
                if (idx === 0 || idx === lineChartData.length - 1) {
                  return `${date.getFullYear()} Jan`;
                }
                return "";
              }}
              interval={0}
              minTickGap={0}
            />
            <YAxis
              domain={[0, 15]}
              axisLine={false}
              tickLine={false}
              ticks={[0, 3, 6, 9, 15]}
            />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="value"
              stroke="#008CFF"
              strokeWidth={3}
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
        {loading && <div className="text-gray-400 text-center py-4">Loading...</div>}
        {!loading && lineChartData.length === 0 && <div className="text-gray-400 text-center py-4">No visit data found</div>}
      </div>
    </div>
  );
}
