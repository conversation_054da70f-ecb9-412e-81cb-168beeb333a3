import Image from "next/image";
import { motion } from "framer-motion";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";
import { useState, useEffect } from "react";

// FHIR resource type definitions
interface FHIRCoding {
  system?: string;
  code?: string;
  display?: string;
}

interface FHIRCodeableConcept {
  coding?: FHIRCoding[];
  text?: string;
}

interface FHIRReference {
  reference?: string;
  display?: string;
}

interface FHIRCondition {
  resourceType: "Condition";
  id?: string;
  clinicalStatus?: {
    coding?: FHIRCoding[];
    text?: string;
  };
  verificationStatus?: {
    coding?: FHIRCoding[];
    text?: string;
  };
  category?: FHIRCodeableConcept[];
  severity?: FHIRCodeableConcept;
  code?: FHIRCodeableConcept;
  subject?: FHIRReference;
  onsetDateTime?: string;
  recordedDate?: string;
  recorder?: FHIRReference;
  note?: Array<{ text?: string }>;
}

interface SymptomContentProps {
  record?: MedicalRecord | null;
}

export default function SymptomContent({ record }: SymptomContentProps) {
  const [symptomData, setSymptomData] = useState<{
    description: string;
    status: string;
    verification: string;
    onset: string;
    severity: string;
    categories: string[];
    recorder: string;
    notes: string[];
  }>({
    description: "Loading symptom details...",
    status: "-",
    verification: "-",
    onset: "-",
    severity: "-",
    categories: [],
    recorder: "-",
    notes: [],
  });

  useEffect(() => {
    if (!record) return;

    // Debug logging
    console.log("Processing symptom/condition record:", record);

    // Extract data from FHIR resource if available
    const conditionData = record.rawData as FHIRCondition | undefined;

    // Parse the data with robust fallbacks
    try {
      // Extract display text for the condition
      const conditionDisplayText =
        conditionData?.code?.text ||
        conditionData?.code?.coding?.find((c) => c.display)?.display ||
        "Unspecified symptom";

      // Extract the more detailed description from various possible sources
      const description =
        record.description ||
        conditionDisplayText +
          (conditionData?.clinicalStatus?.text
            ? ` (${conditionData.clinicalStatus.text})`
            : "") +
          (conditionData?.verificationStatus?.text
            ? ` - ${conditionData.verificationStatus.text}`
            : "") ||
        "Patient presented with symptoms.";

      // Get clinical status with fallbacks
      const status =
        conditionData?.clinicalStatus?.coding?.[0]?.display ||
        conditionData?.clinicalStatus?.coding?.[0]?.code ||
        conditionData?.clinicalStatus?.text ||
        "active";

      // Get verification status with fallbacks
      const verification =
        conditionData?.verificationStatus?.coding?.[0]?.display ||
        conditionData?.verificationStatus?.coding?.[0]?.code ||
        conditionData?.verificationStatus?.text ||
        "confirmed";

      // Get onset date
      const onset =
        conditionData?.onsetDateTime ||
        conditionData?.recordedDate ||
        record.date ||
        new Date().toISOString();

      // Extract severity if available
      const severity =
        conditionData?.severity?.coding?.[0]?.display ||
        conditionData?.severity?.text ||
        "moderate";

      // Extract categories
      const categories = conditionData?.category?.map(
        (c) => c.text || c.coding?.[0]?.display || "symptom"
      ) || [conditionData?.code?.text || "Symptom"];

      // Get recorder information
      const recorder =
        conditionData?.recorder?.display || "Healthcare Provider";

      // Extract clinical notes if available
      const notes = conditionData?.note?.map((n) => n.text || "") || [];

      // Add ICD and SNOMED codes if available
      const icdCode = conditionData?.code?.coding?.find(
        (c) => c.system?.includes("icd-10") || c.system?.includes("icd-9")
      )?.code;

      const snomedCode = conditionData?.code?.coding?.find((c) =>
        c.system?.includes("snomed")
      )?.code;

      // Add codes to notes if available and not already in notes
      if (icdCode && !notes.some((note) => note.includes(icdCode))) {
        notes.push(`ICD Code: ${icdCode}`);
      }

      if (snomedCode && !notes.some((note) => note.includes(snomedCode))) {
        notes.push(`SNOMED Code: ${snomedCode}`);
      }

      // Update state with all extracted data
      setSymptomData({
        description,
        status,
        verification,
        onset,
        severity,
        categories,
        recorder,
        notes,
      });
    } catch (error) {
      console.error("Error parsing condition data:", error);
      // Set default data on error
      setSymptomData({
        description: record.description || "No symptom details available.",
        status: "-",
        verification: "-",
        onset: record.date || "-",
        severity: "-",
        categories: [],
        recorder: "-",
        notes: [],
      });
    }
  }, [record]);

  // Format dates nicely
  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      // Return the original string if date parsing fails
      return dateString;
    }
  };

  // Add function to render severity indicator
  const renderSeverityIndicator = (severity: string) => {
    const severityLower = severity.toLowerCase();
    if (
      severityLower.includes("severe") ||
      severityLower.includes("critical")
    ) {
      return (
        <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-2"></span>
      );
    } else if (severityLower.includes("moderate")) {
      return (
        <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2"></span>
      );
    } else if (
      severityLower.includes("mild") ||
      severityLower.includes("low")
    ) {
      return (
        <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
      );
    }
    return (
      <span className="inline-block w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
    );
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.4 }}
    >
      {/* Symptom Summary - main section with key information */}
      <motion.div
        className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <motion.div
          className="flex items-center mb-3"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="text-red-500 mr-2">
            <Image
              src="/outpatient/heart-rate-monitor.svg"
              alt="Symptom"
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </div>
          <h2 className="text-red-500 font-medium">Symptom Summary</h2>
        </motion.div>

        <motion.p
          className="text-gray-700 text-[14px]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          {symptomData.description}
        </motion.p>

        <motion.div
          className="mt-4 space-y-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Clinical Status:</span>
            <div className="flex items-center">
              {renderSeverityIndicator(symptomData.status)}
              <span
                className={`font-medium ${
                  symptomData.status.toLowerCase() === "active" ||
                  symptomData.status.toLowerCase() === "recurrence"
                    ? "text-red-600"
                    : symptomData.status.toLowerCase() === "resolved"
                      ? "text-green-600"
                      : "text-blue-600"
                }`}
              >
                {symptomData.status.charAt(0).toUpperCase() +
                  symptomData.status.slice(1).toLowerCase()}
              </span>
            </div>
          </div>

          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Verification:</span>
            <span className="font-medium">
              {symptomData.verification.charAt(0).toUpperCase() +
                symptomData.verification.slice(1).toLowerCase()}
            </span>
          </div>

          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Onset Date:</span>
            <span className="font-medium">{formatDate(symptomData.onset)}</span>
          </div>

          {symptomData.severity && (
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-500">Severity:</span>
              <div className="flex items-center">
                {renderSeverityIndicator(symptomData.severity)}
                <span
                  className={`font-medium ${
                    symptomData.severity.toLowerCase().includes("severe")
                      ? "text-red-600"
                      : symptomData.severity.toLowerCase().includes("mild")
                        ? "text-green-600"
                        : "text-yellow-600"
                  }`}
                >
                  {symptomData.severity.charAt(0).toUpperCase() +
                    symptomData.severity.slice(1).toLowerCase()}
                </span>
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>

      {/* Categories and Additional Info */}
      {(symptomData.categories.length > 0 || symptomData.recorder !== "-") && (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            className="flex items-center mb-3"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <div className="text-blue-500 mr-2">
              <Image
                src="/outpatient/inspect.svg"
                alt="Additional Info"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-blue-500 font-medium">
              Additional Information
            </h2>
          </motion.div>

          {symptomData.categories.length > 0 && (
            <div className="mb-4">
              <h3 className="text-gray-500 text-sm mb-2">Categories</h3>
              <div className="flex flex-wrap gap-2">
                {symptomData.categories.map((category, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-md"
                  >
                    {category}
                  </span>
                ))}
              </div>
            </div>
          )}

          {symptomData.recorder !== "-" && (
            <div>
              <h3 className="text-gray-500 text-sm mb-2">Recorded By</h3>
              <p className="text-sm font-medium">{symptomData.recorder}</p>
            </div>
          )}
        </motion.div>
      )}

      {/* Clinical Notes */}
      {symptomData.notes.length > 0 && (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <motion.div
            className="flex items-center mb-3"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <div className="text-green-500 mr-2">
              <Image
                src="/medical-record/note.svg"
                alt="Clinical Notes"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-green-500 font-medium">Clinical Notes</h2>
          </motion.div>

          <div className="space-y-2">
            {symptomData.notes.map((note, index) => (
              <div key={index} className="bg-gray-100 rounded-lg p-3">
                <p className="text-sm">{note}</p>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Raw Data for Development */}
      {record?.rawData && (
        <motion.div
          className="bg-gray-100 p-4 rounded-lg mt-6 text-xs"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <details>
            <summary className="cursor-pointer font-medium mb-2">
              Raw FHIR Data (Dev Only)
            </summary>
            <pre className="overflow-auto max-h-96">
              {JSON.stringify(record.rawData, null, 2)}
            </pre>
          </details>
        </motion.div>
      )}

      {/* Additional enhancement: Display coding information */}
      {record?.rawData &&
        typeof record.rawData === "object" &&
        "code" in record.rawData &&
        record.rawData.code !== null &&
        typeof record.rawData.code === "object" &&
        record.rawData.code &&
        "coding" in record.rawData.code &&
        Array.isArray(record.rawData.code.coding) &&
        record.rawData.code.coding.length > 0 && (
          <motion.div
            className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              className="flex items-center mb-3"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <div className="text-purple-500 mr-2">
                <Image
                  src="/medical-record/note.svg"
                  alt="Medical Codes"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <h2 className="text-purple-500 font-medium">Diagnostic Codes</h2>
            </motion.div>

            <div className="space-y-2">
              {record.rawData.code.coding.map(
                (coding: FHIRCoding, index: number) => (
                  <div key={index} className="bg-gray-100 rounded-lg p-3">
                    <p className="text-xs text-gray-500">{coding.system}</p>
                    <p className="text-sm font-medium">
                      {coding.code} {coding.display && `- ${coding.display}`}
                    </p>
                  </div>
                )
              )}
            </div>
          </motion.div>
        )}
    </motion.div>
  );
}
