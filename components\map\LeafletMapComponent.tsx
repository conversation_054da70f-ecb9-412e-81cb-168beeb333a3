"use client";

import * as React from "react";
import { createComponent } from "@toolpad/studio/browser";
import { Map<PERSON>ontainer, TileLayer } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import MapUpdater from "./MapUpdater";
import PanToLocation from "./PanToLocation";
import MapMarkers from "./MapMarkers";
import { useWindowSize } from "@/hooks/useWindowSize";
import { ArrowLeft } from "lucide-react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";

// Базовий тип для LeafletProps (якщо він визначений у вашому проєкті)
export interface LeafletProps {
  lat: number;
  long: number;
  zoom: number;
}

// Розширений тип для LeafletMapProps
export interface LeafletMapProps extends LeafletProps {
  filteredEntities?: any[];
  selectedEntity?: any | null;
  setSelectedEntity?: (entity: any | null) => void;
  panTo?: { lat: number; long: number } | null;
  searchTerm?: string;
  setSearchTerm?: (term: string) => void;
  setIsListExpanded?: (value: boolean) => void; // додаємо новий проп
}

export function LeafletMap({
  lat,
  long,
  zoom,
  filteredEntities,
  selectedEntity,
  setSelectedEntity,
  panTo,
  searchTerm,
  setSearchTerm,
  setIsListExpanded,
}: LeafletMapProps) {
  const isDesktop = useWindowSize();
  const router = useRouter();
  const pathname = usePathname();
  // Fix path detection to check if pathname contains '/chats' anywhere in the path
  const isChatsRoute = pathname?.includes('/chats');

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (setSearchTerm) {
      // Перевірка на наявність setSearchTerm
      setSearchTerm(e.target.value);
    }
  };

  const filteredMarkers = React.useMemo(() => {
    if (!filteredEntities || !searchTerm) return filteredEntities;

    return filteredEntities.filter((entity) => {
      const searchTermLower = searchTerm.toLowerCase();
      return (
        String(entity.name || "")
          .toLowerCase()
          .includes(searchTermLower) ||
        String(entity.Specialties || "")
          .toLowerCase()
          .includes(searchTermLower) ||
        String(entity.address || "")
          .toLowerCase()
          .includes(searchTermLower) ||
        String(entity.ClinicName || "")
          .toLowerCase()
          .includes(searchTermLower)
      );
    });
  }, [filteredEntities, searchTerm]);

  return (
    <div className="relative h-full w-full">
      {!isDesktop && !isChatsRoute && (
        <div className="absolute top-2 left-2 right-2 z-10">
          <div className="relative flex items-center gap-2 rounded-full px-3 py-2">
            <button
              className="w-12 h-12 rounded-2xl flex items-center bg-[#FFFFFF] justify-center border-2 border-[#D4D4D4]"
              onClick={() => router.push("/chats")}
            >
              <ArrowLeft className="w-6 h-6 text-[#929292]" />
            </button>
            <div className="relative flex-1 backdrop-blur-[2px]">
              <div className="absolute left-3 top-1/2 -translate-y-1/2">
                <Image
                  src="/graysearch.svg"
                  alt="Search icon"
                  width={24}
                  height={24}
                  className=""
                />
              </div>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className="bg-[#FFFFFF] h-12 pl-10 pr-4 text-sm rounded-2xl border-2 border-[#D4D4D4] w-full text-[#00000080]"
              />
            </div>
          </div>
        </div>
      )}
      <MapContainer
        center={[lat, long]}
        zoom={zoom}
        scrollWheelZoom={true}
        style={{
          width: "100%",
          height: "100%",
          zIndex: 1,
          borderTopRightRadius: "1.5rem",
          borderBottomRightRadius: "1.5rem",
        }}
        zoomControl={false}
        attributionControl={false}
      >
        <TileLayer
          attribution=""
          url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png"
        />
        <MapUpdater lat={lat} long={long} />
        {panTo && <PanToLocation lat={panTo.lat} long={panTo.long} />}
        {filteredMarkers &&
          selectedEntity !== undefined &&
          setSelectedEntity && (
            <MapMarkers
              lat={lat}
              long={long}
              filteredEntities={filteredMarkers}
              setSelectedEntity={setSelectedEntity}
              selectedEntity={selectedEntity}
              setIsListExpanded={setIsListExpanded!}
            />
          )}
      </MapContainer>
    </div>
  );
}

// Експортуємо компонент із типами через createComponent
export default createComponent(LeafletMap, {
  argTypes: {
    lat: { type: "number", defaultValue: 51.505 },
    long: { type: "number", defaultValue: -0.09 },
    zoom: { type: "number", defaultValue: 13 },
    filteredEntities: { type: "object", defaultValue: [] },
    selectedEntity: { type: "object", defaultValue: null },
    setSelectedEntity: { type: "event" },
    panTo: { type: "object", defaultValue: null },
    searchTerm: { type: "string", defaultValue: "" },
    setSearchTerm: { type: "event" },
  },
});
