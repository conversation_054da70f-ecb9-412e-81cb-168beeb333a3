import { Amplify } from "aws-amplify";

import {
  handleGetChatHistory,
  handleSendMessage,
  handleCreateChatWithQuestionnaire,
  handleCreateHealthChat,
  handleResetQuestionnaire,
  handleCreateGroupChat,
  handleCreateDoctorChat,
} from "./handlers/chat-handlers";
import { handleStreamMessage } from "./handlers/stream-handlers";
import { IdentityInfo } from "./types";

import type { Hand<PERSON> } from "aws-lambda";

import { getAmplifyDataClientConfig } from "@aws-amplify/backend/function/runtime";
import { env } from "$amplify/env/requestOrchestrator";

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

// Configure Amplify with both Data API and Event API
Amplify.configure(
  {
    ...resourceConfig,
    API: {
      ...resourceConfig.API,
      Events: {
        endpoint: `https://${process.env.EVENT_API_ENDPOINT}/event`,
        region:
          process.env.EVENT_API_REGION || process.env.AWS_REGION || "us-east-1",
        defaultAuthMode: "iam",
      },
    },
  },
  libraryOptions
);



export const handler = async (event: any) => {
  console.log("Event: ", JSON.stringify(event, null, 2));

  const fieldName = event.fieldName;
  const args = event.arguments;

  // Extract user ID securely from identity context
  const userId = event.identity?.sub;

  if (!userId) {
    throw new Error("User authentication required");
  }

  const identity: IdentityInfo = {
    sub: userId,
    ...event.identity,
  };

  try {
    switch (fieldName) {
      case "getChatHistory":
        return await handleGetChatHistory(args, identity);
      case "sendMessage":
        return await handleSendMessage(args, identity);
      case "streamMessage":
        return await handleStreamMessage(args, identity);
      case "createChatWithQuestionnaire":
        return await handleCreateChatWithQuestionnaire(args, identity);
      case "createHealthChat":
        return await handleCreateHealthChat(args, identity);
      case "resetChat":
        return await handleResetQuestionnaire(args, identity);
      case "createGroupChat":
        return await handleCreateGroupChat(args, identity);
      case "createDoctorChat":
        return await handleCreateDoctorChat(args, identity);
      default:
        throw new Error(`Unknown field ${fieldName}`);
    }
  } catch (error) {
    console.error("Error: ", error);
    throw error;
  }
};
