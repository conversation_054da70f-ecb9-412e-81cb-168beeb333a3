{"version": 1, "models": {"Chat": {"name": "Cha<PERSON>", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "name": {"name": "name", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "description": {"name": "description", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "lastMessageAt": {"name": "lastMessageAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "metadata": {"name": "metadata", "isArray": false, "type": {"nonModel": "ChatMetadata"}, "isRequired": false, "attributes": []}, "chatType": {"name": "chatType", "isArray": false, "type": {"enum": "ChatChatType"}, "isRequired": false, "attributes": []}, "questionnaireId": {"name": "questionnaireId", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "messages": {"name": "messages", "isArray": true, "type": {"model": "ChatMessage"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["chatId"]}}, "chatParticipants": {"name": "chatParticipants", "isArray": true, "type": {"model": "ChatParticipant"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["chatId"]}}}, "syncable": true, "pluralName": "Chats", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "ChatMessage": {"name": "ChatMessage", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "chatId": {"name": "chatId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "messageType": {"name": "messageType", "isArray": false, "type": {"enum": "ChatMessageMessageType"}, "isRequired": false, "attributes": []}, "showMap": {"name": "showMap", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "attachments": {"name": "attachments", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "chat": {"name": "chat", "isArray": false, "type": {"model": "Cha<PERSON>"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["chatId"]}}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "ChatMessages", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["id"]}}, {"type": "key", "properties": {"name": "chatMessagesByChatIdAndCreatedAt", "queryField": "listMessagesByDate", "fields": ["chatId", "createdAt"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "User": {"name": "User", "fields": {"userId": {"name": "userId", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "name": {"name": "name", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "profilePicture": {"name": "profilePicture", "isArray": false, "type": "AWSURL", "isRequired": false, "attributes": []}, "userType": {"name": "userType", "isArray": false, "type": {"enum": "UserUserType"}, "isRequired": false, "attributes": []}, "chatParticipants": {"name": "chatParticipants", "isArray": true, "type": {"model": "ChatParticipant"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["userId"]}}, "medicalProfile": {"name": "medicalProfile", "isArray": false, "type": {"model": "MedicalProfile"}, "isRequired": false, "attributes": [], "association": {"connectionType": "HAS_ONE", "associatedWith": ["userId"], "targetNames": []}}, "vitalSigns": {"name": "vitalSigns", "isArray": true, "type": {"model": "VitalSign"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["userId"]}}, "medicalRecords": {"name": "medicalRecords", "isArray": true, "type": {"model": "MedicalRecord"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["userId"]}}, "comments": {"name": "comments", "isArray": true, "type": {"model": "Comment"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["userId"]}}, "posts": {"name": "posts", "isArray": true, "type": {"model": "Post"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["userId"]}}, "savedPosts": {"name": "savedPosts", "isArray": true, "type": {"model": "SavedPost"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["userId"]}}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Users", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["userId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": true, "primaryKeyFieldName": "userId", "sortKeyFieldNames": []}}, "UsersMedicalRecords": {"name": "UsersMedicalRecords", "fields": {"userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "recordId": {"name": "recordId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "resourceType": {"name": "resourceType", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "rawData": {"name": "rawData", "isArray": false, "type": "AWSJSON", "isRequired": true, "attributes": []}, "lastUpdated": {"name": "lastUpdated", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}, "category": {"name": "category", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "date": {"name": "date", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "provider": {"name": "provider", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "medicalSystem": {"name": "medicalSystem", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "externalId": {"name": "externalId", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "status": {"name": "status", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}}, "syncable": true, "pluralName": "UsersMedicalRecords", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["userId", "recordId"]}}, {"type": "key", "properties": {"name": "usersMedicalRecordsByUserIdAndDate", "queryField": "listUsersMedicalRecordsByDate", "fields": ["userId", "date"]}}, {"type": "key", "properties": {"name": "usersMedicalRecordsByUserIdAndResourceType", "queryField": "listUsersMedicalRecordsByType", "fields": ["userId", "resourceType"]}}, {"type": "key", "properties": {"name": "usersMedicalRecordsByUserIdAndProvider", "queryField": "listUsersMedicalRecordsByProvider", "fields": ["userId", "provider"]}}, {"type": "key", "properties": {"name": "usersMedicalRecordsByUserIdAndCategory", "queryField": "listUsersMedicalRecordsByCategory", "fields": ["userId", "category"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": true, "primaryKeyFieldName": "userId", "sortKeyFieldNames": ["recordId"]}}, "ALSCenter": {"name": "ALSCenter", "fields": {"ClinicName": {"name": "ClinicName", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "DoctorName": {"name": "<PERSON><PERSON><PERSON>", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "WebsiteURL": {"name": "WebsiteURL", "isArray": false, "type": "AWSURL", "isRequired": false, "attributes": []}, "ZipCode": {"name": "ZipCode", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "Location": {"name": "Location", "isArray": false, "type": {"nonModel": "ALSCenterLocation"}, "isRequired": false, "attributes": []}, "Specialties": {"name": "Specialties", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "Distance": {"name": "Distance", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "PhoneNumber": {"name": "PhoneNumber", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "ALSCenters", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["ClinicName", "<PERSON><PERSON><PERSON>"]}}, {"type": "key", "properties": {"name": "aLSCentersByZipCode", "queryField": "listCentersByZipCode", "fields": ["ZipCode"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": true, "primaryKeyFieldName": "ClinicName", "sortKeyFieldNames": ["<PERSON><PERSON><PERSON>"]}}, "MedicalProfile": {"name": "MedicalProfile", "fields": {"userId": {"name": "userId", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "chatId": {"name": "chatId", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "profileData": {"name": "profileData", "isArray": false, "type": "AWSJSON", "isRequired": false, "attributes": []}, "age": {"name": "age", "isArray": false, "type": "Int", "isRequired": false, "attributes": []}, "gender": {"name": "gender", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "country": {"name": "country", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "height": {"name": "height", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "weight": {"name": "weight", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "medicalHistory": {"name": "medicalHistory", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}}, "syncable": true, "pluralName": "MedicalProfiles", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["userId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": true, "primaryKeyFieldName": "userId", "sortKeyFieldNames": []}}, "VitalSign": {"name": "VitalSign", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "timestamp": {"name": "timestamp", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}, "heartRate": {"name": "heartRate", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "bloodPressureSystolic": {"name": "bloodPressureSystolic", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "bloodPressureDiastolic": {"name": "bloodPressureDiastolic", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "sleepDuration": {"name": "sleepDuration", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "VitalSigns", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"name": "vitalSignsByUserIdAndTimestamp", "queryField": "listVitalSignsByDate", "fields": ["userId", "timestamp"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "MedicalRecord": {"name": "MedicalRecord", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "fileUrl": {"name": "fileUrl", "isArray": false, "type": "AWSURL", "isRequired": true, "attributes": []}, "recordType": {"name": "recordType", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "uploadDate": {"name": "uploadDate", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "metadata": {"name": "metadata", "isArray": false, "type": "AWSJSON", "isRequired": false, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "MedicalRecords", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"name": "medicalRecordsByUserIdAndUploadDate", "queryField": "listMedicalRecordsByDate", "fields": ["userId", "uploadDate"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "HealthQuestion": {"name": "HealthQuestion", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "prompt": {"name": "prompt", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "category": {"name": "category", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "questionnaireQuestions": {"name": "questionnaireQuestions", "isArray": true, "type": {"model": "QuestionnaireQuestion"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["healthQuestionId"]}}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "HealthQuestions", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "Questionnaire": {"name": "Questionnaire", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "name": {"name": "name", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "questionnaireQuestions": {"name": "questionnaireQuestions", "isArray": true, "type": {"model": "QuestionnaireQuestion"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["questionnaireId"]}}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Questionnaires", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "QuestionnaireQuestion": {"name": "QuestionnaireQuestion", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "questionnaireId": {"name": "questionnaireId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "healthQuestionId": {"name": "healthQuestionId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "sequence": {"name": "sequence", "isArray": false, "type": "Int", "isRequired": true, "attributes": []}, "questionnaire": {"name": "questionnaire", "isArray": false, "type": {"model": "Questionnaire"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["questionnaireId"]}}, "healthQuestion": {"name": "healthQuestion", "isArray": false, "type": {"model": "HealthQuestion"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["healthQuestionId"]}}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "QuestionnaireQuestions", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"name": "questionnaireQuestionsByQuestionnaireIdAndSequence", "queryField": "listQuestionnaireQuestionsBySequence", "fields": ["questionnaireId", "sequence"]}}, {"type": "key", "properties": {"name": "questionnaireQuestionsByHealthQuestionId", "queryField": "listQuestionnaireQuestionsByHealthQuestion", "fields": ["healthQuestionId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "Comment": {"name": "Comment", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "postId": {"name": "postId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "content": {"name": "content", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "parentCommentId": {"name": "parentCommentId", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "likes": {"name": "likes", "isArray": false, "type": "Int", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "post": {"name": "post", "isArray": false, "type": {"model": "Post"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["postId"]}}, "replies": {"name": "replies", "isArray": true, "type": {"model": "Comment"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["parentCommentId"]}}, "parent": {"name": "parent", "isArray": false, "type": {"model": "Comment"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["parentCommentId"]}}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Comments", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["id"]}}, {"type": "key", "properties": {"name": "commentsByPostIdAndCreatedAt", "queryField": "listCommentsByPost", "fields": ["postId", "createdAt"]}}, {"type": "key", "properties": {"name": "commentsByParentCommentId", "queryField": "listCommentReplies", "fields": ["parentCommentId"]}}, {"type": "key", "properties": {"name": "gsi-Comment.replies", "fields": ["parentCommentId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "Post": {"name": "Post", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "userName": {"name": "userName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "title": {"name": "title", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "content": {"name": "content", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "images": {"name": "images", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "likes": {"name": "likes", "isArray": false, "type": "Int", "isRequired": false, "attributes": []}, "commentsCount": {"name": "commentsCount", "isArray": false, "type": "Int", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "comments": {"name": "comments", "isArray": true, "type": {"model": "Comment"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["postId"]}}, "savedBy": {"name": "savedBy", "isArray": true, "type": {"model": "SavedPost"}, "isRequired": false, "attributes": [], "isArrayNullable": true, "association": {"connectionType": "HAS_MANY", "associatedWith": ["postId"]}}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Posts", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["id"]}}, {"type": "key", "properties": {"name": "postsByUserIdAndCreatedAt", "queryField": "listPostsByUser", "fields": ["userId", "createdAt"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "SavedPost": {"name": "SavedPost", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "postId": {"name": "postId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "post": {"name": "post", "isArray": false, "type": {"model": "Post"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["postId"]}}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "SavedPosts", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["id"]}}, {"type": "key", "properties": {"name": "savedPostsByUserIdAndCreatedAt", "queryField": "listSavedPostsByUser", "fields": ["userId", "createdAt"]}}, {"type": "key", "properties": {"name": "savedPostsByPostId", "queryField": "listSavedPostsByPost", "fields": ["postId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "HealthDataConnection": {"name": "HealthDataConnection", "fields": {"appUserId": {"name": "appUserId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "status": {"name": "status", "isArray": false, "type": {"enum": "HealthDataConnectionStatus"}, "isRequired": false, "attributes": []}, "lastSync": {"name": "lastSync", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "accessToken": {"name": "accessToken", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "refreshToken": {"name": "refreshToken", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "tokenExpiration": {"name": "tokenExpiration", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}, "refreshTokenExpiration": {"name": "refreshTokenExpiration", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "scope": {"name": "scope", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "token_type": {"name": "token_type", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}, "ttl": {"name": "ttl", "isArray": false, "type": "Int", "isRequired": false, "attributes": []}}, "syncable": true, "pluralName": "HealthDataConnections", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["appUserId"]}}, {"type": "key", "properties": {"name": "healthDataConnectionsByUserId", "queryField": "listHealthDataConnectionsByUser", "fields": ["userId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": true, "primaryKeyFieldName": "appUserId", "sortKeyFieldNames": []}}, "UserConnectedSystem": {"name": "UserConnectedSystem", "fields": {"userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "systemId": {"name": "systemId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "status": {"name": "status", "isArray": false, "type": {"enum": "UserConnectedSystemStatus"}, "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": true, "attributes": []}}, "syncable": true, "pluralName": "UserConnectedSystems", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["userId", "systemId"]}}, {"type": "key", "properties": {"name": "userConnectedSystemsByUserId", "queryField": "listUserConnectedSystemsByUser", "fields": ["userId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": true, "primaryKeyFieldName": "userId", "sortKeyFieldNames": ["systemId"]}}, "ChatParticipant": {"name": "ChatParticipant", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "chatId": {"name": "chatId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "joinedAt": {"name": "joinedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "chat": {"name": "chat", "isArray": false, "type": {"model": "Cha<PERSON>"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["chatId"]}}, "user": {"name": "user", "isArray": false, "type": {"model": "User"}, "isRequired": false, "attributes": [], "association": {"connectionType": "BELONGS_TO", "targetNames": ["userId"]}}, "lastMessageAt": {"name": "lastMessageAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "ChatParticipants", "attributes": [{"type": "model", "properties": {}}, {"type": "key", "properties": {"fields": ["id"]}}, {"type": "key", "properties": {"name": "chatParticipantsByChatId", "queryField": "listChatParticipantsByChat", "fields": ["chatId"]}}, {"type": "key", "properties": {"name": "chatParticipantsByUserId", "queryField": "listChatParticipantsByUser", "fields": ["userId"]}}, {"type": "auth", "properties": {"rules": [{"allow": "public", "provider": "<PERSON><PERSON><PERSON><PERSON>", "operations": ["create", "update", "delete", "read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {"MessageType": {"name": "MessageType", "values": ["TEXT", "IMAGE", "FILE", "SYSTEM", "AUDIO", "VIDEO"]}, "UserStatus": {"name": "UserStatus", "values": ["ONLINE", "OFFLINE", "AWAY", "DO_NOT_DISTURB"]}, "ChatChatType": {"name": "ChatChatType", "values": ["GROUP", "DIRECT"]}, "ChatMessageMessageType": {"name": "ChatMessageMessageType", "values": ["TEXT", "IMAGE", "FILE", "SYSTEM", "AUDIO", "VIDEO"]}, "UserUserType": {"name": "UserUserType", "values": ["HUMAN", "AI", "SYSTEM"]}, "SendMessageMessageType": {"name": "SendMessageMessageType", "values": ["TEXT", "IMAGE", "FILE", "SYSTEM", "AUDIO", "VIDEO"]}, "ClientInfoTypeDeviceType": {"name": "ClientInfoTypeDeviceType", "values": ["MOBILE", "DESKTOP", "TABLET"]}}, "nonModels": {"ClientInfoType": {"name": "ClientInfoType", "fields": {"browser": {"name": "browser", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "platform": {"name": "platform", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "deviceType": {"name": "deviceType", "isArray": false, "type": {"enum": "ClientInfoTypeDeviceType"}, "isRequired": false, "attributes": []}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "isArray": false, "type": "AWSIPAddress", "isRequired": false, "attributes": []}, "userAgent": {"name": "userAgent", "isArray": false, "type": "String", "isRequired": false, "attributes": []}}}, "ChatMetadata": {"name": "ChatMetadata", "fields": {"createdBy": {"name": "created<PERSON>y", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "isArchived": {"name": "isArchived", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "category": {"name": "category", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "configuration": {"name": "configuration", "isArray": false, "type": "AWSJSON", "isRequired": false, "attributes": []}, "currentQuestionIndex": {"name": "currentQuestionIndex", "isArray": false, "type": "Int", "isRequired": false, "attributes": []}, "isQuestionnaireComplete": {"name": "isQuestionnaireComplete", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "questionnaireCompletedAt": {"name": "questionnaireCompletedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "diagnosisMessageSent": {"name": "diagnosisMessageSent", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "earlyCompletion": {"name": "earlyCompletion", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}}}, "ALSCenterLocation": {"name": "ALSCenterLocation", "fields": {"LatLng": {"name": "LatLng", "isArray": false, "type": {"nonModel": "ALSCenterLocationLatLng"}, "isRequired": false, "attributes": []}}}, "ALSCenterLocationLatLng": {"name": "ALSCenterLocationLatLng", "fields": {"Lat": {"name": "Lat", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "Lng": {"name": "Lng", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}}}}, "mutations": {"createChatWithQuestionnaire": {"name": "createChatWithQuestionnaire", "isArray": false, "type": {"model": "Cha<PERSON>"}, "isRequired": false, "arguments": {"userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true}, "questionnaireId": {"name": "questionnaireId", "isArray": false, "type": "String", "isRequired": true}, "name": {"name": "name", "isArray": false, "type": "String", "isRequired": true}, "description": {"name": "description", "isArray": false, "type": "String", "isRequired": false}, "metadata": {"name": "metadata", "isArray": false, "type": {"input": "CreateChatWithQuestionnaireMetadataInput"}, "isRequired": false}}}, "sendMessage": {"name": "sendMessage", "isArray": false, "type": {"model": "ChatMessage"}, "isRequired": false, "arguments": {"chatId": {"name": "chatId", "isArray": false, "type": "String", "isRequired": true}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": true}, "messageType": {"name": "messageType", "isArray": false, "type": {"enum": "SendMessageMessageType"}, "isRequired": false}, "attachments": {"name": "attachments", "isArray": true, "type": "String", "isRequired": false, "isArrayNullable": true}}}, "streamMessage": {"name": "streamMessage", "isArray": false, "type": {"model": "ChatMessage"}, "isRequired": false, "arguments": {"userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true}, "chatId": {"name": "chatId", "isArray": false, "type": "String", "isRequired": true}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": true}, "eventConfig": {"name": "eventConfig", "isArray": false, "type": {"input": "StreamMessageEventConfigInput"}, "isRequired": false}}}, "resetChat": {"name": "resetChat", "isArray": false, "type": {"model": "Cha<PERSON>"}, "isRequired": false, "arguments": {"chatId": {"name": "chatId", "isArray": false, "type": "String", "isRequired": true}}}}, "inputs": {"CreateChatWithQuestionnaireMetadataInput": {"name": "CreateChatWithQuestionnaireMetadataInput", "attributes": {"createdBy": {"name": "created<PERSON>y", "isArray": false, "type": "String", "isRequired": true}, "isArchived": {"name": "isArchived", "isArray": false, "type": "Boolean", "isRequired": false}, "category": {"name": "category", "isArray": false, "type": "String", "isRequired": false}, "configuration": {"name": "configuration", "isArray": false, "type": "AWSJSON", "isRequired": false}}}, "StreamMessageEventConfigInput": {"name": "StreamMessageEventConfigInput", "attributes": {"channelName": {"name": "channelName", "isArray": false, "type": "String", "isRequired": true}, "sessionId": {"name": "sessionId", "isArray": false, "type": "String", "isRequired": true}, "namespace": {"name": "namespace", "isArray": false, "type": "String", "isRequired": false}}}}}