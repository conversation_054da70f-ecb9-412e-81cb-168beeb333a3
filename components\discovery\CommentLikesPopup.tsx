import Image from "next/image"
import { X } from "lucide-react"

interface CommentLikesPopupProps {
  isOpen: boolean;
  onClose: () => void;
  likes: Array<{
    id: string;
    userId: string;
    user: {
      name: string;
      profilePicture?: string;
    };
  }>;
}

export default function CommentLikesPopup({ isOpen, onClose, likes }: CommentLikesPopupProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg w-full max-w-md mx-4">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="text-lg font-semibold">Likes</h3>
          <button onClick={onClose}>
            <X className="w-6 h-6" />
          </button>
        </div>
        <div className="max-h-[60vh] overflow-y-auto">
          {likes.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              No likes yet
            </div>
          ) : (
            likes.map((like) => (
              <div key={like.id} className="flex items-center p-4 hover:bg-gray-50">
                <div className="w-10 h-10 rounded-full overflow-hidden">
                  <Image
                    src={like.user?.profilePicture || "/groupavatar.svg"}
                    alt={like.user?.name || "User"}
                    width={40}
                    height={40}
                    className="object-cover"
                  />
                </div>
                <span className="ml-3 font-medium">
                  {like.user?.name || like.userId}
                </span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
