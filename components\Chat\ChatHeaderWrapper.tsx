import { useRouter } from "next/navigation";
import { useMemo } from "react";
import { AI_ASSISTANT_ID } from "@/constants/chat";
import { useUser } from "@/hooks/useUsers";
import { useCurrentChat } from "./context/ChatContext";
import { ChatParticipant, ChatParticipantFormatted } from "@/types/chat";
import ChatHeader from "./ChatHeader";

interface ChatHeaderWrapperProps {
  onGroupDetailsClick?: () => void;
  handleLeaveGroup?: () => Promise<void>;
}

const ChatHeaderWrapper = ({
  onGroupDetailsClick,
  handleLeaveGroup,
}: ChatHeaderWrapperProps) => {
  const router = useRouter();
  const { currentChat } = useCurrentChat();
  const { currentUser } = useUser();
  const userId = currentUser?.userId || null;

  const chatData = useMemo(() => {
    if (!currentChat) return null;
    const { id, name, chatType, chatParticipants } = currentChat;

    const defaultMembers: ChatParticipantFormatted[] = [];

    const members: ChatParticipantFormatted[] =
      chatParticipants && userId
        ? chatParticipants
            .filter(
              (p) =>
                p.userId !== AI_ASSISTANT_ID && p.userId !== userId && p.user
            )
            .map((p: ChatParticipant) => ({
              userId: p.userId,
              user: p.user
                ? {
                    email: p.user.email || undefined,
                    name: p.user.name || "Unknown User",
                    profilePicture: p.user.profilePicture || undefined,
                  }
                : undefined,
            }))
        : defaultMembers;

    return {
      id,
      name: name || "Chat",
      type: chatType || "AI",
      members,
      isGroup: chatType === "GROUP",
    };
  }, [currentChat, userId]);

  const handleBack = () => {
    router.push("/chats");
  };

  if (!chatData) {
    return null;
  }

  return (
    <ChatHeader
      chatData={chatData}
      onGroupDetailsClick={onGroupDetailsClick}
      onBackClick={handleBack}
      handleLeaveGroup={handleLeaveGroup}
    />
  );
};

export default ChatHeaderWrapper;
