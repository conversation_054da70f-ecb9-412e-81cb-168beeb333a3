import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { X, ChevronDown } from "lucide-react";
import { useState, useCallback, useEffect } from "react";
import Image from "next/image";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { ChatDropDown } from "./ChatDropDown";
import { ChatParticipantFormatted } from "@/types/chat";
import { useIsMobile } from "@/hooks/use-mobile";
import PageHeader from "@/components/ui/PageHeader";
import { ActionButton } from "../common/ActionButton";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { fetchAuthSession } from "aws-amplify/auth";

import { Schema } from "@/amplify/data/resource";
import { generateClient } from "@aws-amplify/api";

const client = generateClient<Schema>();

export type ChatParticipant = ChatParticipantFormatted;

interface ChatDropDownGroupProps {
  chatName: string;
  chatId: string;
  members: ChatParticipant[];
  handleLeaveGroup: () => Promise<void>;
}

export function ChatDropDownGroup({
  chatName,
  chatId,
  members = [],
  handleLeaveGroup,
}: ChatDropDownGroupProps) {
  const [groupName, setGroupName] = useState(chatName);
  const [isEditing, setIsEditing] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [showAllMembers, setShowAllMembers] = useState(false);
  const isMobile = useIsMobile();
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  // Get current user ID to filter out from members list
  useEffect(() => {
    const getCurrentUserId = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.sub) {
          const userId = String(session.tokens.idToken.payload.sub);
          setCurrentUserId(userId);
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    getCurrentUserId();
  }, []);

  const filteredMembers = members.filter(
    (member) => member.userId !== currentUserId
  );

  const visibleMembers = showAllMembers
    ? filteredMembers
    : filteredMembers.slice(0, 8);

  const hasMoreMembers = filteredMembers.length > 8;

  const handleSave = useCallback(async () => {
    console.log(`Updating chat ${chatId} name to ${groupName}`);

    await client.models.Chat.update({
      id: chatId,
      name: groupName,
    });

    setIsEditing(false);
  }, [chatId, groupName]);

  const GroupContent = ({ scrollable = false }: { scrollable?: boolean }) => (
    <div className={`flex flex-col ${scrollable ? "flex-1" : ""}`}>
      {/* <div className="p-4 space-y-3"> */}
      {/* <h2 className="font-semibold text-[16px] text-[#171717] font-figtree text-center">
          {isEditing ? "Edit Group Name" : groupName}
        </h2>
        {isEditing ? (
          <>
            <div className="relative">
              <Input
                className="w-full h-9 rounded-full bg-gray-100 border-gray-300 pl-4 text-[16px] font-figtree"
                placeholder="Group Name"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
              />
            </div>
            <Button
              className="w-full py-1.5 bg-[#5185FF] hover:bg-[#4175EF] text-white text-[16px] rounded-full font-figtree"
              onClick={handleSave}
            >
              Save
            </Button>
          </>
        ) : (
          <Button
            className="w-full py-1.5 bg-[#5185FF] hover:bg-[#4175EF] text-white text-[16px] rounded-full font-figtree"
            onClick={() => setIsEditing(true)}
          >
            Edit Name
          </Button>
        )} */}
      {/* </div> */}

      <div className="overflow-y-auto max-h-[350px] bg-white">
        <div className="px-5 py-3 flex max-sm:flex-wrap md:flex-col gap-6 w-full">
          {visibleMembers.length === 0 ? (
            <div className="text-center text-gray-500 py-4 col-span-3 font-figtree">
              No members found
            </div>
          ) : (
            visibleMembers.map((member, i) => (
              <ChatDropDown userData={member} key={`${member.userId}-${i}`}>
                <div className="flex max-sm:flex-col items-center gap-2">
                  <Avatar className="h-12 w-12 border-2 border-white shadow-sm">
                    {/* {member.user?.profilePicture ? ( */}
                    <AvatarImage
                      src={"/Avatar.png"}
                      // alt={member.user.name || `Member ${i + 1}`}
                    />
                    {/*    ) : (
                       <AvatarFallback className="bg-[#0051FF] text-white">
                         {member.user?.name?.charAt(0) || `${i + 1}`}
                       </AvatarFallback>
                    )} */}
                  </Avatar>
                  <span className="max-sm:truncate max-w-20 text-[12px] font-medium text-black text-center font-figtree">
                    {member.user?.email || `User ${i + 1}`}
                  </span>
                </div>
              </ChatDropDown>
            ))
          )}
        </div>
      </div>

      {hasMoreMembers && !showAllMembers && (
        <div className="flex justify-center px-5 py-3 border-t border-gray-100">
          <Button
            variant="outline"
            className="rounded-full py-3 px-4 flex items-center gap-2 border shadow-md"
            onClick={() => setShowAllMembers(true)}
          >
            <span className="text-[#929292] font-medium text-[16px] font-figtree">
              More
            </span>
            <ChevronDown className="h-5 w-5 text-[#929292]" />
          </Button>
        </div>
      )}

      <div
        className={`border-gray-200 px-5 py-2 w-full bg-white mt-3 ${
          isMobile ? "mb-4" : ""
        }`}
      >
        <ActionButton
          icon={X}
          text="Leave Group"
          onConfirm={handleLeaveGroup}
          confirmationTitle="Leave Group"
          confirmationMessage="Are you sure you want to leave this group? You won't receive messages from this group anymore."
          confirmationButtonText="Leave Group"
        />
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <>
        <Image
          alt="chat details"
          src="/chats/chat-details.svg"
          width={24}
          height={24}
          className="cursor-pointer"
          onClick={() => setIsOpen(true)}
        />
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent
            className="p-0 max-w-full sm:max-w-md h-screen sm:h-auto flex flex-col rounded-none sm:rounded-xl bg-[#F5F5F5] "
            showCloseButton={false}
          >
            <DialogHeader>
              <PageHeader
                title={
                  <span className="text-[16px] font-semibold font-figtree">
                    {groupName}
                  </span>
                }
                leftContent={
                  <Button
                    variant="ghost"
                    className="p-2 mr-2"
                    onClick={() => setIsOpen(false)}
                  >
                    <Image
                      alt="back"
                      src="/arrow-left.svg"
                      width={24}
                      height={24}
                    />
                  </Button>
                }
                background="white"
                className="border-b border-gray-200"
                sticky={true}
              />
            </DialogHeader>

            <GroupContent scrollable={true} />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <DropdownMenu onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Image
          alt="chat details"
          src="/chats/chat-details.svg"
          width={24}
          height={24}
          className="cursor-pointer"
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[330px] p-0 rounded-2xl shadow-lg border-none mt-2 relative max-h-[480px] flex flex-col">
        {/* Triangle pointer at top */}
        <div className="absolute -top-2 right-4 w-4 h-4 bg-white rotate-45"></div>
        <GroupContent />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
