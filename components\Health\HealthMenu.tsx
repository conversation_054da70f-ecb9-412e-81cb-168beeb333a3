import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useWindowSize } from "@/hooks/useWindowSize";
import MedicalRecordModal from "./MedicalRecordModal";
import MedicationModal from "./MedicationModal";
import SymptomModal from "./SymptomModal";
import GoalSettingModal from "./GoalSettingModal";
import { useUser } from "@/hooks/useUsers";
import {
  OneUpHealthService,
  type ConnectedSystem,
  type MedicalRecord,
} from "@/lib/services/oneUpHealthService";
import { toast } from "sonner";

interface HealthMenuProps {
  isVisible?: boolean;
  onPanelToggle: (isOpen: boolean) => void;
  isPanelOpen?: boolean;
}

// Make sure userInfo has all required fields for MedicalRecordModal
const defaultUserInfo = {
  name: "-",
  birthDate: "-",
  birthYear: "-",
  gender: "-",
  region: "-",
  race: "-",
  ethnicity: "-",
  maritalStatus: "-",
  email: "-",
  mobilePhone: "-",
  workPhone: "-",
  address: "-",
  primaryLanguage: "-",
  secondaryLanguage: "-",
};

const HealthMenu = ({
  isVisible = true,
  onPanelToggle,
  isPanelOpen = false,
}: HealthMenuProps) => {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const isDesktop = useWindowSize();
  const { currentUser } = useUser();

  // Health data states
  const [oneUpService, setOneUpService] = useState<OneUpHealthService | null>(
    null
  );
  const [isDataConnected, setIsDataConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userInfo, setUserInfo] = useState(defaultUserInfo);
  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [connectedSystems, setConnectedSystems] = useState<ConnectedSystem[]>(
    []
  );

  // Initialize OneUpHealth service when user is available
  useEffect(() => {
    if (!currentUser) return;

    const config = {
      userId: currentUser.userId,
      clientId:
        process.env.NEXT_PUBLIC_ONEUP_CLIENT_ID ||
        "591e25edc3e5013a7743658a26875d3a",
      clientSecret:
        process.env.NEXT_PUBLIC_ONEUP_CLIENT_SECRET ||
        "2cc1b5cd9264a67f8666ac48aaf2a265",
    };

    const service = new OneUpHealthService(config);
    setOneUpService(service);
  }, [currentUser]);

  // Check token validity
  const isTokenValid = (): boolean => {
    if (!currentUser) return false;

    const expirationTimeStr = localStorage.getItem(
      `oneup_token_expiration_${currentUser.userId}`
    );
    if (!expirationTimeStr) return false;

    const expirationTime = new Date(expirationTimeStr).getTime();
    return Date.now() < expirationTime - 30000;
  };

  // Refresh token function
  const refreshToken = async (
    service: OneUpHealthService
  ): Promise<boolean> => {
    if (!currentUser) return false;

    try {
      const refreshToken = localStorage.getItem(
        `oneup_refresh_token_${currentUser.userId}`
      );
      if (!refreshToken) return false;

      const accessToken = localStorage.getItem(
        `oneup_access_token_${currentUser.userId}`
      );
      if (accessToken) {
        service.setAccessToken(accessToken);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to refresh token:", error);
      return false;
    }
  };

  // Function to fetch health data
  const fetchHealthData = async () => {
    if (!oneUpService || !currentUser) return;

    setIsLoading(true);
    try {
      // Check if token is valid or try to refresh
      if (!isTokenValid()) {
        const refreshed = await refreshToken(oneUpService);
        if (!refreshed) {
          setIsDataConnected(false);
          setIsLoading(false);
          return;
        }
      }

      // Try to initialize and fetch patient data
      const initialized = await oneUpService.initialize();
      if (initialized) {
        const patient = await oneUpService.fetchPatientData();
        if (patient) {
          setIsDataConnected(true);
          setUserInfo({
            name: patient?.name || "-",
            birthDate: patient?.birthDate || "-",
            birthYear: patient?.birthYear || "-",
            gender: patient?.gender || "-",
            region: patient?.region || "-",
            race: patient?.race || "-",
            ethnicity: patient?.ethnicity || "-",
            maritalStatus: (patient as any)?.maritalStatus || "-",
            email: patient?.email || "-",
            mobilePhone: (patient as any)?.mobilePhone || "-",
            workPhone: (patient as any)?.workPhone || "-",
            address: patient?.address || "-",
            primaryLanguage: (patient as any)?.primaryLanguage || "-",
            secondaryLanguage: (patient as any)?.secondaryLanguage || "-",
          });

          // Fetch medical records
          const records = await oneUpService.fetchMedicalRecords();
          setMedicalRecords(records.length > 0 ? records : []);

          // Fetch connected systems
          const systems = await oneUpService.getConnectedHealthSystems();
          setConnectedSystems(systems);
        } else {
          setIsDataConnected(false);
        }
      } else {
        setIsDataConnected(false);
      }
    } catch (error) {
      console.error("Error fetching health data:", error);
      setIsDataConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isMenuOpen) {
      setIsAnimating(true);
    }
  }, [isMenuOpen]);

  const closeMenu = () => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsMenuOpen(false);
    }, 300); // Match transition duration
  };

  const toggleMenu = () => {
    if (isMenuOpen) {
      closeMenu();
    } else {
      setIsMenuOpen(true);
    }
  };

  const handleBackdropClick = () => {
    closeMenu();
  };

  const closeModal = () => {
    setActiveModal(null);
  };

  const navigateTo = (path: string) => {
    // Extract the route name without the leading slash
    const route = path.substring(1);

    // On desktop, show modals instead of navigating
    if (isDesktop) {
      // When opening medical records modal, fetch the data
      if (route === "medical-record") {
        fetchHealthData();
      }
      setActiveModal(route);
    } else {
      // On mobile, navigate to the separate page
      router.push(path);
    }

    // Close the menu
    closeMenu();
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Menu Button */}
      <div
        className={`${!isDesktop ? "bg-white" : ""} w-10 h-10 rounded-2xl flex items-center justify-center cursor-pointer`}
        onClick={toggleMenu}
      >
        <Image
          src="/menu-deepg.svg"
          alt="Menu"
          width={24}
          height={24}
          className="w-6 h-6"
        />
      </div>

      {/* Dropdown Menu for all devices */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 z-30 flex items-start justify-end pt-[100px] pr-6 transition-opacity duration-300 ease-in-out"
          style={{ opacity: isAnimating ? 1 : 0 }}
          onClick={handleBackdropClick}
        >
          <div
            className={`bg-white rounded-3xl shadow-lg p-2 w-[170px] h-[176px] overflow-y-auto transition-all duration-300 ease-in-out ${
              isAnimating
                ? "transform-none opacity-100"
                : "transform translate-x-4 opacity-0"
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            <div
              className="flex items-center cursor-pointer"
              onClick={() => navigateTo("/medical-record")}
            >
              <div className="w-10 h-10 rounded-xl flex items-center justify-center">
                <Image
                  src="/health/nurse.svg"
                  alt="Medical Record"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <span className="ml-2 text-sm font-medium">Medical Record</span>
            </div>

            <div
              className="flex items-center cursor-pointer"
              onClick={() => navigateTo("/goal-seatting")}
            >
              <div className="w-10 h-10 rounded-xl flex items-center justify-center">
                <Image
                  src="/health/target.svg"
                  alt="Goal Setting"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <span className="ml-2 text-sm font-medium">Goal Setting</span>
            </div>

            <div
              className="flex items-center cursor-pointer"
              onClick={() => navigateTo("/medication")}
            >
              <div className="w-10 h-10 rounded-xl flex items-center justify-center">
                <Image
                  src="/health/capsule.svg"
                  alt="Medication"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <span className="ml-2 text-sm font-medium">Medication</span>
            </div>

            <div
              className="flex items-center cursor-pointer"
              onClick={() => navigateTo("/symptom")}
            >
              <div className="w-10 h-10 rounded-xl flex items-center justify-center">
                <Image
                  src="/health/polygon.svg"
                  alt="Symptom"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <span className="ml-2 text-sm font-medium">Symptom</span>
            </div>
          </div>
        </div>
      )}

      {/* Modals for desktop */}
      {isDesktop && activeModal === "medical-record" && (
        <MedicalRecordModal
          onClose={closeModal}
          userInfo={userInfo}
          records={medicalRecords}
          connectedSystems={connectedSystems}
          isLoading={isLoading}
          oneUpService={oneUpService}
          userId={currentUser?.userId}
          isTokenValid={isTokenValid}
          refreshToken={refreshToken}
        />
      )}

      {isDesktop && activeModal === "medication" && (
        <MedicationModal onClose={closeModal} />
      )}

      {isDesktop && activeModal === "goal-seatting" && (
        <GoalSettingModal onClose={closeModal} />
      )}

      {isDesktop && activeModal === "symptom" && (
        <SymptomModal onClose={closeModal} />
      )}
    </>
  );
};

export default HealthMenu;
