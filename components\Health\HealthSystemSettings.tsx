import { useState, useEffect } from "react";
import HealthSystemConnectedList from "./HealthSystemConnectedList";
import HealthSystemAvailableList from "./HealthSystemAvailableList";
import { motion, AnimatePresence } from "framer-motion";
import { HealthSystemManager } from "./HealthSystemMobile";

interface HealthSystemSettingsProps {
  onClose: () => void;
}

const HealthSystemSettings = ({ onClose }: HealthSystemSettingsProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [showMobilePopup, setShowMobilePopup] = useState(false);

  // Detect mobile
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 768);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  if (isMobile) {
    return <HealthSystemManager isModal={true} onClose={onClose} />;
  }

  return (
    <div className="fixed inset-0 flex justify-end items-stretch z-50 p-4 md:py-6 md:pr-6">
      <div className="bg-white w-full md:w-[940px] h-full rounded-2xl shadow-xl flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between py-4 md:py-5 px-4 md:px-6 h-[56px] border-b">
          <div className="flex items-center space-x-3">
            <button onClick={onClose} className="">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M15 18l-6-6 6-6" />
              </svg>
            </button>
            <h2 className="text-lg font-medium">Add Medical Record</h2>
          </div>
          <button onClick={onClose} className="p-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        {/* Mobile/Desktop layout */}
        <div className="flex flex-col md:flex-row flex-1 overflow-hidden">
          <HealthSystemConnectedList
            setSearchQuery={setSearchQuery}
            onAddMoreClick={
              isMobile ? () => setShowMobilePopup(true) : undefined
            }
          />

          {!isMobile && (
            <HealthSystemAvailableList
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
            />
          )}
        </div>
      </div>

      <AnimatePresence>
        {isMobile && showMobilePopup && (
          <>
            <motion.div
              className="fixed inset-0 bg-black bg-opacity-40 z-40"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setShowMobilePopup(false)}
            />
            <motion.div
              className="fixed left-0 right-0 bottom-0 bg-white rounded-t-3xl z-50 p-4 flex flex-col h-[90%]"
              initial={{ y: "100%" }}
              animate={{ y: 0 }}
              exit={{ y: "100%" }}
              transition={{ type: "spring", damping: 20 }}
            >
              <div className="absolute top-0 left-0 right-0 flex justify-center pt-2">
                <div className="w-12 h-1.5 bg-gray-300 rounded-full"></div>
              </div>
              <div className="flex items-center justify-between mt-4 mb-5">
                <h2 className="text-2xl font-medium">Add Health System</h2>
                <button
                  onClick={() => setShowMobilePopup(false)}
                  className="p-2 hover:bg-gray-100 rounded-full"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div className="overflow-y-auto flex-1">
                <HealthSystemAvailableList
                  searchQuery={searchQuery}
                  setSearchQuery={setSearchQuery}
                />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default HealthSystemSettings;
