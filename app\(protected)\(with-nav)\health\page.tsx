"use client";

import Chat from "@/components/Chat/Chat";
import ChatHeader from "@/components/Chat/ChatHeader";
import Image from "next/image";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import MedicalRecordPanel from "@/components/Health/MedicalRecordPanel";
import { useWindowSize } from "@/hooks/useWindowSize";
import MedicalRecordModal from "@/components/Health/MedicalRecordModal";
import { OneUpHealthService } from "@/lib/services/oneUpHealthService";
import { useUser } from "@/hooks/useUsers";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";

// Add this interface to match the expected response type
interface OneUpHealthTokenResponse {
  data?: {
    data?: {
      access_token: string;
      refresh_token?: string;
      expires_in?: number;
    };
  };
}

export default function HealthPage() {
  const [isMobile, setIsMobile] = useState(false);
  const [showMedicalRecordPanel, setShowMedicalRecordPanel] = useState(false);
  const [showMedicalModal, setShowMedicalModal] = useState(false);
  const [medicalRecords, setMedicalRecords] = useState([]);
  const [connectedSystems, setConnectedSystems] = useState([]);
  const [userInfo, setUserInfo] = useState({
    birthYear: "-",
    gender: "-",
    region: "-",
    race: "-",
    ethnicity: "-",
  });
  const [oneUpService, setOneUpService] = useState<OneUpHealthService | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();
  const isDesktop = useWindowSize();
  const { currentUser } = useUser();

  // Check if we should show the medical modal (coming from success page)
  useEffect(() => {
    if (!isDesktop) return;

    const shouldOpenModal = localStorage.getItem("openHealthModal") === "true";

    if (shouldOpenModal) {
      // Remove flag to avoid reopening
      localStorage.removeItem("openHealthModal");

      // Check if we have richer data
      const modalDataStr = localStorage.getItem("healthModalData");
      if (modalDataStr) {
        try {
          const modalData = JSON.parse(modalDataStr);
          // Only use data if it's fresh (less than 2 minutes old)
          if (
            modalData.timestamp &&
            Date.now() - modalData.timestamp < 120000
          ) {
            setUserInfo(modalData.userInfo || userInfo);
            setMedicalRecords(modalData.records || []);
            setConnectedSystems(modalData.connectedSystems || []);
          }
          localStorage.removeItem("healthModalData");
        } catch (e) {
          console.error("Failed to parse modal data", e);
        }
      }

      // Initialize OneUpHealth service
      if (currentUser?.userId) {
        const service = new OneUpHealthService({ userId: currentUser.userId });
        const storedToken = localStorage.getItem(
          `oneup_access_token_${currentUser.userId}`
        );
        if (storedToken) {
          service.setAccessToken(storedToken);
          setOneUpService(service);
        }
      }

      // Open modal after a short delay
      setTimeout(() => {
        setShowMedicalModal(true);
      }, 300);
    }
  }, [isDesktop, currentUser]);

  // Function to check token validity
  const isTokenValid = (): boolean => {
    if (!currentUser) return false;

    const expirationTimeStr = localStorage.getItem(
      `oneup_token_expiration_${currentUser.userId}`
    );
    if (!expirationTimeStr) return false;

    const expirationTime = new Date(expirationTimeStr).getTime();
    return Date.now() < expirationTime - 30000;
  };

  // Function to refresh token
  const refreshToken = async (
    service: OneUpHealthService
  ): Promise<boolean> => {
    if (!currentUser) return false;

    try {
      const refreshToken = localStorage.getItem(
        `oneup_refresh_token_${currentUser.userId}`
      );

      if (!refreshToken) return false;

      const client = generateClient<Schema>();
      // Add the type assertion to fix the error
      const refreshResult = (await client.mutations.refreshOneUpHealthToken({
        refreshToken: refreshToken,
      })) as unknown as OneUpHealthTokenResponse;

      if (!refreshResult?.data?.data?.access_token) return false;

      service.setAccessToken(refreshResult.data.data.access_token);
      return true;
    } catch (error) {
      console.error("Failed to refresh token:", error);
      return false;
    }
  };

  const AddMedicalDataButton = () => (
    <Button
      variant="outline"
      size="sm"
      className="flex items-center gap-1 rounded-full border border-gray-200 h-9"
      onClick={() => toast.info("Add Medical Data feature coming soon!")}
    >
      <Image
        src="/assets/report.svg"
        alt="Add Medical Data"
        width={16}
        height={16}
      />
      <span className="text-sm font-medium">Add Medical Data</span>
    </Button>
  );

  const PlotTrendButton = () => (
    <Button
      variant="outline"
      size="sm"
      className="flex items-center gap-1 rounded-full border border-gray-200 h-9"
      onClick={() => toast.info("Plot Trend feature coming soon!")}
    >
      <Image
        src="/assets/trend-up.svg"
        alt="Plot Trend"
        width={16}
        height={16}
      />
      <span className="text-sm font-medium">Plot Trend</span>
    </Button>
  );

  // const HealthReportButton = () => (
  //   <Button
  //     variant="outline"
  //     size="sm"
  //     className="flex items-center gap-1 rounded-full border border-gray-200"
  //     onClick={() => toast.info("Health report feature coming soon!")}
  //   >
  //     <Image
  //       src="/assets/report-icon.svg"
  //       alt="Report"
  //       width={16}
  //       height={16}
  //     />
  //     <span className="text-sm font-medium">Health Report</span>
  //   </Button>
  // );

  // Group the buttons together
  const ButtonGroup = () => (
    <div className="flex gap-2 flex-wrap ">
      <AddMedicalDataButton />
      <PlotTrendButton />
      {/* <HealthReportButton /> */}
    </div>
  );

  const healthChatData = {
    id: "health-chat",
    name: "AI Nurse",
    type: "AI",
    members: [],
    isGroup: false,
  };

  const handleBack = () => {
    router.back();
  };

  const handlePanelToggle = (isOpen: boolean) => {
    setShowMedicalRecordPanel(isOpen);
  };

  return (
    <div className="md:relative h-full flex flex-row overflow-hidden">
      <div
        className={`flex-1 flex flex-col overflow-hidden transition-all duration-300`}
      >
        <ChatHeader
          chatData={healthChatData}
          onBackClick={handleBack}
          icon="/chat/health-logo.svg"
          onPanelToggle={handlePanelToggle}
          isPanelOpen={showMedicalRecordPanel} // Pass panel state to header
        />

        <div className="flex-1 overflow-hidden sm-max:m-2  bg-white md:rounded-b-3xl  max-sm:rounded-t-3xl">
          <Chat
            questionnaireId="2"
            headerAction={<ButtonGroup />}
            showSidebar={false}
            isHealthTab={true}
          />
        </div>
      </div>
    </div>
  );
}
