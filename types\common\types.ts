// Common types used across the application
export type FriendRequestStatusType = "PENDING" | "ACCEPTED" | "DECLINED";
export type FriendRequestDirectionType = "sent" | "received";

export interface FriendRequestStatus {
  type: FriendRequestDirectionType;
  status: FriendRequestStatusType;
  requestId?: string;
}

export interface BaseUserType {
  userId: string;
  name?: string | null;
  email?: string | null;
  profilePicture?: string | null;
  friendRequestStatus?: FriendRequestStatus | null;
}

export interface FriendRequest {
  id: string;
  senderId: string;
  receiverId: string;
  status: FriendRequestStatusType;
  createdAt: string;
  updatedAt: string;
  direction?: FriendRequestDirectionType;
  user?: BaseUserType;
}
