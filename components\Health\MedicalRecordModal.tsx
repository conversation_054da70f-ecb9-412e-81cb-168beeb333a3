import { useState, useEffect } from "react";
import Image from "next/image";
import MedicalRecordContent from "./MedicalRecordContent";
import HealthSystemSettings from "./HealthSystemSettings";
import {
  type PatientData,
  type MedicalRecord,
  type ConnectedSystem,
  OneUpHealthService,
} from "@/lib/services/oneUpHealthService";
import { useUser } from "@/hooks/useUsers";
import { toast } from "sonner";

interface MedicalRecordModalProps {
  onClose: () => void;
  userInfo?: {
    name: string;
    birthDate: string;
    birthYear: string;
    gender: string;
    region: string;
    race: string;
    ethnicity: string;
    maritalStatus: string;
    email: string;
    mobilePhone: string;
    workPhone: string;
    address: string;
    primaryLanguage: string;
    secondaryLanguage: string;
  };
  records: MedicalRecord[];
  connectedSystems: ConnectedSystem[];
  isLoading: boolean;
  oneUpService?: OneUpHealthService | null;
  userId?: string;
  isTokenValid?: () => boolean;
  refreshToken?: (service: OneUpHealthService) => Promise<boolean>;
  onPatientDataChange?: (patientData: PatientData[]) => void;
  selectedRecord?: MedicalRecord | null;
}

const defaultUserInfo = {
  name: "-",
  birthDate: "-",
  birthYear: "-",
  gender: "-",
  region: "-",
  race: "-",
  ethnicity: "-",
  maritalStatus: "-",
  email: "-",
  mobilePhone: "-",
  workPhone: "-",
  address: "-",
  primaryLanguage: "-",
  secondaryLanguage: "-",
};

const MedicalRecordModal = ({
  onClose,
  userInfo = defaultUserInfo,
  records: initialRecords = [],
  connectedSystems: initialConnectedSystems = [],
  isLoading: initialIsLoading = false,
  oneUpService = null,
  userId,
  isTokenValid,
  refreshToken,
  onPatientDataChange,
  selectedRecord: activeRecord = null,
}: MedicalRecordModalProps) => {
  const { currentUser } = useUser();
  const [activeTab, setActiveTab] = useState("All");
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(
    activeRecord || null
  );
  const [modalLoading, setModalLoading] = useState(false);
  const [localRecords, setLocalRecords] =
    useState<MedicalRecord[]>(initialRecords);
  const [filteredRecords, setFilteredRecords] =
    useState<MedicalRecord[]>(initialRecords);
  const [localConnectedSystems, setLocalConnectedSystems] = useState<
    ConnectedSystem[]
  >(initialConnectedSystems);
  const [localOneUpService, setLocalOneUpService] =
    useState<OneUpHealthService | null>(null);
  const [localUserInfo, setLocalUserInfo] = useState(userInfo);

  const [showSettings, setShowSettings] = useState(false);

  // Check if user is connected - same logic as page
  const isHealthConnected = currentUser?.is_1healthup_connected === true;
  const hasRecords = localRecords.length > 0;
  // Показуємо записи якщо є хоча б один запис, навіть якщо не підключено систему
  const shouldShowRecords = hasRecords || isHealthConnected;

  // Create OneUpHealthService instance if user is connected - same as page
  useEffect(() => {
    if (!currentUser) return;

    const config = { userId: currentUser.userId };
    const service = new OneUpHealthService(config);
    setLocalOneUpService(service);

    // Load patient data if user is connected
    if (isHealthConnected) {
      loadPatientData(service);
    }
  }, [currentUser, isHealthConnected]);

  // Load patient data when service is ready - same as page
  const loadPatientData = async (service: OneUpHealthService) => {
    try {
      setModalLoading(true);
      console.log("Loading patient data in modal...");

      const patient = await service.fetchPatientDataFromDatabase();
      const records = await service.fetchMedicalRecordsFromDatabase();

      if (patient) {
        setLocalUserInfo({
          name: patient.name || "-",
          birthDate: patient.birthDate || "-",
          birthYear: patient.birthYear || "-",
          gender: patient.gender || "-",
          region: patient.region || "-",
          race: patient.race || "-",
          ethnicity: patient.ethnicity || "-",
          maritalStatus: (patient as any).maritalStatus || "-",
          email: patient.email || "-",
          mobilePhone: (patient as any).mobilePhone || "-",
          workPhone: (patient as any).workPhone || "-",
          address: patient.address || "-",
          primaryLanguage: (patient as any).primaryLanguage || "-",
          secondaryLanguage: (patient as any).secondaryLanguage || "-",
        });
      }

      console.log("Fetched records in modal:", records.length);
      setLocalRecords(records);

      const systems = await service.getConnectedHealthSystems();
      if (systems.length > 0) {
        setLocalConnectedSystems(systems);
      }
    } catch (error) {
      console.error("Failed to load patient data in modal:", error);
    } finally {
      setModalLoading(false);
    }
  };

  // Update local records when initialRecords changes
  useEffect(() => {
    if (initialRecords.length > 0) {
      setLocalRecords(initialRecords);
      setFilteredRecords(initialRecords); // Initialize filtered records
      console.log(
        "Updated localRecords from initialRecords:",
        initialRecords.length
      );
    }
  }, [initialRecords]);

  // Якщо localRecords змінився, оновлюємо filteredRecords для вкладки All
  useEffect(() => {
    if (activeTab === "All") {
      setFilteredRecords(localRecords);
    } else {
      setFilteredRecords(filterRecordsByDate(localRecords, activeTab));
    }
  }, [localRecords, activeTab]);

  // Enhanced debug to check both user and records
  useEffect(() => {
    console.log("Modal currentUser:", currentUser);
    console.log("Modal isHealthConnected:", isHealthConnected);
    console.log("Modal shouldShowRecords:", shouldShowRecords);
    console.log("Initial records length:", initialRecords.length);
    console.log("Local records length:", localRecords.length);
  }, [
    currentUser,
    isHealthConnected,
    shouldShowRecords,
    initialRecords,
    localRecords,
  ]);

  const filterRecordsByDate = (records: MedicalRecord[], filterType: string) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentYear = new Date(now.getFullYear(), 0, 1);

    return records.filter((record) => {
      if (!record.date) return false;

      const recordDate = new Date(record.date);

      switch (filterType) {
        case "Day":
          return recordDate >= today;
        case "Month":
          return recordDate >= currentMonth;
        case "Year":
          return recordDate >= currentYear;
        default:
          return true; // "All" case
      }
    });
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setModalLoading(true);

    try {
      if (tab === "All") {
        setFilteredRecords(localRecords);
      } else {
        const filtered = filterRecordsByDate(localRecords, tab);
        setFilteredRecords(filtered);
        console.log(`Filtered ${filtered.length} records for ${tab}`);
      }
    } catch (error) {
      console.error("Error filtering records:", error);
      // In case of error, show all records
      setFilteredRecords(localRecords);
    } finally {
      setModalLoading(false);
    }
  };

  const handleRecordClick = (record: MedicalRecord | null) => {
    setSelectedRecord(record);
  };

  // Handle modal close safely, adding a warning if we're in the middle of connecting
  const handleModalClose = () => {
    if (!shouldShowRecords && modalLoading) {
      if (
        window.confirm(
          "Are you sure you want to cancel the connection process?"
        )
      ) {
        toast.info("Connection process cancelled");
        onClose();
      }
    } else {
      onClose();
    }
  };

  const handleDisconnectSystem = (systemId: string) => {
    toast.success("Health system disconnected");

    setLocalConnectedSystems((prev) =>
      prev.filter((system) => system.id !== systemId)
    );
  };

  const handleConnectProvider = (providerId: string) => {
    toast.info(`Initiating connection to health system...`);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-end items-stretch z-50 py-6 pr-6">
      {showSettings ? (
        <HealthSystemSettings
          onClose={() => setShowSettings(false)}
         
        />
      ) : (
        <div className="bg-white w-[940px] h-full rounded-2xl overflow-hidden flex flex-col">
          <div className="flex justify-between h-[56px] items-center p-5 border-b">
            <h2 className="text-xl font-semibold">Medical Record</h2>
            <div className="flex items-center space-x-5">
              <button className="p-1" onClick={() => setShowSettings(true)}>
                <Image
                  src="/setting.svg"
                  alt="Settings"
                  width={24}
                  height={24}
                />
              </button>
              <button onClick={handleModalClose} className="p-1">
                <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
              </button>
            </div>
          </div>

          <div className="flex-1 overflow-hidden">
            {shouldShowRecords ? (
              <MedicalRecordContent
                isDataConnected={isHealthConnected}
                isLoading={initialIsLoading || modalLoading}
                userInfo={localUserInfo}
                activeTab={activeTab}
                displayRecords={filteredRecords}
                connectedSystems={localConnectedSystems}
                onTabChange={handleTabChange}
                onRecordClick={handleRecordClick}
                selectedRecord={selectedRecord}
                isModal={true}
              />
            ) : (
              <div className="h-full px-6 bg-white overflow-auto">
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-gray-500 mb-6">There's no data found</p>
                  <button
                    onClick={() => setShowSettings(true)}
                    className="bg-blue-500 text-white font-medium py-3 px-6 rounded-2xl w-full max-w-md"
                  >
                    Connect Medical Data
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MedicalRecordModal;
