import { useState, useCallback } from "react";
import { generateClient } from "aws-amplify/api";
import { fetchAuthSession } from "@aws-amplify/auth";
import { Schema } from "@/amplify/data/resource";
import {
  ERROR_USER_NOT_AUTHENTICATED,
  ERROR_MISSING_CHAT_ID,
  ERROR_FAILED_SEND_MESSAGE,
} from "@/constants/chat";
import { MessageWithoutChat } from "@/types/chat";

const client = generateClient<Schema>();

type UserInfo = {
  email?: string | null;
  name?: string | null;
  profilePicture?: string | null;
  userId?: string | null;
};

export function useMessageSending(
  userId: string | null,
  chatId: string | null,
  sessionId: string | null,
  addMessage: (message: MessageWithoutChat) => void,
  resetStreaming: () => void,
  prepareForResponse: () => void,
  removeMessage: (messageId: string) => void
) {
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const sendMessage = useCallback(
    async (message: string) => {
      if (!userId) {
        setError(ERROR_USER_NOT_AUTHENTICATED);
        return;
      }
      if (!chatId) {
        setError(ERROR_MISSING_CHAT_ID);
        return;
      }
      if (!sessionId) {
        setError("No active session");
        return;
      }
      if (!message.trim()) return;

      let tempMessageId = "";

      try {
        setIsSending(true);
        setError(null);
        resetStreaming();

        const currentTime = new Date().toISOString();
        tempMessageId = `temp-${Date.now()}`;

        let userInfo: UserInfo = {};
        try {
          const session = await fetchAuthSession();
          if (session.tokens?.idToken?.payload) {
            const payload = session.tokens.idToken.payload;
            userInfo = {
              email: String(payload.email || ""),
              name: String(payload.name || payload.email || ""),
              userId: String(payload.sub || userId),
            };
          }
        } catch (authErr) {
          console.warn("Could not fetch user data from auth session:", authErr);
        }

        const userMessage: MessageWithoutChat = {
          id: tempMessageId,
          chatId,
          userId,
          message,
          createdAt: currentTime,
          updatedAt: currentTime,
          user: userInfo,
        };

        addMessage(userMessage);

        prepareForResponse();

        try {
          const response = await client.mutations.streamMessage({
            chatId,
            message,
            eventConfig: {
              channelName: `chat-${chatId}`,
              sessionId,
              namespace: "default",
            },
          });
          if (!response.data) {
            throw new Error("No response data received from steam");
          }

          const responseWithUser = {
            ...response.data,
            user: response.data.user || userInfo,
          };

          addMessage(responseWithUser);
        } catch (streamErr: any) {
          console.warn("streamMessage failed:", streamErr);

          if (tempMessageId) {
            removeMessage(tempMessageId);
          }

          if (
            !navigator.onLine ||
            streamErr.message?.includes("network") ||
            streamErr.name === "NetworkError" ||
            streamErr.message?.includes("connection")
          ) {
            setError("Please check your internet connection and try again.");
          } else {
            setError(ERROR_FAILED_SEND_MESSAGE);
          }
          resetStreaming();
        }
      } catch (err) {
        console.error("Error sending message:", err);

        if (tempMessageId) {
          removeMessage(tempMessageId);
        }

        if (
          !navigator.onLine ||
          (err instanceof Error &&
            (err.message?.includes("network") ||
              err.name === "NetworkError" ||
              err.message?.includes("connection")))
        ) {
          setError("Please check your internet connection and try again.");
        } else {
          setError(ERROR_FAILED_SEND_MESSAGE);
        }
        resetStreaming();
      } finally {
        setIsSending(false);
      }
    },
    [
      userId,
      chatId,
      sessionId,
      addMessage,
      resetStreaming,
      prepareForResponse,
      removeMessage,
    ]
  );

  return {
    sendMessage,
    isSending,
    error,
  };
}
