import { EventManager } from "../services/EventManager";
import { Context } from "./QuestionnaireContext";

/**
 * Interface for questionnaire strategies
 * Defines the contract for different questionnaire implementations
 */
export interface QuestionnaireStrategy {
  /**
   * Initializes the questionnaire strategy
   * @param context The questionnaire context containing metadata and state
   */
  initialize(context: Context): Promise<void>;

  /**
   * Handles a user response to a questionnaire question
   * @param userResponse The user's response to the current question
   * @param context The questionnaire context
   * @param eventManager Event manager for handling streaming events
   */
  handleResponse(
    userResponse: string,
    context: Context,
    eventManager: EventManager
  ): Promise<{
    nextQuestion: string | null;
    isComplete: boolean;
    messageId?: string;
    chatMessage?: any;
  }>;

  /**
   * Checks if early completion is possible
   * @param userResponse Current user response
   * @param context The questionnaire context
   */
  checkEarlyCompletion?(
    userResponse: string,
    context: Context
  ): Promise<boolean>;

  /**
   * Handles the completion of the questionnaire
   * @param chatId The chat ID
   * @param context The questionnaire context
   * @param prompt The prompt for the completion
   * @param eventManager Event manager for handling streaming events
   */
  onQuestionnaireComplete(
    chatId: string,
    context: Context,
    prompt: string,
    eventManager: EventManager
  ): Promise<{
    enhancedPrompt: string;
    showSpecialUI?: boolean;
  }>;
}
