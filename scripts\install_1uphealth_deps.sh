#!/bin/bash

# Script to install dependencies for 1upHealth Lambda functions

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Installing dependencies for 1upHealth Lambda functions...${NC}"

# Install dependencies for oneup-health-connect function
echo -e "\n${YELLOW}Installing dependencies for oneup-health-connect...${NC}"
cd amplify/functions/oneup-health-connect
npm install
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Dependencies installed successfully for oneup-health-connect${NC}"
else
    echo -e "${RED}✗ Error installing dependencies for oneup-health-connect${NC}"
    exit 1
fi

# Install dependencies for oneup-health-data function
echo -e "\n${YELLOW}Installing dependencies for oneup-health-data...${NC}"
cd ../oneup-health-data
npm install
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Dependencies installed successfully for oneup-health-data${NC}"
else
    echo -e "${RED}✗ Error installing dependencies for oneup-health-data${NC}"
    exit 1
fi

# Go back to project root
cd ../../..

echo -e "\n${GREEN}All dependencies installed successfully!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo -e "1. Set up your 1upHealth API credentials:"
echo -e "   - ${YELLOW}npx ampx secrets set ONEUP_CLIENT_ID${NC}"
echo -e "   - ${YELLOW}npx ampx secrets set ONEUP_CLIENT_SECRET${NC}"
echo -e "2. Deploy the Amplify backend:"
echo -e "   - ${YELLOW}npx ampx deploy${NC}"
echo -e "3. Update OneUpHealthConnect component with your client credentials."
echo -e "\n${GREEN}Done!${NC}"
