import { useMemo, useEffect, useCallback, useState, useRef } from "react";
import { useMessageLoader } from "./useMessageLoader";
import { useMessageStreaming } from "./useMessageStreaming";
import { useMessageSending } from "./useMessageSending";
import { AI_ASSISTANT_ID } from "@/constants/chat";
import { MessageWithoutChat } from "@/types/chat";

export function useMessages(
  userId: string | null,
  chatId: string | null,
  sessionId: string | null,
  options?: { setNewMessageAdded?: (value: boolean) => void }
) {
  const [localMessages, setLocalMessages] = useState<MessageWithoutChat[]>([]);
  const previousMessagesLengthRef = useRef(0);

  const setNewMessageAdded = useMemo(
    () => options?.setNewMessageAdded || ((value: boolean) => {}),
    [options?.setNewMessageAdded]
  );

  const onFinalMessageAddedRef = useRef<(() => void) | null>(null);

  const {
    messages: fetchedMessages,
    isLoading,
    error: loadError,
    addMessage: addMessageToLoader,
    removeMessage: removeMessageFromLoader,
    setObserverTarget,
    hasMore,
    isLoadingMore,
  } = useMessageLoader(chatId);

  const observerSetRef = useRef(false);

  const handleSetObserverTarget = useCallback(
    (element: HTMLElement | null) => {
      if (element) {
        setObserverTarget(element);
        observerSetRef.current = true;
      }
    },
    [setObserverTarget]
  );

  useEffect(() => {
    observerSetRef.current = false;
  }, [chatId]);

  useEffect(() => {
    if (fetchedMessages.length > 0) {
      const hasNewMessages =
        fetchedMessages.length > previousMessagesLengthRef.current;

      if (hasNewMessages) {
        // Always trigger scroll on new messages
        setNewMessageAdded(true);
      }

      setLocalMessages(fetchedMessages);
      previousMessagesLengthRef.current = fetchedMessages.length;
    } else if (chatId) {
      setLocalMessages([]);
      previousMessagesLengthRef.current = 0;
    }
  }, [fetchedMessages, chatId, setNewMessageAdded]);

  const addMessage = useCallback(
    (message: MessageWithoutChat) => {
      addMessageToLoader(message);

      if (
        message.userId === AI_ASSISTANT_ID &&
        onFinalMessageAddedRef.current
      ) {
        onFinalMessageAddedRef.current();
      }

      // Always set newMessageAdded to true when we add a message
      // This will force scroll to bottom regardless of user's position
      setNewMessageAdded(true);

      setLocalMessages((prev) => {
        const filteredMessages =
          message.userId === AI_ASSISTANT_ID
            ? prev.filter((m) => !(m.isGhost && m.userId === AI_ASSISTANT_ID))
            : prev;

        const messageIndex = filteredMessages.findIndex(
          (m) => m.id === message.id
        );

        if (messageIndex >= 0) {
          return filteredMessages.map((m) =>
            m.id === message.id ? message : m
          );
        } else {
          return [...filteredMessages, message];
        }
      });
    },
    [addMessageToLoader, setNewMessageAdded]
  );

  const removeMessage = useCallback(
    (messageId: string) => {
      setLocalMessages((prev) =>
        prev.filter((message) => message.id !== messageId)
      );

      if (removeMessageFromLoader) {
        removeMessageFromLoader(messageId);
      }
    },
    [removeMessageFromLoader]
  );

  const updateMessages = useCallback(
    (newMessage: MessageWithoutChat) => {
      addMessageToLoader(newMessage);

      // Always force scroll for new messages
      setNewMessageAdded(true);

      setLocalMessages((prevMessages) => {
        const baseMessages =
          newMessage.userId === AI_ASSISTANT_ID
            ? prevMessages.filter(
                (m) => !(m.isGhost && m.userId === AI_ASSISTANT_ID)
              )
            : prevMessages;

        const existingIndex = baseMessages.findIndex(
          (m) => m.id === newMessage.id
        );
        if (existingIndex >= 0) {
          return baseMessages.map((m) =>
            m.id === newMessage.id ? newMessage : m
          );
        } else {
          return [...baseMessages, newMessage];
        }
      });
    },
    [addMessageToLoader, setNewMessageAdded]
  );

  const {
    ghostMessage,
    streamingMessageId,
    isStreaming,
    isTransitioning,
    streamingError,
    resetStreaming,
    prepareForResponse,
    checkForNewAIMessages,
    onFinalMessageAdded,
  } = useMessageStreaming(chatId, sessionId, addMessage);

  useEffect(() => {
    onFinalMessageAddedRef.current = onFinalMessageAdded;
  }, [onFinalMessageAdded]);

  const {
    sendMessage: originalSendMessage,
    isSending,
    error: sendError,
  } = useMessageSending(
    userId,
    chatId,
    sessionId,
    addMessage,
    resetStreaming,
    prepareForResponse,
    removeMessage
  );

  const sendMessage = useCallback(
    async (message: string) => {
      setNewMessageAdded(true);

      return await originalSendMessage(message);
    },
    [originalSendMessage, setNewMessageAdded]
  );

  useEffect(() => {
    if (localMessages.length > 0) {
      // Using 'as any' to bypass TypeScript type checking
      checkForNewAIMessages(localMessages as any);
    }
  }, [localMessages, checkForNewAIMessages]);

  const displayMessages = useMemo(() => {
    if (ghostMessage) {
      const noGhosts = isTransitioning
        ? localMessages
        : localMessages.filter((m) => !m.isGhost);
      return [...noGhosts, ghostMessage];
    }
    return localMessages;
  }, [localMessages, ghostMessage, isTransitioning]);

  const error = loadError || sendError || streamingError;

  return {
    messages: displayMessages,
    isLoading,
    isSending,
    error,
    sendMessage,
    isStreaming,
    streamingMessageId,
    updateMessages,
    addMessage,
    removeMessage,

    hasMore,
    isLoadingMore,
    setObserverTarget: handleSetObserverTarget,
  };
}
