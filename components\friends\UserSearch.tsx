import { Input } from "@/components/ui/input";
import Image from "next/image";

type UserSearchProps = {
  searchValue: string;
  setSearchValue: (value: string) => void;
  onSearch: () => void;
};

export default function UserSearch({ searchValue, setSearchValue, onSearch }: UserSearchProps) {
  return (
    <>
      {/* Search input */}
      <div className="px-[14px] mt-5">
        <div className="relative">
          <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
            <Image src="/chats/search.svg" alt="Search" width={20} height={20} />
          </div>
          <Input 
            className="pl-10 py-6  rounded-full bg-[#F5F5F5] h-[56px]"
            placeholder="Search user Id"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
        </div>
      </div>
      
      {/* Search suggestion when text is entered */}
      {searchValue && (
        <div className="px-5 mt-4 bg-white py-2">
          <button 
            className="flex items-center w-full"
            onClick={onSearch}
          >
            <div className="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <Image src="/whitesearch.svg" alt="Search" width={17} height={17} />
            </div>
            <span className="text-left text-base">Search {searchValue}</span>
          </button>
        </div>
      )}
    </>
  );
}
