"use client"

import { useState } from "react"
import useLogin from "@/hooks/useLogin"
import { useWindowSize } from "@/hooks/useWindowSize"
import type { FormData } from "@/types/auth"
import { useForm } from "react-hook-form"
import { useRouter } from "next/navigation"
import { Amplify } from "aws-amplify"
import outputs from "@/amplify_outputs.json"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Eye, EyeOff, X } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import CustomButton from "@/components/ui/CustomButton"
import RightSide from "@/components/auth/RightSide"

Amplify.configure(outputs)

export default function Login() {
  const isDesktop = useWindowSize()
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>()
  const { loading, errorMessage, isLoading, userEmail, handleGoogleLogin, handleFacebookLogin, handleLogin } =
    useLogin()
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-secondary">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  

  return (
    <main className=" flex flex-col h-full">
      {isDesktop ? (
        // Desktop Layout
        <div className="flex h-full">
          {/* Left side */}
          <div className="hidden md:flex w-full md:w-1/2 flex-col justify-start h-full px-8 lg:px-16 xl:px-28 2xl:px-[140px] py-8 lg:pt-32 lg:pb-[224px] bg-white overflow-hidden rounded-3xl h-responsive-padding">
          <div className="flex">
          <p className="text-[#FFB300] text-lg font-medium mb-2">jin</p>
          <p className="text-[#4187FF] text-lg font-medium mb-2">I</p>
          <p className="text-[#D93C85] text-lg font-medium mb-2">X</p>
          </div>
        

            <h1 className="text-4xl font-normal text-gray-900 mb-4">Health journal</h1>
            <h1 className="text-4xl font-normal text-gray-900 mb-6">from jinx to jinIX</h1>

            <form onSubmit={handleSubmit(handleLogin)} className="space-y-4">
              <div>
              <label htmlFor="email" className="block font-semibold text-[16px] text-[#000000] mb-2">
                  Email
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Email"
                  
                  {...register("email")}
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
              </div>

              <div>
              <label htmlFor="password" className="block font-semibold text-[16px] text-[#000000] mb-2">
                  Password
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Password"
                    
                    {...register("password")}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
                {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>}
              </div>

              {errorMessage && <p className="text-red-500 text-sm mb-4">{errorMessage}</p>}

              <div className="text-right">
                <button
                  type="button"
                  onClick={() => router.push("/forgot-password")}
                  className="text-blue-500 text-sm hover:underline"
                  disabled={loading}
                >
                  Forgot Password?
                </button>
              </div>

              <CustomButton
              type="submit"
              loading={loading}
            >
              Sign In
            </CustomButton>
            </form>

            <style jsx global>{`
              @media (max-height: 900px) {
                .h-responsive-padding {
                  padding-top: 1rem !important;
                  padding-bottom: 2rem !important;
                }
                .lg\\:pt-32 {
                  padding-top: 1rem !important;
                }
                .lg\\:pb-\\[224px\\] {
                  padding-bottom: 2rem !important;
                }
              }
              @media (max-height: 800px) {
                .h-responsive-padding {
                  overflow: auto !important;
                }
              }
            `}</style>

            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gray-300"></div>
              <p className="mx-4 text-gray-600 text-sm">Or</p>
              <div className="flex-1 h-px bg-gray-300"></div>
            </div>

            <div className="flex flex-col gap-4">
              <Button
                variant="outline"
                onClick={handleFacebookLogin}
                className="flex items-center justify-between gap-2 rounded-2xl border border-gray-300 py-2 px-4 pl-3"
                disabled={loading}
              >
                <Image src="/facebookk.svg" width={16} height={16} alt="Facebook" className="w-[28px] h-[28px]"/>
                <span className="flex-1 text-center">Continue with Facebook</span>
              </Button>
              <Button
                variant="outline"
                onClick={handleGoogleLogin}
                className="flex items-center justify-between gap-2 rounded-2xl border border-gray-300 py-2 px-4"
                disabled={loading}
              >
                <Image src="/Group.svg" width={16} height={16} alt="Google" className="w-5 h-5"/>
                <span className="flex-1 text-center">Continue with Google</span>
              </Button>
              <p className="text-center text-sm text-gray-600 mt-4">
              Don’t have an account?{" "}
              <Link href="/register" className="text-blue-500 hover:underline">
                Sign Up
              </Link>
            </p>
            </div>
          </div>

          {/* Right side */}
          <RightSide />
        </div>
      ) : (
        // Mobile Layout
        <div className="md:hidden flex flex-col items-center justify-start min-h-screen h-screen max-h-screen bg-gradient-to-b p-5 overflow-hidden bg-white">
          <div className="absolute top-5 left-5 mb-8">
                  <X className="w-6 h-6 cursor-pointer " onClick={() => router.push("/")} />
                </div>
          <div className="relative w-full max-w-md mb-8">
            {/* <Image src="/6.png" alt="Abstract shape" width={400} height={400} className="w-full h-auto max-h-[200px] object-contain" /> */}
            <div className="absolute inset-0 flex items-center justify-center">
              
            </div>
          </div>

          <div className="w-full max-w-md text-start mb-4">
            {/* <div className="inline-block bg-white rounded-full px-6 py-2 mb-2">
              <p className="text-[#0051FF] font-medium">Sign In</p>
            </div> */}
            {/* <h1 className="text-4xl font-bold text-[#0A1E42] mb-8">
              Welcome <span className="text-[#0051FF]">Back</span>
            </h1> */}
          <p className="text-[#0F172A] text-[30px] font-[400]">Sign In</p>
          <p className="text-[14px] font-[500] text-[#737373]">Vorem ipsum dolor sit amet, consectetur adipiscing elit.Vorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
          </div>

          <div className="flex flex-col items-center space-y-4 mt-4 w-full max-w-md">
            <form onSubmit={handleSubmit(handleLogin)} className="space-y-2 w-full">
            <div>
  <Input
    id="email"
    type="email"
    placeholder="Email"
    className={`w-full h-[43px] mb-3 py-3 pl-4 rounded-xl bg-[#F5F5F54D] border ${
      errors.email ? "border-[#DC2625]" : "border-gray-300"
    } focus:outline-none focus:border-blue-500 text-gray-900`}
    {...register("email", { required: "Email is required" })}
  />
  {errors.email && <p className="text-[#DC2625] text-sm mt-1">{errors.email.message}</p>}
</div>

<div>
  <div className="relative">
    <Input
      id="password"
      type={showPassword ? "text" : "password"}
      placeholder="Password"
      className={`w-full h-[43px] mb-3 py-3 pl-4 rounded-xl bg-[#F5F5F54D] border ${
        errors.password ? "border-[#DC2625]" : "border-gray-300"
      } focus:outline-none focus:border-blue-500 text-gray-900`}
      {...register("password", { required: "Password is required" })}
    />
    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
      <button
        type="button"
        onClick={() => setShowPassword(!showPassword)}
        className="text-gray-400 hover:text-gray-600"
      >
        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
      </button>
    </div>
  </div>
  {errors.password && <p className="text-[#DC2625] text-sm mt-1">{errors.password.message}</p>}
</div>

              {errorMessage && <p className="text-[#DC2625] text-sm mb-4">{errorMessage}</p>}

              <div className="text-right">
                <button
                  type="button"
                  onClick={() => router.push("/forgot-password")}
                  className="text-blue-500 text-sm hover:underline"
                  disabled={loading}
                >
                  Forgot Password?
                </button>
              </div>

              <CustomButton
              type="submit"
              loading={loading}
            >
              Sign In
            </CustomButton>
            </form>

           

            <div className="flex items-center w-full">
            <div className="flex-1 h-px bg-gray-300"></div>
            <p className="mx-[2px] my-3 text-gray-600 text-sm whitespace-nowrap">Or</p>
            <div className="flex-1 h-px bg-gray-300"></div>
          </div>

            <Button
              variant="outline"
              onClick={handleFacebookLogin}
              className="w-full h-[52px] flex items-center justify-start gap-2 rounded-xl border border-gray-300 bg-white text-gray-900 pl-3 hover:bg-gray-100"
              disabled={loading}
            >
              <Image  src="/facebookk.svg" width={16} height={16} alt="Facebook" className="w-[28px] h-[28px]"/>
              <span className="flex-grow text-center">Continue with Facebook</span>
            </Button>

            <Button
  variant="outline"
  onClick={handleGoogleLogin}
  className="w-full h-[52px] flex items-center justify-start gap-2 rounded-xl border border-gray-300 bg-white text-gray-900 hover:bg-gray-100"
  disabled={loading}
>
  <Image src="/Group.svg" width={16} height={16} alt="Google" className="w-5 h-5"/>
  <span className="flex-grow text-center">Continue with Google</span>
</Button>
            <div className="w-full max-w-md fixed bottom-0 text-center py-5  ">
            <p className="text-center text-sm text-gray-600 mt-8">
              Don’t have an account?{" "}
              <Link href="/register" className="text-[#0051FF] hover:underline">
                Create an account
              </Link>
            </p>
            </div>
          </div>
        </div>
      )}
    </main>
  )
}