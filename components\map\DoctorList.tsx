"use client";

import Image from "next/image";
import * as React from "react";
import { ArrowLeft, Search, Star } from "lucide-react";
import { useSearchParams, useRouter } from "next/navigation";
import { useWindowSize } from "@/hooks/useWindowSize";

export default function DoctorList({
  filteredEntities,
  lat,
  long,
  calculateDistance,
  handleDoctorClick,
  isListExpanded,
  setIsListExpanded,
  searchTerm,
  setSearchTerm,
  selectedEntity, // Додаємо selectedEntity в пропси
}: {
  filteredEntities: any[];
  lat: number;
  long: number;
  calculateDistance: (lat1: number, long1: number, lat2: number, long2: number) => number;
  handleDoctorClick: (entity: any) => void;
  isListExpanded: boolean;
  setIsListExpanded: (value: boolean) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedEntity: any | null;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [touchStart, setTouchStart] = React.useState<number | null>(null);
  const [touchEnd, setTouchEnd] = React.useState<number | null>(null);
  const minSwipeDistance = 50;
  const isDesktop = useWindowSize();

  React.useEffect(() => {
    const params = new URLSearchParams(searchParams?.toString());
    if (searchTerm) {
      params.set("search", searchTerm);
    } else {
      params.delete("search");
    }
    router.push(`?${params.toString()}`, { scroll: false });
  }, [searchTerm, router, searchParams]);

  const filteredDoctors = filteredEntities.filter((entity) => {
    const searchTermLower = searchTerm.toLowerCase();
    return (
      String(entity.name || '').toLowerCase().includes(searchTermLower) ||
      String(entity.Specialties || '').toLowerCase().includes(searchTermLower) ||
      String(entity.address || '').toLowerCase().includes(searchTermLower) ||
      String(entity.ClinicName || '').toLowerCase().includes(searchTermLower)
    );
  });

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isSwipeUp = distance > minSwipeDistance;
    const isSwipeDown = distance < -minSwipeDistance;

    if (isSwipeUp && !isListExpanded) {
      setIsListExpanded(true);
    } else if (isSwipeDown && isListExpanded) {
      setIsListExpanded(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value); // Оновлюємо searchTerm через пропс
  };

  // Визначаємо, які елементи показувати
  const displayedEntities = !isDesktop && selectedEntity 
    ? [selectedEntity] 
    : filteredDoctors;

  return (
    <div
      className={`flex flex-col border-gray-400 shadow-sm w-full mx-auto transition-all duration-300 rounded-l-3xl ${
        isListExpanded ? "h-[80vh]" : "h-[30vh]"
      } md:h-full absolute bottom-0 left-0 md:static z-10 bg-white`}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      style={{ maxHeight: "962px" }}
    >
      {isDesktop && ( // Показуємо пошук тільки на десктопі
        <div className="p-3 sm:p-4 border-b border-gray-100 flex flex-col gap-2 sm:gap-3">
          <div className="flex items-center gap-2">
            <button 
              className="w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center" 
              onClick={() => router.push('/chats')}
            >
              <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 text-gray-700" />
            </button>
            <div className="flex-1 relative">
              <div className="absolute left-2 sm:left-3 top-1/2 -translate-y-1/2 ">
              <Image
                src="/Search.svg"
                alt="Search icon"
                width={24}
                height={24}
                className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400"
              />
                {/* <Search className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" /> */}
              </div>
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={handleSearchChange}
                className="w-full h-8 sm:h-10 pl-8 bg-[#F5F5F5] sm:pl-10 pr-3 sm:pr-4 text-sm rounded-2xl border border-gray-200 text-gray-700"
              />
            </div>
          </div>
        </div>
      )}
      <h2 className="text-base sm:text-lg font-medium text-gray-800 text-center mt-3">
        {!isDesktop && selectedEntity 
          ? 'Selected Doctor'
          : `Doctor List • ${filteredDoctors.length} Results`}
      </h2>
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        {(!selectedEntity || isDesktop) ? (
          // Показуємо повний список якщо немає вибраного лікаря або це десктоп
          filteredDoctors.length > 0 ? (
            filteredDoctors.map((entity, index) => (
              <div key={index}>
                <div
                  onClick={() => handleDoctorClick(entity)}
                  className="p-3 sm:p-4 hover:bg-gray-50 cursor-pointer"
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-r from-purple-400 to-blue-400">
                        <Image
                          src={entity.avatar || "/1.svg"}
                          alt={`Avatar of ${entity.name}`}
                          width={64}
                          height={64}
                          className="rounded-full object-cover"
                        />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-base sm:text-lg font-bold text-gray-900 truncate">
                          {entity.name}
                        </h3>
                        <span className="text-sm sm:text-base font-medium text-gray-900 ml-2 whitespace-nowrap">
                          {calculateDistance(lat, long, entity.lat, entity.long).toFixed(0)} miles
                        </span>
                      </div>
                      <div className="flex items-center gap-1 mt-1">
                        <Star className="w-3 h-3 sm:w-4 sm:h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs sm:text-sm text-gray-700">4.5 (35)</span>
                      </div>
                      <div className="text-xs sm:text-sm text-gray-600 truncate">
                        {entity.Specialties || "Specialization"}
                      </div>
                      
                      <a
                        href={`tel:${entity.phone}`}
                        className="text-blue-500 text-xs sm:text-sm font-medium block mt-1 sm:mt-2"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {entity.phone || "************"}
                      </a>
                      <div className="text-gray-500 text-xs sm:text-sm mt-1 line-clamp-2">
                        {entity.address || "322 W North River Drive Spokane WA 99201-3208"}
                      </div>
                    </div>
                  </div>
                </div>
                {entity.WebsiteURL && (
                  <a
                    href={entity.WebsiteURL}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mx-4 md:max-w-[388px] md:w-[90%] w-[95%] mt-3 sm:mt-4 inline-flex items-center justify-center text-sm sm:text-base font-medium text-gray-800 bg-white transition-colors rounded-full relative"
                    style={{
                      background: "white",
                      height: "40px",
                    }}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <span className="relative z-10">
                      Visit Website
                      <span className="ml-2">→</span>
                    </span>
                    <span
                      className="absolute inset-0 md:rounded-full rounded-xl"
                      style={{
                        padding: "1px",
                        background: "linear-gradient(to right, #FFB300, #0051FF, #FF569F)",
                        mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                        maskComposite: "exclude",
                        WebkitMaskComposite: "xor",
                      }}
                    ></span>
                  </a>
                )}
                <div className="h-px bg-gray-200 mx-4 mt-8"></div>
              </div>
            ))
          ) : (
            <div className="p-4 text-gray-500 text-center">
              {searchTerm ? "No doctors found matching your search" : "No facilities within 100 miles"}
            </div>
          )
        ) : (
          // Показуємо тільки вибраного лікаря
          <div>
            <div
              onClick={() => handleDoctorClick(selectedEntity)}
              className="p-3 sm:p-4 hover:bg-gray-50 cursor-pointer"
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 rounded-full overflow-hidden bg-gradient-to-r from-purple-400 to-blue-400">
                    <Image
                      src={selectedEntity.avatar || "/1.svg"}
                      alt={`Avatar of ${selectedEntity.name}`}
                      width={64}
                      height={64}
                      className="rounded-full object-cover"
                    />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-base sm:text-lg font-bold text-gray-900 truncate">
                      {selectedEntity.name}
                    </h3>
                    <span className="text-sm sm:text-base font-medium text-gray-900 ml-2 whitespace-nowrap">
                      {calculateDistance(lat, long, selectedEntity.lat, selectedEntity.long).toFixed(0)} miles
                    </span>
                  </div>
                  <div className="flex items-center gap-1 mt-1">
                    <Star className="w-3 h-3 sm:w-4 sm:h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs sm:text-sm text-gray-700">4.5 (35)</span>
                  </div>
                  <div className="text-xs sm:text-sm text-gray-600 truncate">
                    {selectedEntity.Specialties || "Specialization"}
                  </div>
                  
                  <a
                    href={`tel:${selectedEntity.phone}`}
                    className="text-blue-500 text-xs sm:text-sm font-medium block mt-1 sm:mt-2"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {selectedEntity.phone || "************"}
                  </a>
                  <div className="text-gray-500 text-xs sm:text-sm mt-1 line-clamp-2">
                    {selectedEntity.address || "322 W North River Drive Spokane WA 99201-3208"}
                  </div>
                </div>
              </div>
            </div>
            {selectedEntity.WebsiteURL && (
              <a
                href={selectedEntity.WebsiteURL}
                target="_blank"
                rel="noopener noreferrer"
                className="mx-4 md:max-w-[388px] md:w-[90%] w-[95%] mt-3 sm:mt-4 inline-flex items-center justify-center text-sm sm:text-base font-medium text-gray-800 bg-white transition-colors rounded-full relative"
                style={{
                  background: "white",
                  height: "40px",
                }}
                onClick={(e) => e.stopPropagation()}
              >
                <span className="relative z-10">
                  Visit Website
                  <span className="ml-2">→</span>
                </span>
                <span
                  className="absolute inset-0 rounded-full"
                  style={{
                    padding: "1px",
                    background: "linear-gradient(to right, #FFB300, #0051FF, #FF569F)",
                    mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                    maskComposite: "exclude",
                    WebkitMaskComposite: "xor",
                  }}
                ></span>
              </a>
            )}
            <div className="h-px bg-gray-200 mx-4 mt-8"></div>
          </div>
        )}
      </div>
      <div className="h-1 w-16 bg-gray-300 rounded-full mx-auto mb-2 mt-1 md:hidden"></div>
    </div>
  );
}