'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import AddSymptomModal from './AddSymptomModal';
import { motion, AnimatePresence } from 'framer-motion';
import { OneUpHealthService, MedicalRecord } from '@/lib/services/oneUpHealthService';

interface SymptomModalProps {
  onClose: () => void;
}

const SymptomModal = ({ onClose }: SymptomModalProps) => {
  const router = useRouter();
  const [selectedSymptom, setSelectedSymptom] = useState<MedicalRecord | null>(null);
  const [showAddSymptomModal, setShowAddSymptomModal] = useState(false);
  const [symptoms, setSymptoms] = useState<MedicalRecord[]>([]);
  const [loading, setLoading] = useState(true);

  // Отримуємо userId з localStorage або беремо перший запис з бази, якщо не знайдено
  const [userId, setUserId] = useState<string>("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedId = localStorage.getItem("currentUserId");
      if (storedId && storedId !== "") {
        setUserId(storedId);
      } else {
        // fallback: отримати userId з першого запису
        (async () => {
          try {
            const service = new OneUpHealthService({ userId: "" });
            const records = await service.fetchMedicalRecordsFromDatabase();
            const first = records.find(r => r.type === "Symptom");
            if (first?.id) {
              setUserId(first.id);
            }
          } catch {
            setUserId("");
          }
        })();
      }
    }
  }, []);

  useEffect(() => {
    const fetchSymptoms = async () => {
      setLoading(true);
      if (!userId) {
        setSymptoms([]);
        setLoading(false);
        return;
      }
      try {
        const service = new OneUpHealthService({ userId });
        const records = await service.fetchMedicalRecordsFromDatabase();
        const symptomRecords = records.filter(r => r.type === "Symptom");
        setSymptoms(symptomRecords);
      } catch (error) {
        setSymptoms([]);
      }
      setLoading(false);
    };
    fetchSymptoms();
  }, [userId, showAddSymptomModal]);

  const handleAddSymptom = () => {
    setShowAddSymptomModal(true);
  };
  
  const handleSaveSymptom = (symptomData: any) => {
    // Here you would save the symptom data to your backend
    console.log('Saved symptom:', symptomData);
    setShowAddSymptomModal(false);
    // Symptoms will be refetched due to useEffect dependency
  };

  const handleSymptomClick = (symptom: MedicalRecord) => {
    setSelectedSymptom(symptom);
  };

  // Додаємо функцію форматування дати
  function formatDate(dateStr: string) {
    if (!dateStr) return "-";
    const d = new Date(dateStr);
    if (isNaN(d.getTime())) return dateStr;
    const day = String(d.getDate()).padStart(2, "0");
    const month = String(d.getMonth() + 1).padStart(2, "0");
    const year = d.getFullYear();
    return `${day}.${month}.${year}`;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-end items-stretch z-50 py-6 pr-6">
      <div className="bg-white w-[940px] h-full rounded-2xl overflow-hidden flex flex-col">
        {/* Unified Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b h-[56px]">
          <h2 className="text-xl font-medium">Symptom</h2>
          <div className="flex items-center gap-4">
            <button 
              className="flex items-center text-black"
              onClick={handleAddSymptom}
            >
              <span className="text-xl mr-1">+</span> Symptom
            </button>
            <button onClick={onClose}>
              <Image src="/xrestuk.svg" alt="Close" width={24} height={24} className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content area */}
        <div className="flex flex-1 overflow-hidden">
          {/* Left Panel - Symptom List */}
          <div className="w-[350px] flex flex-col border-r">
            {/* Symptom list */}
            <div className="flex-1 p-5 overflow-y-auto">
              <div className="space-y-8">
                {loading ? (
                  <div className="text-gray-400 text-center py-2">Loading...</div>
                ) : symptoms.length === 0 ? (
                  <div className="text-gray-400 text-center py-2">No symptoms found</div>
                ) : (
                  symptoms.map((symptom, index) => (
                    <div key={symptom.id} className="relative">
                      {/* Timeline line for all but the last item */}
                      {index < symptoms.length - 1 && (
                        <div className="absolute top-4 left-1.5 w-[1px] h-full bg-gray-200" style={{ transform: "translateX(-50%)" }}></div>
                      )}
                      {/* Timeline dot */}
                      <div className="absolute top-1.5 left-0 w-3 h-3 bg-white rounded-full border border-gray-400 z-10"></div>
                      {/* Date */}
                      <div className="ml-6 text-sm text-[#4285F4] bg-[#EDF2FD] rounded-full px-3 py-1 inline-block">
                        {formatDate(symptom.date)}
                      </div>
                      {/* Symptom card - clickable */}
                      <div 
                        className={`ml-6 bg-white rounded-2xl ${selectedSymptom?.id === symptom.id ? 'border-2 border-blue-300' : 'border border-gray-100'} p-4 mt-2 shadow-sm hover:border-gray-200 transition-colors cursor-pointer`}
                        onClick={() => handleSymptomClick(symptom)}
                      >
                        <div className="flex justify-between items-center">
                          <h2 className="text-[16px] font-medium">Symptom</h2>
                          <Image
                            src="/health/Arrow-right.svg"
                            alt="View details"
                            width={20}
                            height={20}
                          />
                        </div>
                        <div className="flex mt-2 items-center text-gray-600 bg-gray-50 rounded-xl p-2">
                          <Image 
                            src="/medical-record/note.svg"
                            alt="Description"
                            width={16}
                            height={16}
                            className="mr-2"
                          />
                          <span className="text-sm line-clamp-2">
                            {
                              symptom.rawData?.code
                                && typeof symptom.rawData.code === 'object'
                                && 'text' in symptom.rawData.code
                                && (symptom.rawData.code as any).text
                              || symptom.description
                            }
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Right Panel - Symptom Details */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 p-6 overflow-y-auto">
              <AnimatePresence mode="wait">
                {selectedSymptom ? (
                  <motion.div
                    key={`symptom-${selectedSymptom.id}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {/* Header with date */}
                    <div className="flex items-center mb-6">
                      <h2 className="text-xl font-medium">Symptom</h2>
                      <span className="text-gray-500 ml-3">{formatDate(selectedSymptom.date)}</span>
                    </div>
                    
                    {/* Content box */}
                    <div className="bg-[#FFF9F3] rounded-2xl p-6 border border-[#FFE8D4]">
                      <div className="flex items-start mb-4">
                        <div className="flex-shrink-0 mr-3 text-[#FF5C00]">
                          <Image 
                            src="/outpatient/heart-rate-monitor.svg" 
                            alt="Symptom Recording" 
                            width={28} 
                            height={28} 
                          />
                        </div>
                        <div>
                          <h3 className="text-[#FF5C00] font-medium">Symptom Recording</h3>
                        </div>
                      </div>
                      <p className="text-gray-700">
                        {
                          selectedSymptom.rawData?.code
                            && typeof selectedSymptom.rawData.code === 'object'
                            && 'text' in selectedSymptom.rawData.code
                            && (selectedSymptom.rawData.code as any).text
                          || selectedSymptom.description
                        }
                      </p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="empty"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="flex items-center justify-center h-full text-gray-400">
                      <p>Select a symptom to view details</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
      
      {/* Add Symptom Modal */}
      {showAddSymptomModal && (
        <AddSymptomModal 
          onClose={() => setShowAddSymptomModal(false)} 
          onSave={handleSaveSymptom} 
        />
      )}
    </div>
  );
};

export default SymptomModal;
