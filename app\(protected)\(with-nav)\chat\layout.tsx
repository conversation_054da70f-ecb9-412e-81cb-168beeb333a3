import NavBar from "@/components/NavBar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { cookies } from "next/headers";
import Image from "next/image";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get("sidebar_state")?.value === "true";

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <div className="flex flex-col h-screen w-full overflow-hidden">
        {/* Header with logo and sidebar trigger */}
        <header className="flex  justify-between items-center bg-[#F5F5F54D] backdrop-blur-[20px] w-full py-[16px] px-[20px] md:left-20 md:pl-[100px] z-50 fixed top-0 left-0">
          <div className="flex items-center">
            <Image
              alt="Logo"
              src="/chat/logo.svg"
              width={118}
              height={80}
              priority
            />
          </div>
          <div>
            <SidebarTrigger className="p-2 hover:bg-gray-100 rounded-md transition-colors" />
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 overflow-auto relative">{children}</main>
      </div>
    </SidebarProvider>
  );
}
