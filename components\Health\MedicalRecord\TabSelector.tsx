import React from 'react';

interface TabSelectorProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isModal?: boolean;
}

const TabSelector: React.FC<TabSelectorProps> = ({ 
  activeTab, 
  onTabChange,
  isModal = false 
}) => {
  const tabs = ["All", "Day", "Month", "Year"];
  
  return (
    <div className={`flex space-x-2 ${isModal ? 'px-6 pb-6' : 'my-[30px] h-[38px]'}`}>
      {tabs.map((tab) => (
        <button
          key={tab}
          className={`
            ${isModal 
              ? 'w-[72px] h-[38px] flex items-center justify-center' 
              : 'px-6 py-2 w-[87px]'
            } 
            rounded-full text-sm 
            ${activeTab === tab
              ? "bg-[#E9F0FF] text-blue-500 font-medium" + (!isModal ? " shadow-sm" : "")
              : "bg-[#F5F5F5] text-black"
            }
          `}
          onClick={() => onTabChange(tab)}
        >
          {tab}
        </button>
      ))}
    </div>
  );
};

export default TabSelector;
