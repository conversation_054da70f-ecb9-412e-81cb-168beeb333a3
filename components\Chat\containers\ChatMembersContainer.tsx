"use client";

import { motion } from "framer-motion";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { fetchAuthSession } from "aws-amplify/auth";
import { Chat } from "@/types/chat";
import { useViewState } from "@/components/Chat/context/ViewStateContext";
import { useRouter, usePathname } from "next/navigation";
import { parseChatPathname } from "@/utils/pathHelpers";

// Variants for the mobile details panel
const mobileDetailsVariants = {
  hidden: {
    opacity: 0,
    flex: "0 0 0%",
    y: 50,
  },
  visible: {
    opacity: 1,
    flex: "0 0 100%",
    y: 0,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
  exit: {
    opacity: 0,
    flex: "0 0 0%",
    y: 50,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
};

interface ChatMemberType {
  user?: {
    id?: string;
    userId?: string;
    email?: string;
    profilePicture?: string;
  };
  userId: string;
  profilePicture?: string;
}

interface ChatMembersContainerProps {
  currentChat: Chat | null;
  showAllMembers: boolean;
  setShowAllMembers: (value: boolean) => void;
  onMemberClick?: (member: ChatMemberType) => void;
}

/**
 * Component specifically for displaying chat members in details view
 */
export const ChatMembersContainer = ({
  currentChat,
  showAllMembers,
  setShowAllMembers,
  onMemberClick,
}: ChatMembersContainerProps) => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const { setActiveView, setSelectedUserDetail } = useViewState();

  useEffect(() => {
    const getCurrentUserId = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.sub) {
          const userId = String(session.tokens.idToken.payload.sub);
          setCurrentUserId(userId);
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    getCurrentUserId();
  }, []);

  // Get participants excluding current user
  const filteredParticipants = currentChat?.chatParticipants
    ? currentChat.chatParticipants.filter(
        (member) => member.userId !== currentUserId
      )
    : [];

  const visibleMembers = showAllMembers
    ? filteredParticipants
    : filteredParticipants.slice(0, 8);

  const hasMoreMembers = filteredParticipants.length > 8;

  // For direct chats, get the other member
  const directChatMember =
    currentChat?.chatType === "DIRECT" && filteredParticipants.length > 0
      ? filteredParticipants[0]
      : null;

  const router = useRouter();
  const pathname = usePathname();

  const handleMemberClick = (member: ChatMemberType) => {
    if (onMemberClick) {
      onMemberClick(member);
      return;
    }

    if (member.user) {
      const userWithId = {
        ...member.user,
        id: member.user.id || member.userId,
        userId: member.user.userId || member.user.id || member.userId,
      };
      setSelectedUserDetail(userWithId);
      setActiveView("USER_DETAILS");
      
      // Get the current chatId from the pathname
      const parsedPath = parseChatPathname(pathname);
      if (parsedPath.chatId) {
        // Navigate to user details with the current chatId in the URL to maintain context
        router.push(`/chats/${parsedPath.chatId}/user/${userWithId.userId}`);
      }
    } else {
      console.warn(
        "Clicked member does not have a user object. Using partial data."
      );
      const userData = {
        id: member.userId,
        userId: member.userId,
        email: `User ${member.userId}`,
        profilePicture: member.profilePicture,
      };
      setSelectedUserDetail(userData);
      setActiveView("USER_DETAILS");

      // Get the current chatId from the pathname
      const parsedPath = parseChatPathname(pathname);
      if (parsedPath.chatId) {
        // Navigate to user details with the current chatId in the URL to maintain context
        router.push(`/chats/${parsedPath.chatId}/user/${userData.userId}`);
      }
    }
  };

  if (!currentChat) {
    return null;
  }

  if (currentChat.chatType === "DIRECT" && directChatMember?.user) {
    // Direct chat - show the other participant
    return (
      <motion.div
        className="flex-1 flex flex-col overflow-y-auto p-4"
        variants={mobileDetailsVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex flex-col items-center mb-4 mt-2">
          <div className="w-[120px] h-[120px] relative flex items-center justify-center mb-4">
            <div className="absolute w-full h-full rounded-full bg-[#2567FF57] opacity-20"></div>
            <div className="absolute w-[80%] h-[80%] rounded-full bg-[#2567FF24] opacity-30"></div>
            <div className="absolute w-[60%] h-[60%] rounded-full bg-[#2567FF14] opacity-40"></div>

            <Avatar className="relative z-10 w-[80px] h-[80px] border-2 border-white">
              <AvatarImage
                src={directChatMember.user?.profilePicture || "/Avatar.png"}
                alt={directChatMember.user?.email || "Contact"}
              />
              <AvatarFallback>
                {directChatMember.user?.email?.charAt(0)?.toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
          </div>
          <h2 className="text-xl font-semibold mb-1">
            {directChatMember.user?.email || "Contact"}
          </h2>
        </div>
      </motion.div>
    );
  }

  if (currentChat.chatType === "GROUP") {
    // Group chat - show all members
    return (
      <motion.div
        className="flex-1 flex flex-col overflow-y-auto p-4"
        variants={mobileDetailsVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex flex-wrap gap-4 justify-center">
          {visibleMembers.length === 0 ? (
            <div className="text-center text-gray-500 py-4 w-full">
              No members found
            </div>
          ) : (
            visibleMembers.map((member, i) => (
              <div
                key={`${member.userId}-${i}`}
                className="flex flex-col items-center gap-2 mb-3 cursor-pointer"
                onClick={() => handleMemberClick(member)}
              >
                <Avatar className="h-14 w-14 border-2 border-white shadow-sm">
                  <AvatarImage
                    src={member.user?.profilePicture || "/Avatar.png"}
                    alt={member.user?.email || `Member ${i + 1}`}
                  />
                </Avatar>
                <span className="max-w-20 text-sm font-medium text-black text-center truncate">
                  {member.user?.email || `User ${i + 1}`}
                </span>
              </div>
            ))
          )}
        </div>

        {hasMoreMembers && !showAllMembers && (
          <div className="flex justify-center my-2">
            <Button
              variant="outline"
              className="rounded-full py-2 px-4 flex items-center gap-2 border shadow-sm bg-white"
              onClick={() => setShowAllMembers(true)}
              aria-label="Show more members"
            >
              <span className="text-[#929292] font-medium text-sm">More</span>
              <ChevronDown className="h-4 w-4 text-[#929292]" />
            </Button>
          </div>
        )}
      </motion.div>
    );
  }

  return null;
};
