"use client";

import { useState } from "react";
import { useUser } from "@/hooks/useUsers";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import OneUpHealthConnect from "@/components/Health/OneUpHealthConnect";

export default function HealthConnections() {
  const { currentUser } = useUser();
  const [activeTab, setActiveTab] = useState("connect");

  // In a production app, these would be securely stored and retrieved
  // For demo purposes, these are hardcoded (you should use environment variables)
  const clientId = "591e25edc3e5013a7743658a26875d3a";
  const clientSecret = "2cc1b5cd9264a67f8666ac48aaf2a265";

  if (!currentUser) {
    return (
      <div className="flex justify-center items-center min-h-[50vh]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access your health connections
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Button onClick={() => (window.location.href = "/login")}>
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-5xl px-4">
      <h1 className="text-3xl font-bold mb-8">Health Connections</h1>

      <Tabs
        defaultValue="connect"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid grid-cols-2 w-full max-w-md mb-8">
          <TabsTrigger value="connect">Connect Providers</TabsTrigger>
          <TabsTrigger value="manage">Manage Data</TabsTrigger>
        </TabsList>

        <TabsContent value="connect">
          {/* <OneUpHealthConnect
            userId={currentUser.userId}
            clientId={clientId}
            clientSecret={clientSecret}
          /> */}
        </TabsContent>

        <TabsContent value="manage">
          <Card>
            <CardHeader>
              <CardTitle>Manage Health Data</CardTitle>
              <CardDescription>
                View, update, and control your imported health records
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Data Refresh</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Manually refresh your health data from connected providers
                  </p>
                  <Button variant="outline">Refresh Data</Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Data Permissions</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Control how your health data is used within the application
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>Use data for health insights</span>
                      <input
                        type="checkbox"
                        defaultChecked
                        className="toggle"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Send reminders based on medications</span>
                      <input
                        type="checkbox"
                        defaultChecked
                        className="toggle"
                      />
                    </div>
                  </div>
                </div>

                <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                  <h3 className="font-medium text-red-700 mb-2">Danger Zone</h3>
                  <p className="text-sm text-red-600 mb-4">
                    These actions cannot be undone
                  </p>
                  <div className="space-x-2">
                    <Button variant="destructive" size="sm">
                      Remove All Connections
                    </Button>
                    <Button variant="outline" size="sm">
                      Delete Imported Data
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
