"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useContext } from "react";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import NewChatViewManager from "@/components/NewChat/NewChatViewManager";
import MobileHeader from "@/components/Chat/MobileHeader";
import { ChatHeaderWrapper } from "@/components/Chat/headers/ChatHeaderWrapper";
import { NewChatContainer } from "./NewChatContainer";

interface MobileHeaderContainerProps {
  toggleChatDetails: () => void;
  handleDetailsViewBackClick: () => void;
  handleLeaveGroup: () => Promise<void>;
}

/**
 * Container to manage which header to display in mobile view
 */
export const MobileHeaderContainer = ({
  toggleChatDetails,
  handleDetailsViewBackClick,
  handleLeaveGroup,
}: MobileHeaderContainerProps) => {
  const { showChatDetails, showNewChatPage } = useContext(ChatContext);

  return (
    <div className="flex-shrink-0">
      <AnimatePresence mode="wait">
        {/* Standard chat header */}
        {!showChatDetails && !showNewChatPage && (
          <motion.div
            key="chat-header"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <ChatHeaderWrapper
              onGroupDetailsClick={toggleChatDetails}
              handleLeaveGroup={handleLeaveGroup}
            />
          </motion.div>
        )}

        {/* Chat details back header */}
        {showChatDetails && !showNewChatPage && (
          <motion.div
            key="chat-details-header"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <MobileHeader onBackClick={handleDetailsViewBackClick} />
          </motion.div>
        )}

        {/* New chat container (includes header and confirm group chat) */}
        {showNewChatPage && (
          <motion.div
            key="new-chat-container"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <NewChatContainer />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
