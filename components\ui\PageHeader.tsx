"use client";

import React from "react";

import { cn } from "@/lib/utils";

interface PageHeaderProps {
  title?: React.ReactNode;
  leftContent?: React.ReactNode;
  rightContent?: React.ReactNode;
  className?: string;
  background?: "default" | "transparent" | "blur" | "white";
  sticky?: boolean;
}

const PageHeader = ({
  title,
  leftContent,
  rightContent,
  className,
  background = "default",
  sticky = true,
}: PageHeaderProps) => {
  const backgroundClasses = {
    white: "bg-white",
    default: "bg-[#F5F5F5]",
    transparent: "bg-transparent",
    blur: "bg-[#F5F5F54D] backdrop-blur-md",
  };

  return (
    <div
      className={cn(
        "grid grid-cols-[1fr_2fr_1fr] items-center px-4 py-3 border-b",
        backgroundClasses[background],
        sticky && "sticky top-0 z-20",
        className
      )}
    >
      <div className="justify-self-start">{leftContent}</div>
      <div className="justify-self-center text-center">{title}</div>
      <div className="justify-self-end">{rightContent}</div>
    </div>
  );
};

export default PageHeader;
