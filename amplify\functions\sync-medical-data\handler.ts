import { Amplify } from "aws-amplify";
import axios, { type AxiosError } from "axios";
import { generateClient } from "@aws-amplify/api";
import type { Schema } from "../../data/resource";
import type { Handler } from "aws-lambda";
import { getAmplifyDataClientConfig } from "@aws-amplify/backend/function/runtime";
import { env } from "$amplify/env/sync-medical-data";

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

interface ResponseType {
  success: boolean;
  error?: string | null;
  data?: {
    syncedRecords?: number;
    message?: string;
  };
}

enum Endpoint {
  PATIENT = "Patient",
  CONDITION = "Condition",
  OBSERVATION = "Observation",
  MEDICATION = "MedicationRequest",
  PROCEDURE = "Procedure",
  ALLERGY = "AllergyIntolerance",
  ENCOUNTER = "Encounter",
}

export const handler: Handler = async (event: any): Promise<ResponseType> => {
  console.log("Sync Medical Data event:", JSON.stringify(event, null, 2));

  const fieldName = event.fieldName || event.info?.fieldName;
  const userId = event.identity?.sub;

  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    return {
      success: false,
      error: "OneUp Health credentials not configured",
      data: undefined,
    };
  }

  if (!userId) {
    return {
      success: false,
      error: "User authentication required - no user ID found",
      data: undefined,
    };
  }

  try {
    // Test client connection
    console.log("Testing client connection...");
    if (!client || !client.models) {
      console.error("Client or client.models is not available");
      return {
        success: false,
        error: "Database client not properly initialized",
        data: undefined,
      };
    }

    switch (fieldName) {
      case "syncMedicalData":
        return await syncMedicalDataToDatabase(clientId, clientSecret, userId);
      default:
        return {
          success: false,
          error: `Unsupported field: ${fieldName}`,
          data: undefined,
        };
    }
  } catch (error: unknown) {
    console.error("Error syncing medical data:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Internal server error",
      data: undefined,
    };
  }
};

async function syncMedicalDataToDatabase(
  clientId: string,
  clientSecret: string,
  userId: string
): Promise<ResponseType> {
  try {
    console.log("Starting sync for user:", userId);
    console.log("Client models available:", Object.keys(client.models || {}));

    // Check if the UsersMedicalRecords model exists
    if (!client.models?.UsersMedicalRecords) {
      console.error("UsersMedicalRecords model not found in client.models");
      console.log("Available models:", Object.keys(client.models || {}));
      return {
        success: false,
        error: "Database model not available. Please check the configuration.",
        data: undefined,
      };
    }

   
    // Get valid access token for user
    const accessToken = await getValidAccessToken(
      clientId,
      clientSecret,
      userId
    );
    if (!accessToken) {
      return {
        success: false,
        error: "Failed to obtain valid access token",
        data: undefined,
      };
    }

    let totalSyncedRecords = 0;
    let totalUpdatedRecords = 0;

    // Sync different types of medical records
    const endpoints = [
      Endpoint.PATIENT,
      Endpoint.CONDITION,
      Endpoint.OBSERVATION,
      Endpoint.MEDICATION,
      Endpoint.PROCEDURE,
      Endpoint.ALLERGY,
      Endpoint.ENCOUNTER,
    ];

    for (const endpoint of endpoints) {
      try {
        const records = await fetchMedicalRecordsFrom1upHealth(
          clientId,
          clientSecret,
          accessToken,
          endpoint
        );

        console.log(`Fetched ${records.length} records from ${endpoint}`);

        for (const record of records) {
          const medicalRecordData = map1upHealthRecord(record, userId);

          // Handle Patient resource to sync connected systems
          if (medicalRecordData.resourceType === "Patient") {
            const rawData = JSON.parse(medicalRecordData.rawData);
            // Use only the id from the source
            const systemId = rawData.meta?.source?.split(':')[1] || "Unknown";

            if (systemId !== "Unknown") {
              try {
                await upsertUserConnectedSystem(userId, systemId);
                console.log(
                  `Synced UserConnectedSystem for user ${userId}, system ${systemId}`
                );
              } catch (error) {
                console.error(
                  `Failed to sync UserConnectedSystem for user ${userId}, system ${systemId}:`,
                  error
                );
                // Continue despite error to avoid blocking record sync
              }
            }
          }

          try {
            const result = await upsertMedicalRecord(medicalRecordData);
            if (result.created) {
              totalSyncedRecords++;
              console.log(`Created new record: ${medicalRecordData.recordId}`);
            } else if (result.updated) {
              totalUpdatedRecords++;
              console.log(`Updated record: ${medicalRecordData.recordId}`);
            }
          } catch (recordError) {
            console.error(
              `Error processing record ${medicalRecordData.recordId}:`,
              recordError
            );
            // Continue with other records even if one fails
          }
        }
      } catch (endpointError) {
        console.error(`Error syncing ${endpoint} data:`, endpointError);
        // Continue with other endpoints even if one fails
      }
    }

    // Update lastSync timestamp in HealthDataConnection
    try {
      const { data: connection } = await client.models.HealthDataConnection.get(
        {
          appUserId: userId,
        }
      );

      if (connection) {
        await client.models.HealthDataConnection.update({
          appUserId: connection.appUserId,
          accessToken: connection.accessToken,
          refreshToken: connection.refreshToken,
          code: connection.code,
          updatedAt: new Date().toISOString(),
        });
      }
    } catch (connectionError) {
      console.error("Error updating lastSync timestamp:", connectionError);
      // Don't fail the entire sync for this
    }

    return {
      success: true,
      data: {
        syncedRecords: totalSyncedRecords,
        message: `Successfully synced ${totalSyncedRecords} new records and updated ${totalUpdatedRecords} existing records`,
      },
      error: null,
    };
  } catch (error: unknown) {
    console.error("Error in syncMedicalDataToDatabase:", error);
    throw error;
  }
}

/**
 * Upsert a medical record with proper error handling and retry logic
 */
async function upsertMedicalRecord(medicalRecordData: {
  userId: string;
  recordId: string;
  resourceType: string;
  rawData: string;
  lastUpdated: string;
  category: string;
  date: string;
  provider: string;
  medicalSystem: string;
  externalId: string | null;
  status: string;
}): Promise<{ created: boolean; updated: boolean }> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Try to get existing record first
      const { data: existingRecord } =
        await client.models.UsersMedicalRecords.get({
          userId: medicalRecordData.userId,
          recordId: medicalRecordData.recordId,
        });

      if (existingRecord) {
        // Update existing record
        const response = await client.models.UsersMedicalRecords.update({
          userId: medicalRecordData.userId,
          recordId: medicalRecordData.recordId,
          resourceType: medicalRecordData.resourceType,
          rawData: medicalRecordData.rawData,
          lastUpdated: medicalRecordData.lastUpdated,
          category: medicalRecordData.category,
          date: medicalRecordData.date,
          provider: medicalRecordData.provider,
          medicalSystem: medicalRecordData.medicalSystem,
          externalId: medicalRecordData.externalId,
          status: medicalRecordData.status,
        });

        if (response.data) {
          return { created: false, updated: true };
        } else if (response.errors) {
          throw new Error(`Update failed: ${JSON.stringify(response.errors)}`);
        }
      } else {
        // Create new record
        const response = await client.models.UsersMedicalRecords.create({
          userId: medicalRecordData.userId,
          recordId: medicalRecordData.recordId,
          resourceType: medicalRecordData.resourceType,
          rawData: medicalRecordData.rawData,
          lastUpdated: medicalRecordData.lastUpdated,
          category: medicalRecordData.category,
          date: medicalRecordData.date,
          provider: medicalRecordData.provider,
          medicalSystem: medicalRecordData.medicalSystem,
          externalId: medicalRecordData.externalId,
          status: medicalRecordData.status,
        });

        if (response.data) {
          return { created: true, updated: false };
        } else if (response.errors) {
          const errorMessages = response.errors
            .map((e) => e.message)
            .join(", ");

          // Check if it's a conditional check failure (record already exists)
          if (errorMessages.includes("ConditionalCheckFailedException")) {
            console.log(
              `Record ${medicalRecordData.recordId} already exists, attempting update...`
            );

            // Try to update instead
            const updateResponse =
              await client.models.UsersMedicalRecords.update({
                userId: medicalRecordData.userId,
                recordId: medicalRecordData.recordId,
                resourceType: medicalRecordData.resourceType,
                rawData: medicalRecordData.rawData,
                lastUpdated: medicalRecordData.lastUpdated,
                category: medicalRecordData.category,
                date: medicalRecordData.date,
                provider: medicalRecordData.provider,
                medicalSystem: medicalRecordData.medicalSystem,
                externalId: medicalRecordData.externalId,
                status: medicalRecordData.status,
              });

            if (updateResponse.data) {
              return { created: false, updated: true };
            }
          }

          throw new Error(`Create failed: ${errorMessages}`);
        }
      }

      // If we get here, neither data nor errors were returned
      throw new Error("Unexpected response: no data or errors returned");
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt < maxRetries) {
        console.log(
          `Attempt ${attempt} failed for record ${medicalRecordData.recordId}, retrying...`
        );
        // Exponential backoff: wait 100ms * 2^(attempt-1)
        await new Promise((resolve) =>
          setTimeout(resolve, 100 * Math.pow(2, attempt - 1))
        );
      }
    }
  }

  // If all retries failed, throw the last error
  throw new Error(
    `Failed to upsert record ${medicalRecordData.recordId} after ${maxRetries} attempts: ${lastError?.message}`
  );
}

/**
 * Upsert a UserConnectedSystem record for a given user and system
 */
async function upsertUserConnectedSystem(
  userId: string,
  systemId: string
): Promise<void> {
  const now = new Date().toISOString();
  const existing = await client.models.UserConnectedSystem.get({
    userId,
    systemId,
  });

  if (existing.data) {
    await client.models.UserConnectedSystem.update({
      userId,
      systemId,
      updatedAt: now,
    });
  } else {
    await client.models.UserConnectedSystem.create({
      userId,
      systemId,
      createdAt: now,
      updatedAt: now,
    });
  }
}

async function fetchMedicalRecordsFrom1upHealth(
  clientId: string,
  clientSecret: string,
  accessToken: string,
  endpoint: Endpoint
): Promise<any[]> {
  try {
    const response = await axios.get(`https://api.1up.health/r4/${endpoint}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json",
      },
    });

    console.log(`Response for ${endpoint}:`, response.status, response.data);

    if (response.data && response.data.entry) {
      return response.data.entry.map((entry: any) => entry.resource);
    }

    return [];
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error(`Error fetching ${endpoint} data from 1upHealth:`, {
      status: axiosError?.response?.status,
      data: axiosError?.response?.data,
      message: axiosError?.message,
    });
    return [];
  }
}

function map1upHealthRecord(
  record: Record<string, unknown>,
  userId: string
): {
  userId: string;
  recordId: string;
  resourceType: string;
  rawData: string;
  lastUpdated: string;
  category: string;
  date: string;
  provider: string;
  medicalSystem: string;
  externalId: string | null;
  status: string;
} {
  const resourceType = (record.resourceType as string) || "Unknown";
  const recordId = (record.id as string) || `${resourceType}_${Date.now()}`;

  let category = "General";
  switch (resourceType) {
    case "Patient":
      category = "Patient Information";
      break;
    case "Condition":
      category = "Medical Conditions";
      break;
    case "Observation":
      category = "Lab Results";
      break;
    case "MedicationRequest":
    case "Medication":
      category = "Medications";
      break;
    case "Procedure":
      category = "Procedures";
      break;
    case "AllergyIntolerance":
      category = "Allergies";
      break;
    case "Encounter":
      category = "Visits";
      break;
    default:
      category = "Other";
  }

  let recordDate = null;
  const dateFields = [
    "effectiveDateTime",
    "authoredOn",
    "recordedDate",
    "onsetDateTime",
    "period.start",
    "issued",
    "date",
  ];

  for (const field of dateFields) {
    const value = getNestedValue(record, field);
    if (value) {
      recordDate = new Date(value as string);
      break;
    }
  }

  const dateString = recordDate
    ? recordDate.toISOString()
    : new Date().toISOString();

  const meta = record.meta as Record<string, unknown> | undefined;
  const identifier = record.identifier as
    | Array<Record<string, unknown>>
    | undefined;

  let serializedRawData: string;
  try {
    serializedRawData = JSON.stringify(record);
  } catch (serializeError) {
    console.error("Error serializing rawData:", serializeError);
    serializedRawData = JSON.stringify({
      error: "Failed to serialize data",
      resourceType,
    });
  }

  return {
    userId,
    recordId,
    resourceType,
    rawData: serializedRawData,
    lastUpdated: new Date().toISOString(),
    category,
    date: dateString,
    provider: "1upHealth",
    medicalSystem: (meta?.source as string) || "Unknown",
    externalId: (identifier?.[0]?.value as string) || null,
    status: (record.status as string) || "active",
  };
}

function getNestedValue(obj: Record<string, unknown>, path: string): unknown {
  return path.split(".").reduce((current: unknown, key: string) => {
    if (current && typeof current === "object") {
      return (current as Record<string, unknown>)[key];
    }
    return undefined;
  }, obj);
}

async function verifyAccessToken(accessToken: string): Promise<boolean> {
  try {
    // Make a simple request to verify the token is valid
    const response = await axios.get(
      "https://api.1up.health/r4/Patient?_count=1",
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response.status >= 200 && response.status < 300;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(
      "Access token verification failed:",
      axiosError?.response?.data || axiosError?.message
    );
    return false;
  }
}

async function getValidAccessToken(
  clientId: string,
  clientSecret: string,
  userId: string
): Promise<string | null> {
  const appUserId = `jinix_${userId}`;

  try {
    // Fetch HealthDataConnection record
    const { data: connection } = await client.models.HealthDataConnection.get({
      appUserId,
    });

    if (!connection) {
      console.log(
        `No health data connection found for user: ${userId}, creating new connection...`
      );
      // Create a new user and connection
      try {
        // Create 1upHealth user
        const userResponse = await axios.post(
          `https://api.1up.health/user-management/v1/user?app_user_id=${appUserId}`,
          {},
          {
            headers: {
              client_id: clientId,
              client_secret: clientSecret,
            },
          }
        );

        // Get authorization code
        const authCodeResponse = await axios.post(
          `https://api.1up.health/user-management/v1/user/auth-code?app_user_id=${appUserId}&client_id=${clientId}&client_secret=${clientSecret}`,
          {}
        );
        const code = authCodeResponse.data.code;

        // Exchange code for tokens
        const tokenResponse = await axios.post(
          "https://auth.1up.health/oauth2/token",
          new URLSearchParams({
            grant_type: "authorization_code",
            code: String(code),
            client_id: clientId,
            client_secret: clientSecret,
          }),
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        const tokenData = tokenResponse.data as {
          access_token: string;
          refresh_token: string;
          expires_in: number;
        };

        if (!tokenData?.access_token) {
          console.error(
            `Failed to obtain initial access token for user: ${userId}`
          );
          return null;
        }

        const now = new Date();

        // Create HealthDataConnection record
        await client.models.HealthDataConnection.create({
          appUserId,
          userId,
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token,
          code,
          createdAt: now.toISOString(),
          updatedAt: now.toISOString(),
        });

        console.log(`Created new health data connection for user: ${userId}`);
        return tokenData.access_token;
      } catch (error) {
        const axiosError = error as AxiosError;
        console.error(
          "Error creating new health data connection:",
          axiosError?.response?.data || axiosError?.message
        );
        return null;
      }
    }

    // Verify if the access token is valid
    const isTokenValid = await verifyAccessToken(connection.accessToken);
    if (isTokenValid) {
      console.log(`Using existing valid access token for user: ${userId}`);
      return connection.accessToken;
    }

    console.log(
      `Access token invalid for user: ${userId}, attempting to refresh...`
    );
    try {
      // Attempt to refresh the token
      const tokenResponse = await axios.post(
        "https://auth.1up.health/oauth2/token",
        new URLSearchParams({
          grant_type: "refresh_token",
          refresh_token: connection.refreshToken,
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const tokenData = tokenResponse.data as {
        access_token: string;
        refresh_token?: string;
        expires_in: number;
      };

      if (tokenData?.access_token) {
        const newAccessToken = tokenData.access_token;
        const newRefreshToken =
          tokenData.refresh_token || connection.refreshToken;
        const now = new Date();

        await client.models.HealthDataConnection.update({
          appUserId: connection.appUserId,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          updatedAt: now.toISOString(),
        });

        console.log(`Successfully refreshed access token for user: ${userId}`);
        return newAccessToken;
      } else {
        throw new Error("No access token returned in refresh response");
      }
    } catch (refreshError) {
      const axiosError = refreshError as AxiosError;
      console.error(
        "Error refreshing access token:",
        axiosError?.response?.data || axiosError?.message
      );

      // If refresh token is invalid/expired, generate a new auth code
      if (
        axiosError.response?.status === 400 ||
        axiosError.response?.status === 401
      ) {
        console.log("Refresh token expired, generating new auth code...");
        const authCodeResponse = await axios.post(
          `https://api.1up.health/user-management/v1/user/auth-code?app_user_id=${appUserId}&client_id=${clientId}&client_secret=${clientSecret}`,
          {}
        );
        const code = authCodeResponse.data.code;

        // Exchange new code for tokens
        const tokenResponse = await axios.post(
          "https://auth.1up.health/oauth2/token",
          new URLSearchParams({
            grant_type: "authorization_code",
            code: String(code),
            client_id: clientId,
            client_secret: clientSecret,
          }),
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        const tokenData = tokenResponse.data as {
          access_token: string;
          refresh_token: string;
          expires_in: number;
        };

        if (tokenData?.access_token) {
          const now = new Date();
          await client.models.HealthDataConnection.update({
            appUserId: connection.appUserId,
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            code,
            updatedAt: now.toISOString(),
          });

          console.log(
            `Successfully obtained new access token for user: ${userId} using new auth code`
          );
          return tokenData.access_token;
        } else {
          throw new Error("No access token returned in new token response");
        }
      }

      throw new Error(
        `Failed to refresh access token: ${axiosError?.response?.data || axiosError?.message}`
      );
    }
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(
      "Error getting valid access token:",
      axiosError?.response?.data || axiosError?.message
    );
    return null;
  }
}