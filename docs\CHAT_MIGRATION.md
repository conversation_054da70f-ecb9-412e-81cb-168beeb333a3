# Chat Layout Migration Guide

This guide provides step-by-step instructions for migrating components from the old chat layout architecture to the new one.

## Migration Steps

### Step 1: Use the new ViewStateContext instead of boolean flags

**Old approach:**

```tsx
import { useContext } from "react";
import { ChatContext } from "@/components/Chat/context/ChatContext";

function MyComponent() {
  const { showChatDetails, setShowChatDetails } = useContext(ChatContext);

  const toggleDetails = () => {
    setShowChatDetails(!showChatDetails);
  };

  return (
    <div>
      {showChatDetails && <DetailsContent />}
      <button onClick={toggleDetails}>
        {showChatDetails ? "Hide Details" : "Show Details"}
      </button>
    </div>
  );
}
```

**New approach:**

```tsx
import { useViewState } from "@/components/Chat/context/ViewStateContext";

function MyComponent() {
  const { activeView, setActiveView, isViewActive } = useViewState();

  const toggleDetails = () => {
    if (isViewActive("CHAT_DETAILS")) {
      setActiveView("CHAT_CONVERSATION");
    } else {
      setActiveView("CHAT_DETAILS");
    }
  };

  return (
    <div>
      {isViewActive("CHAT_DETAILS") && <DetailsContent />}
      <button onClick={toggleDetails}>
        {isViewActive("CHAT_DETAILS") ? "Hide Details" : "Show Details"}
      </button>
    </div>
  );
}
```

### Step 2: Use the standardized layout components

**Old approach:**

```tsx
<div className="flex flex-col md:flex-row h-full w-full overflow-hidden">
  <div className="w-full md:w-[400px] md:border-r border-gray-200 overflow-hidden">
    <ChatList />
  </div>

  <div className="flex-1 overflow-hidden md:relative">
    <div className="flex-grow overflow-hidden relative bg-white">
      {children}
    </div>
  </div>
</div>
```

**New approach:**

```tsx
import { ChatLayout } from "@/components/Chat/layout/ChatLayout";
import { ChatSidebar } from "@/components/Chat/layout/ChatSidebar";
import { MainContentArea } from "@/components/Chat/layout/MainContentArea";

<ChatLayout
  sidebar={
    <ChatSidebar>
      <ChatList />
    </ChatSidebar>
  }
  detailsPanel={<DetailsPanel>{renderDetailsPanel()}</DetailsPanel>}
>
  <MainContentArea>{children}</MainContentArea>
</ChatLayout>;
```

### Step 3: Use the centralized animation system

**Old approach:**

```tsx
const mobileDetailsVariants = {
  hidden: {
    opacity: 0,
    flex: "0 0 0px",
    y: 50,
  },
  visible: {
    opacity: 1,
    flex: "0 0 40vh",
    y: 0,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
  exit: {
    opacity: 0,
    flex: "0 0 0px",
    y: 50,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
};

<motion.div
  className="flex-1 flex flex-col overflow-y-auto"
  variants={mobileDetailsVariants}
  initial="hidden"
  animate="visible"
  exit="exit"
>
  {content}
</motion.div>;
```

**New approach:**

```tsx
import {
  mobileDetailsVariants,
  AnimatedPanel,
} from "@/components/Chat/animations/LayoutAnimations";

<AnimatedPanel
  className="flex-1 flex flex-col overflow-y-auto"
  variants={mobileDetailsVariants}
  initialState="hidden"
  animateState="visible"
>
  {content}
</AnimatedPanel>;
```

### Step 4: Use the useMobile hook consistently

**Old approach:**

```tsx
const [isMobile, setIsMobile] = useState(false);

useEffect(() => {
  const checkMobile = () => {
    setIsMobile(window.innerWidth < 768);
  };

  checkMobile();
  window.addEventListener("resize", checkMobile);
  return () => window.removeEventListener("resize", checkMobile);
}, []);
```

**New approach:**

```tsx
import { useMobile } from "@/hooks/use-mobile";

const isMobile = useMobile();
```

### Step 5: For backward compatibility, use the ChatContextAdapter

If you need to support components that still use the old ChatContext:

```tsx
import { ViewStateProvider } from "@/components/Chat/context/ViewStateContext";
import { ChatContextAdapter } from "@/components/Chat/context/NewChatContext";

export default function MyLayout({ children }) {
  return (
    <ViewStateProvider>
      <ChatContextAdapter>{children}</ChatContextAdapter>
    </ViewStateProvider>
  );
}
```

## Common Migration Patterns

### Converting Boolean Flags to View States

| Old Boolean Flag   | New View State      |
| ------------------ | ------------------- |
| showChatDetails    | "CHAT_DETAILS"      |
| showNewChatPage    | "NEW_CHAT"          |
| selectedUserDetail | "USER_DETAILS"      |
| (none/default)     | "CHAT_LIST"         |
| chatId present     | "CHAT_CONVERSATION" |

### Navigation Logic

**Old approach:**

```tsx
const handleBackClick = () => {
  if (selectedUserDetail) {
    setSelectedUserDetail(null);
  } else {
    setShowChatDetails(false);
  }
};
```

**New approach:**

```tsx
const handleBackClick = () => {
  // Just use the built-in goBack function
  goBack();
};
```

## Testing Your Migration

After migrating a component, test these scenarios:

1. Navigation between different views
2. Mobile vs desktop behavior
3. Animation transitions
4. Back navigation
5. Deep linking (starting from a specific URL)

## Troubleshooting

If components aren't rendering correctly after migration:

1. Check that you're using the correct view state names
2. Verify that you've wrapped your app with ViewStateProvider
3. Check for missing dependencies in useEffect hooks
4. Verify that animations are using the correct variant names
