import { generateClient } from "@aws-amplify/api";
import { AI_ASSISTANT_ID } from "../../../constants/chat";
import { Context } from "../interfaces/QuestionnaireContext";
import { QuestionnaireStrategy } from "../interfaces/QuestionnaireStrategy";
import { AiService } from "../services/AiService";
import { EventManager } from "../services/EventManager";
import { StreamHandler } from "../services/StreamHandler";
import { formatChatHistory } from "../utils/chat-utils";
import type { Schema } from "../../data/resource";

const client = generateClient<Schema>();

export class HealthQuestionnaireStrategy implements QuestionnaireStrategy {
  private aiService: AiService;
  private streamHandler: StreamHandler;

  constructor(aiService: AiService) {
    this.aiService = aiService;
    this.streamHandler = new StreamHandler(aiService);
  }

  async initialize(context: Context): Promise<void> {
    if (context.metadata.isQuestionnaireComplete) {
      console.log(
        "[HealthQuestionnaireStrategy] Questionnaire is complete - skipping question load"
      );
      context.questions = [];
      return;
    }

    try {
      // Ensure this is a health questionnaire (ID 1)
      const questionnaireId = context.metadata.questionnaireId || "1";
      if (questionnaireId !== "1") {
        console.log(
          `[HealthQuestionnaireStrategy] This strategy is for health questionnaires only (ID 1), received ${questionnaireId}`
        );
        context.questions = [];
        return;
      }

      // Set questionnaireId to ensure proper tracking
      context.metadata.questionnaireId = "1";

      console.log(
        `[HealthQuestionnaireStrategy] Loading questions for questionnaire ID: ${questionnaireId}`
      );

      const { data: questionnaireQuestions } =
        await client.models.QuestionnaireQuestion.listQuestionnaireQuestionsBySequence(
          { questionnaireId },
          { sortDirection: "ASC" }
        );

      if (!questionnaireQuestions || questionnaireQuestions.length === 0) {
        console.error(
          "[HealthQuestionnaireStrategy] No questionnaire questions found"
        );
        return;
      }

      console.log(
        `[HealthQuestionnaireStrategy] Found ${questionnaireQuestions.length} questionnaire questions`
      );

      const questions = [];
      for (const item of questionnaireQuestions) {
        const { data: healthQuestion } = await client.models.HealthQuestion.get(
          {
            id: item.healthQuestionId,
          }
        );

        if (healthQuestion && healthQuestion.prompt) {
          questions.push({ prompt: healthQuestion.prompt });
        }
      }

      console.log(
        `[HealthQuestionnaireStrategy] Successfully loaded ${questions.length} health questions`
      );
      context.questions = questions;

      context.metadata.currentQuestionIndex =
        context.metadata.currentQuestionIndex || 0;

      if (
        context.metadata.currentQuestionIndex >= context.questions.length &&
        context.questions.length > 0
      ) {
        context.metadata.isQuestionnaireComplete = true;
      }
    } catch (error) {
      console.error(
        "[HealthQuestionnaireStrategy] Error loading questions:",
        error
      );
      throw error;
    }
  }

  async handleResponse(
    userResponse: string,
    context: Context,
    eventManager: EventManager
  ): Promise<{
    nextQuestion: string | null;
    isComplete: boolean;
    messageId?: string;
    chatMessage?: any;
  }> {
    const shouldCompleteEarly = await this.checkEarlyCompletion(
      userResponse,
      context
    );
    if (shouldCompleteEarly) {
      console.log(
        "[HealthQuestionnaireStrategy] Questionnaire completed early due to sufficient information"
      );
      return {
        nextQuestion: null,
        isComplete: true,
      };
    }

    const currentQuestionIndex = context.metadata.currentQuestionIndex || 0;

    if (
      currentQuestionIndex >= context.questions.length ||
      context.metadata.isQuestionnaireComplete
    ) {
      return {
        nextQuestion: null,
        isComplete: true,
      };
    }

    const nextQuestion = context.questions[currentQuestionIndex];
    if (!nextQuestion) {
      await eventManager.sendError(
        "Sorry, there was a problem with the questionnaire. Please try again."
      );
      throw new Error(`No question found at index ${currentQuestionIndex}`);
    }

    console.log(
      `[HealthQuestionnaireStrategy] Processing question #${currentQuestionIndex + 1}: ${nextQuestion.prompt}`
    );

    const prompt = this.formatQuestionPrompt(nextQuestion.prompt);
    const formattedChatHistory = formatChatHistory(context.chatHistory);
    if (!context.metadata.chatId) {
      console.error(
        "[HealthQuestionnaireStrategy] No chatId found in context metadata"
      );
      throw new Error("No chatId found in context metadata");
    }

    try {
      const result = await this.streamHandler.processStream({
        prompt,
        formattedChatHistory,
        chatId: context.metadata.chatId,
        eventManager,
        onSuccess: async (messageId, chatMessage) => {
          console.log(
            `[HealthQuestionnaireStrategy] Question sent with ID: ${messageId}`
          );
          return {
            nextQuestion: prompt,
            isComplete: false,
            messageId,
            chatMessage,
          };
        },
      });

      context.metadata.currentQuestionIndex = currentQuestionIndex + 1;

      if (context.metadata.currentQuestionIndex >= context.questions.length) {
        console.log(
          "[HealthQuestionnaireStrategy] That was the final question"
        );
        context.metadata.isQuestionnaireComplete = true;
      }

      return result;
    } catch (error) {
      console.error(
        "[HealthQuestionnaireStrategy] Error processing question:",
        error
      );
      throw error;
    }
  }

  async checkEarlyCompletion(
    userResponse: string,
    context: Context
  ): Promise<boolean> {
    try {
      if (!context.chatHistory || context.chatHistory.length < 4) {
        return false;
      }

      const userMessages = context.chatHistory
        .filter((msg) => msg && msg.userId !== AI_ASSISTANT_ID)
        .map((msg) => msg.message || "")
        .filter((message) => message.trim().length > 0);

      if (userMessages.length < 2) {
        return false;
      }

      const relevantUserResponses = userMessages.slice(-4).join("\n\n");

      const analysisPrompt = `
        You are a medical assistant analyzing patient responses. Determine if we have enough information for a preliminary diagnosis.
        
        Recent patient responses:
        "${relevantUserResponses}"
        "${userResponse || ""}"
        
        Respond with ONLY ONE WORD:
        - "SUFFICIENT" if enough information is present for a preliminary assessment
        - "INSUFFICIENT" if more data is needed
        
        Do NOT include any other text or punctuation.
      `;

      const response = await this.aiService.getChatCompletion(
        analysisPrompt,
        formatChatHistory(context.chatHistory)
      );

      const cleanedResponse = (response || "").trim().toUpperCase();
      console.log(
        `[HealthQuestionnaireStrategy] AI analysis for early completion: "${cleanedResponse}"`
      );

      return cleanedResponse === "SUFFICIENT";
    } catch (error) {
      console.error(
        "[HealthQuestionnaireStrategy] Error checking early completion:",
        error
      );
      return false;
    }
  }

  private formatQuestionPrompt(questionText: string): string {
    return `You are a medical assistant conducting a patient interview. Your goal is to gather information for diagnosis in a structured way.

Please rephrase the following medical question in a friendly, conversational manner:

Question concept: "${questionText}"

Your response must:
1. Sound natural, as if a doctor is speaking directly to the patient
2. Be phrased as a clear question ending with a question mark
3. Be concise (maximum 2-3 sentences)
4. Not contain medical jargon unless necessary
5. Not include explanatory text or additional comments

Format your response as a direct question only, with no other text or context.`;
  }

  async onQuestionnaireComplete(
    chatId: string,
    context: Context,
    prompt: string,
    eventManager: EventManager
  ): Promise<{ enhancedPrompt: string; showSpecialUI?: boolean }> {
    console.log(
      "[HealthQuestionnaireStrategy] Generating diagnosis after questionnaire completion"
    );

    const enhancedPrompt = `You are a medical assistant providing a diagnosis summary.
      
Your task is to analyze the patient's symptoms and provide a clear diagnostic assessment.

Important instructions:
1. DO NOT ask any follow-up questions in your response
2. Focus exclusively on providing your diagnostic assessment
3. Explain possible conditions that match the symptoms
4. Present your findings in a professional but accessible manner
5. If appropriate, suggest general next steps (but without asking questions)

${prompt}`;

    return {
      enhancedPrompt,
      showSpecialUI: true, // This indicates to show map/diagnosis UI
    };
  }
}
