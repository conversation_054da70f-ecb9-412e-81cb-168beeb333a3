"use client"

import { useState, useEffect } from "react"
import { fetchAuthSession } from "@aws-amplify/auth"
import { useParams } from "next/navigation"

import Moment from "@/components/discovery/Moment"

export default function GroupPage() {
  const params = useParams()
  const groupId = params?.id
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(null)

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession()
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email)
          setCurrentUser({ email })
        } else {
          setCurrentUser({ email: "<EMAIL>" })
        }
      } catch (error) {
        setCurrentUser({ email: "<EMAIL>" })
      }
    }
    fetchUser()
  }, [])


  const moments = [
    {
      id: "1",
      userName: "Group name",
      groupTitle: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      groupText: "Corem ipsum dolor sit amet, consectetur adipiscing elit. Corem ipsum dolor sit amet.",
      avatar: "/groupavatar.svg",
      text: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      content: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",  // Add content
      image: null,
      likes: 1,
      comments: 1,
      timeAgo: "2 mins ago",
    },
    {
      id: "2",
      userName: "Group name",
      groupTitle: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      groupText: "Corem ipsum dolor sit amet, consectetur adipiscing elit. Corem ipsum dolor sit amet.",
      avatar: "/groupavatar.svg",
      text: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      content: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",  // Add content
      image: null,
      likes: 1,
      comments: 1,
      timeAgo: "2 mins ago",
    },
    {
      id: "3",
      userName: "Group name",
      groupTitle: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      groupText: "Corem ipsum dolor sit amet, consectetur adipiscing elit. Corem ipsum dolor sit amet.",
      avatar: "/groupavatar.svg",
      text: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      content: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",  
      image: null,
      likes: 1,
      comments: 1,
      timeAgo: "2 mins ago",
    },
    {
      id: "4",
      userName: "Group name",
      groupTitle: "Group Title",
      groupText: "Group description text here",
      avatar: "/groupavatar.svg",
      text: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
      content: "Group description text here",  
      image: "/Frame2.png",
      likes: 1,
      comments: 1,
      timeAgo: "2 mins ago",
    },
  ]

  const handleCommentAdd = async (postId: string) => {
    const updatedMoments = moments.map(moment => {
      if (moment.id === postId) {
        return {
          ...moment,
          comments: moment.comments + 1
        };
      }
      return moment;
    });

  };

  return (
    <div className="min-h-screen bg-white flex ">
      <div className="flex-1 relative">
        
       

        <div className="flex flex-col md:flex-row py-2 md:ml-[96px] lg:ml-[128px] md:mt-0 pb-20" >
          <div className="flex-1 mt-8">
            <div className="space-y-6">
              {moments.map((moment) => (
                <Moment 
                  key={moment.id} 
                  moment={moment}
                  onCommentAdd={handleCommentAdd} 
                />
              ))}
            </div>
          </div>

          <div className="hidden md:block md:ml-6 md:w-[300px] lg:w-[350px]">
            <div className="bg-white rounded-lg p-2 mt-6">
              </div>
          </div>
        </div>
      </div>
    </div>
  )
}
