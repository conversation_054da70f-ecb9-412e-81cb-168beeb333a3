import { AI_ASSISTANT_ID } from "../../../constants/chat";
import type { Message } from "../types";

export function formatChatHistory(chatHistory: any): Message[] {
  const formattedHistory: Message[] = [
    {
      id: "system-message",
      role: "system" as const,
      content:
        "You are a rare disease doctor conducting a patient interview, trying to diagnose whether this patient has ALS or SAMD9/SAMD9L. \n Please use natural and friendly conversations to finish this interview, and you can refer to the following medical questions. Make it sound natural as if a doctor is asking the patient: \nWe shouldn't give the questionnaire directly to our customers and not give a treatment plan at any circumstances because it's against regulations. \nYou should give a conclusion (whether you think they have what disease or you are not sure after max 10 conversations, instead of an infinite loop)",
    },
  ];

  // Handle both formats: when chatHistory is the array itself or when it has a data property
  const messages = Array.isArray(chatHistory)
    ? chatHistory
    : chatHistory?.data || [];

  if (messages.length > 0) {
    messages.forEach((message: any) => {
      // Check both AI_ASSISTANT_ID and the string "AI_ASSISTANT"
      const isAssistant =
        message.userId === AI_ASSISTANT_ID || message.userId === "AI_ASSISTANT";

      // Determine role based on userId and messageType
      let role: "system" | "assistant" | "user" = isAssistant
        ? "assistant"
        : "user";

      // Override role for system messages
      if (message.messageType === "SYSTEM") {
        role = "system";
      }

      formattedHistory.push({
        id: message.id,
        role,
        content: message.message,
      });
    });
  }

  return formattedHistory;
}

export function createRemainingTimeGetter(context?: any): () => number {
  const startTime = Date.now();

  return () => {
    if (context && typeof context.getRemainingTimeInMillis === "function") {
      return context.getRemainingTimeInMillis();
    }

    return 15 * 60 * 1000 - (Date.now() - startTime);
  };
}
