import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { communityService } from '@/services/communityService';
import { useUser } from '@/hooks/useUser';

type Community = {
  id: string;
  name: string;
  description: string;
  members?: number;
  membersCount?: number;
  avatar?: string;
  icon?: string;
};

type DiscoverySidebarProps = {
  communities: Community[];
};

const DiscoverySidebar: React.FC<DiscoverySidebarProps> = ({ communities }) => {
  const router = useRouter();
  const { currentUser } = useUser();
  const [userCommunities, setUserCommunities] = useState<Community[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState(""); // додано стан для пошуку
  
  useEffect(() => {
    const fetchUserCommunities = async () => {
      if (currentUser?.email) {
        try {
          // Using email as identifier since that's what useUser provides
          const result = await communityService.getUserCommunities(currentUser.email);
          // Map the API response to match our Community type
          if (result?.data && Array.isArray(result.data)) {
            const mappedCommunities = result.data.map((item: any) => ({
              id: item.id || '',
              name: item.name || '',
              description: item.description || '',
              membersCount: typeof item.membersCount === 'number' ? item.membersCount : 
                             typeof item.members === 'number' ? item.members : undefined,
              avatar: item.avatar || item.icon || undefined,
              icon: item.icon || undefined
            }));
            setUserCommunities(mappedCommunities);
          } else {
            setUserCommunities([]);
          }
        } catch (error) {
          console.error("Error fetching user communities:", error);
          setUserCommunities([]);
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };
    
    fetchUserCommunities();
  }, [currentUser]);
  
  const handleCreateCommunity = () => {
    router.push('/comunity/create');
  };

  const navigateToCommunity = (communityId: string) => {
    router.push(`/comunity/${communityId}`);
  };

  // Фільтрація спільнот за пошуковим запитом
  const filteredUserCommunities = userCommunities.filter(
    (community) =>
      community.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="hidden md:block md:w-[220px] lg:w-[260px] xl:w-[320px] 2xl:w-[360px] bg-white md:p-2 lg:p-3 xl:p-4 2xl:p-5 border-r border-gray-200 flex-shrink-0">
      {/* Search input with + button */}
      <div className="flex items-center mb-4 md:mb-5 lg:mb-6 xl:mb-7">
        <div className="relative flex-grow bg-gray-100 rounded-full">
          <div className="absolute inset-y-0 left-0 flex items-center pl-2 md:pl-3 lg:pl-4 pointer-events-none">
           <Image
              src="/graysearch.svg"
              alt="Search icon"
              width={24}
              height={24}
              className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 text-gray-400"
            />
          </div>
          <input 
            type="text" 
            placeholder="Search" 
            className="w-full h-[36px] md:h-[40px] lg:h-[45px] xl:h-[50px] pl-8 md:pl-10 lg:pl-12 pr-2 md:pr-3 bg-gray-100 rounded-3xl border-none focus:outline-none focus:ring-0 text-xs md:text-sm lg:text-base"
            value={searchQuery}
            onChange={e => setSearchQuery(e.target.value)} // додано обробник
          />
        </div>
        <button onClick={handleCreateCommunity} className="ml-1 md:ml-2 bg-white text-gray-400 h-[28px] w-[28px] md:h-[32px] md:w-[32px] lg:h-[38px] lg:w-[38px] flex items-center justify-center">
         <Image
            src="/plus.svg"
            alt="Plus icon"
            width={24}
            height={24}
            className="w-5 h-5 md:w-5 md:h-5 lg:w-6 lg:h-6 text-gray-400"
          />
        </button>
      </div>
      
      {/* My communities section */}
      <div className="mb-6">
        <h3 className="font-semibold text-sm text-gray-500 mb-3">MY COMMUNITIES</h3>
        <div className="space-y-4">
          {filteredUserCommunities.length > 0 ? (
            filteredUserCommunities.map((community) => (
              <div 
                key={community.id} 
                className="flex items-center cursor-pointer"
                onClick={() => navigateToCommunity(community.id)}
              >
                <img 
                  src={community.avatar || community.icon || "/groupavatar.svg"} 
                  alt={community.name} 
                  className="w-9 h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full mr-3 md:mr-3 lg:mr-4" 
                />
                <div className="max-w-[75%]">
                  <h4 className="font-medium text-base md:text-base lg:text-lg truncate">{community.name}</h4>
                </div>
              </div>
            ))
          ) : !isLoading && (
            // Display placeholder "GroupName" items when user has no communities
            Array(5).fill(0).map((_, index) => (
              <div key={index} className="flex items-center cursor-pointer">
                <img 
                  src="/groupavatar.svg" 
                  alt="Group avatar" 
                  className="w-9 h-9 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full mr-3 md:mr-3 lg:mr-4" 
                />
                <div className="max-w-[75%]">
                  <h4 className="font-medium text-base md:text-base lg:text-lg truncate">GroupName</h4>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      
      {/* Show placeholders if loading or no communities */}
      {isLoading && userCommunities.length === 0 && communities.length === 0 && (
        <div className="space-y-4 animate-pulse">
          {Array(5).fill(0).map((_, index) => (
            <div key={index} className="flex items-center">
              <div className="w-10 h-10 bg-gray-200 rounded-full mr-3"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DiscoverySidebar;
