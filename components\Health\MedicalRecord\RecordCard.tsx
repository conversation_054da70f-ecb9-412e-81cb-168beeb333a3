import { motion } from "framer-motion";
import { ChevronRightIcon } from "lucide-react";
import Image from "next/image";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";
import { formatDate } from "./utils";

interface RecordCardProps {
  record: MedicalRecord;
  isSelected: boolean;
  index: number;
  onClick: (record: MedicalRecord) => void;
  isModal?: boolean;
}

const RecordCard: React.FC<RecordCardProps> = ({
  record,
  isSelected,
  index,
  onClick,
  isModal = false,
}) => {
  const cardBaseStyles = `
    ml-6 rounded-2xl border border-gray-200 shadow-sm p-4 mt-2 
    ${record.isClickable ? "cursor-pointer hover:shadow-md transition-shadow" : ""} 
    ${isSelected ? "ring-2 ring-blue-500 bg-[#F5F5F5]" : "bg-white"}
  `;

  return (
    <motion.div
      className="relative"
      initial={{ opacity: 0, y: 20 }}
      animate={{
        opacity: isModal ? (isSelected ? 1 : 0.7) : 1,
        y: 0,
      }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
    >
      {/* Timeline line */}
      <div
        className="absolute top-5 left-1.5 w-[1px] h-full bg-[#E5E5E5]"
        style={{ transform: "translateX(-50%)" }}
      ></div>

      {/* Timeline dot */}
      <motion.div
        className="absolute top-1.5 left-0 w-3 h-3 bg-white rounded-full border-2 border-blue-100 z-10"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.2, delay: index * 0.1 + 0.1 }}
      ></motion.div>

      {/* Date badge */}
      <motion.div
        className="ml-6 text-sm text-blue-500 bg-[#E9F0FF] rounded-2xl px-4 inline-block"
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 + 0.2 }}
      >
        {formatDate(record.date)}
      </motion.div>

      {/* Record card */}
      <motion.div
        className={cardBaseStyles}
        onClick={() => onClick(record)}
        whileHover={
          record.isClickable
            ? {
                scale: 1.02,
                boxShadow: "0px 4px 15px rgba(0, 0, 0, 0.08)",
              }
            : {}
        }
        whileTap={record.isClickable ? { scale: 0.98 } : {}}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay: index * 0.1 + 0.3 }}
      >
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-medium text-lg">{record.type}</h3>
          {(record.isClickable || isModal) && (
            <ChevronRightIcon className="h-6 w-6 text-gray-400" />
          )}
        </div>

        {record.location && record.location.trim() && (
          <div className="flex items-center text-gray-500 text-base mb-2">
            <div className="mr-2">
              <Image
                src="/medical-record/map-marker.svg"
                alt="Location"
                width={16}
                height={16}
                className="w-4 h-4"
              />
            </div>
            {record.location}
          </div>
        )}

        {record.doctor && record.doctor.trim() && (
          <div className="flex items-center text-gray-500 text-base mb-2">
            <div className="mr-2">
              <Image
                src="/menu-deepg.svg"
                alt="Doctor"
                width={16}
                height={16}
                className="w-4 h-4"
              />
            </div>
            {record.doctor}
          </div>
        )}

        <div className="flex items-start text-gray-600 text-sm bg-gray-100 rounded-2xl p-2">
          <div className="w-5 flex justify-center mr-1 mt-0.5">
            <Image
              src="/medical-record/note.svg"
              alt="Description"
              width={16}
              height={16}
              className="w-4 h-4"
            />
          </div>
          {record.description}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default RecordCard;
