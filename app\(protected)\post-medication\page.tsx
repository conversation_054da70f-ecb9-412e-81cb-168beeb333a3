'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';
import MedicationCard from '@/components/Health/MedicationCard';

export default function MedicationReaction() {
  const router = useRouter();
  const [selectedMedicationId, setSelectedMedicationId] = useState<string | null>(null);

  // Захардкожені дані для прикладу
  const medications = [
    {
      medicationId: '1',
      name: 'Hydroxychloroquine',
      dateRange: 'March 14, 2025 - April 15, 2025',
      dosage: '500',
      dosageUnit: 'mg',
      frequency: '2',
      frequencyUnit: '/day',
      route: 'Oral',
      administrator: 'Self',
      physician: 'Dr. <PERSON>',
    },
    {
      medicationId: '2',
      name: 'Hydroxychloroquine',
      dateRange: 'March 14, 2025 - April 15, 2025',
      dosage: '500',
      dosageUnit: 'mg',
      frequency: '2',
      frequencyUnit: '/day',
      route: 'Oral',
      administrator: 'Self',
      physician: 'Dr. <PERSON>',
    },
  ];

  // Заглушки для меню (edit/delete)
  const handleOpenMenu = (id: string) => setSelectedMedicationId(id);
  const handleDeleteClick = (id: string) => setSelectedMedicationId(null);
  const menuVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 },
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF] relative">
      {/* Header with back button and title */}
      <div className="flex items-center justify-between pt-[51px] pl-6 pr-6 py-4 overflow-hidden">
        <div className="flex items-center">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <div className="ml-4 flex items-center">
            <h1 className="text-[16px] font-medium mr-2">Post Medication </h1>
          </div>
        </div>
      </div>

      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-4">
        {/* Related Prescription */}
        <div className="bg-white rounded-3xl">
          <div className="flex items-center justify-between mb-4">
            {/* ...existing code... */}
          </div>
          {/* Medication Cards */}
          {medications.map((med) => (
            <MedicationCard
              key={med.medicationId}
              medicationId={med.medicationId}
              name={med.name}
              dateRange={med.dateRange}
              dosage={med.dosage}
              dosageUnit={med.dosageUnit}
              frequency={med.frequency}
              frequencyUnit={med.frequencyUnit}
              route={med.route}
              administrator={med.administrator}
              physician={med.physician}
              selectedMedicationId={selectedMedicationId}
              onOpenMenu={handleOpenMenu}
              onDeleteClick={handleDeleteClick}
              menuVariants={menuVariants}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
