"use client";

import { useEffect, useState } from "react";

export function useWindowSize() {
  // Initialize with desktop view (false means desktop)
  const [isMobile, setIsMobile] = useState<boolean>(false);

  useEffect(() => {
    // Check if window is defined (client-side)
    if (typeof window === "undefined") return;

    function handleResize() {
      // Using Tailwind's md breakpoint (768px)
      setIsMobile(window.innerWidth < 768);
    }

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Call handler right away so state gets updated with initial window size
    handleResize();

    // Remove event listener on cleanup
    return () => window.removeEventListener("resize", handleResize);
  }, []); 

  // Return the inverse since we want desktop to be the default view
  return !isMobile;
}