import Image from "next/image";
import { motion } from "framer-motion";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";
import OutpatientContent from "@/app/(protected)/medical-record/components/OutpatientContent";
import SymptomContent from "@/app/(protected)/medical-record/components/SymptomContent";
import MedicationReactionContent from "@/app/(protected)/medical-record/components/MedicationReactionContent";
import MedicationContent from "@/app/(protected)/medical-record/components/MedicationContent";

interface RecordDetailsProps {
  record: MedicalRecord;
}

// Create and export EmptyState as a separate component
export const EmptyState = () => (
  <motion.div
    key="empty-state"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    transition={{ duration: 0.3 }}
    className="flex items-center justify-center h-full"
  >
    <div className="text-center text-gray-500">
      <svg
        className="mx-auto h-12 w-12 opacity-50"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
        />
      </svg>
      <p className="mt-2">
        Select a record from the timeline to view details
      </p>
    </div>
  </motion.div>
);

const RecordDetails: React.FC<RecordDetailsProps> = ({ record }) => {
  if (!record) return <EmptyState />;

  let resourceType = null;
  if (
    record.rawData &&
    typeof record.rawData === "object" &&
    "resourceType" in record.rawData
  ) {
    resourceType = record.rawData.resourceType;
  }

  let relatedResources = [];

  if (
    record.rawData &&
    typeof record.rawData === "object" &&
    "entry" in record.rawData &&
    Array.isArray(record.rawData.entry)
  ) {
    relatedResources = record.rawData.entry
      .filter(
        (entry) => entry && typeof entry === "object" && "resource" in entry
      )
      .map((entry) => entry.resource);
  }
  else if (
    record.relatedResources &&
    Array.isArray(record.relatedResources)
  ) {
    relatedResources = record.relatedResources;
  }

  const mappedType = resourceType
    ? resourceType === "Condition"
      ? "Symptom"
      : resourceType === "AllergyIntolerance"
        ? "Medication Reaction"
        : resourceType === "MedicationRequest" ||
            resourceType === "Medication"
          ? "Medication"
          : resourceType === "Observation" &&
              record.rawData &&
              typeof record.rawData === 'object' &&
              'category' in record.rawData &&
              Array.isArray(record.rawData.category) &&
              record.rawData.category[0] &&
              'coding' in record.rawData.category[0] &&
              Array.isArray(record.rawData.category[0].coding) &&
              record.rawData.category[0].coding[0] &&
              'code' in record.rawData.category[0].coding[0] &&
              record.rawData.category[0].coding[0].code === "vital-signs"
            ? "Vital Signs"
            : resourceType === "Encounter" || resourceType === "Observation"
              ? "Outpatient Visit"
              : record.type
    : record.type;

  const enhancedRecord = {
    ...record,
    mappedType,
    relatedResources:
      relatedResources.length > 0 ? relatedResources : undefined,
  };

  switch (mappedType || record.type) {
    case "Outpatient Visit":
    case "Vital Signs":
      return <OutpatientContent record={enhancedRecord} />;
    case "Symptom":
      return <SymptomContent record={enhancedRecord} />;
    case "Medication Reaction":
      return <MedicationReactionContent record={enhancedRecord} />;
    case "Medication":
      return <MedicationContent record={enhancedRecord} />;
    default:
      return (
        <div className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm">
          <h3 className="font-medium mb-3">Record Details</h3>
          <p className="text-sm text-gray-500 mb-4">
            This is a {record.type} record from {record.date}.
          </p>
          <div className="bg-gray-100 p-4 rounded-lg text-sm">
            <p className="font-medium mb-2">
              {record.description || "No description available"}
            </p>

            {record.location && (
              <div className="mt-2 flex items-center text-gray-600">
                <Image
                  src="/medical-record/map-marker.svg"
                  alt="Location"
                  width={16}
                  height={16}
                  className="w-4 h-4 mr-2"
                />
                {record.location}
              </div>
            )}

            {record.doctor && (
              <div className="mt-2 flex items-center text-gray-600">
                <Image
                  src="/menu-deepg.svg"
                  alt="Doctor"
                  width={16}
                  height={16}
                  className="w-4 h-4 mr-2"
                />
                {record.doctor}
              </div>
            )}

            {record.rawData && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <details>
                  <summary className="cursor-pointer font-medium">
                    Raw FHIR Data (Dev Only)
                  </summary>
                  <pre className="overflow-auto mt-2 max-h-96 text-xs">
                    {JSON.stringify(record.rawData, null, 2)}
                  </pre>
                </details>
              </div>
            )}
          </div>
        </div>
      );
  }
};

export default RecordDetails;
