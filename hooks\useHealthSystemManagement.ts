import { useState, useEffect } from "react";
import { OneUpHealthService } from "@/lib/services/oneUpHealthService";
import { useUser } from "@/hooks/useUsers";

export interface ConnectedSystem {
  id: string;
  name: string;
}

export interface HealthSystem {
  id: string;
  name: string;
  isConnected: boolean;
}

const defaultAvailableSystems = [
  { id: "epic", name: "Epic Systems", isConnected: false },
  { id: "cerner", name: "<PERSON><PERSON>", isConnected: false },
  { id: "allscripts", name: "Allscripts", isConnected: false },
  { id: "athena", name: "Athenahealth", isConnected: false },
  { id: "nextgen", name: "NextGen Healthcare", isConnected: false },
  { id: "meditech", name: "MEDITECH", isConnected: false },
  { id: "eclinicalworks", name: "eClinicalWorks", isConnected: false },
  { id: "cpsi", name: "<PERSON><PERSON>", isConnected: false },
  { id: "mckesson", name: "Mc<PERSON><PERSON><PERSON>", isConnected: false },
];

export const useHealthSystemManagement = () => {
  const { currentUser } = useUser();
  const userId = currentUser?.userId;

  const [connectedSystems, setConnectedSystems] = useState<ConnectedSystem[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(true);
  const [disconnectingSystem, setDisconnectingSystem] = useState<string | null>(
    null
  );
  const [connectingSystem, setConnectingSystem] = useState<string | null>(null);
  const [availableSystems, setAvailableSystems] = useState<HealthSystem[]>(
    defaultAvailableSystems
  );
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (userId) {
      fetchConnectedSystems();
    } else {
      setConnectedSystems([]);
      setAvailableSystems(defaultAvailableSystems);
      setIsLoading(false);
    }
  }, [userId]);

  const fetchConnectedSystems = async () => {
    if (!userId) return;

    try {
      const service = new OneUpHealthService({ userId });
      await service.initialize();

      const systems = await service.getConnectedHealthSystems();
      setConnectedSystems(systems);

      updateAvailableSystemsConnectionStatus(systems);
    } catch (error) {
      console.error("Failed to fetch connected systems:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateAvailableSystemsConnectionStatus = (
    systems: ConnectedSystem[]
  ) => {
    setAvailableSystems((prev) =>
      prev.map((system) => ({
        ...system,
        isConnected: systems.some((cs) =>
          cs.name.toLowerCase().includes(system.id.toLowerCase())
        ),
      }))
    );
  };

  const handleDisconnect = async (systemId: string) => {
    if (!userId) return;

    try {
      setDisconnectingSystem(systemId);

      const response = await fetch(
        `/api/health-systems/${systemId}/disconnect`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ userId }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to disconnect: ${response.statusText}`);
      }

      setConnectedSystems((prev) =>
        prev.filter((system) => system.id !== systemId)
      );
      updateAvailableSystemsConnectionStatus(
        connectedSystems.filter((system) => system.id !== systemId)
      );
    } catch (error) {
      console.error(`Failed to disconnect system ${systemId}:`, error);
      alert("Failed to disconnect system. Please try again.");
    } finally {
      setDisconnectingSystem(null);
    }
  };

  const handleConnect = async (systemId: string) => {
    if (!userId) return;

    try {
      setConnectingSystem(systemId);

      await new Promise((resolve) => setTimeout(resolve, 1000));

      await fetchConnectedSystems();
    } catch (error) {
      console.error(`Failed to connect system ${systemId}:`, error);
      alert("Failed to connect system. Please try again.");
    } finally {
      setConnectingSystem(null);
    }
  };

  const filteredSystems = availableSystems.filter((system) =>
    system.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return {
    connectedSystems,
    availableSystems: filteredSystems,
    isLoading,
    disconnectingSystem,
    connectingSystem,
    searchQuery,
    setSearchQuery,
    handleConnect,
    handleDisconnect,
    fetchConnectedSystems,
    userId, // Export userId for components that might need it
  };
};
