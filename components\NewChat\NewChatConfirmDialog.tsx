"use client";

import { useState } from "react";
import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useIsMobile } from "@/hooks/use-mobile";

export function NewChatDialog() {
  const [open, setOpen] = useState(false);
  const [groupName, setGroupName] = useState("");
  const isMobile = useIsMobile();

  const handleCreate = () => {
    // Handle create logic here
    console.log("Creating group:", groupName);
    setOpen(false);
  };

  return (
    // Only show the dialog on desktop devices (md: breakpoint and above)
    <div className="hidden md:block">
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button>Open New Chat</Button>
        </DialogTrigger>
        <DialogContent className="p-0 gap-0 max-w-md">
          <div className="flex items-center justify-between p-4 border-[#e5e5e5] flex-shrink-0 border-b">
            <h2 className="text-[20px] leading-[100%] tracking-[0%] font-normal text-[#171717]">
              New Chat
            </h2>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={() => setOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>

          <div className="p-6">
            <Input
              placeholder="Group Name"
              className="bg-muted/50 border-0"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
            />
          </div>

          <div className="p-4 flex w-full gap-4 border-t border-[#e5e5e5] flex-shrink-0">
            <button
              type="button"
              className="rounded-2xl flex-1 px-6 py-1.5 bg-[#E5E5E5] text-[#171717] font-medium text-base hover:bg-[#e5e5e5] transition-colors"
              onClick={() => setOpen(false)}
              style={{
                boxShadow: "0px 1px 2px rgba(0, 0, 0, 0.05)",
                height: "40px",
              }}
            >
              Back
            </button>
            <button
              type="button"
              className="rounded-2xl px-6 py-1.5 flex-1 bg-[#4F86FF] text-white font-medium text-base hover:bg-[#3A75FF] transition-colors"
              onClick={handleCreate}
              style={{
                boxShadow: "0px 1px 2px rgba(0, 0, 0, 0.05)",
                height: "40px",
              }}
            >
              Create
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
