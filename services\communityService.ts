import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>();

// Cache implementation
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes
const communityCache = new Map<string, CacheItem<any>>();
const pendingRequests = new Map<string, Promise<any>>();

const getCachedData = <T>(key: string): T | null => {
  const item = communityCache.get(key);
  if (!item) {
    console.log(`Cache MISS for key: ${key}`);
    return null;
  }
  
  const now = Date.now();
  if (now - item.timestamp > CACHE_EXPIRATION) {
    console.log(`Cache EXPIRED for key: ${key}`);
    communityCache.delete(key);
    return null;
  }
  
  console.log(`Cache HIT for key: ${key}`);
  return item.data as T;
};

const setCachedData = <T>(key: string, data: T): void => {
  console.log(`Caching data for key: ${key}`);
  communityCache.set(key, {
    data,
    timestamp: Date.now()
  });
  console.log(`Cache size: ${communityCache.size} items`);
};

const invalidateCache = (userId?: string, communityId?: string): void => {
  if (userId && communityId) {
    // Invalidate specific membership status
    const membershipKey = `membershipStatus_${userId}_${communityId}`;
    console.log(`Invalidating membership cache for key: ${membershipKey}`);
    communityCache.delete(membershipKey);
    
    // Also invalidate user communities when specific membership changes
    const userCommunitiesKey = `userCommunities_${userId}`;
    console.log(`Invalidating user communities cache for key: ${userCommunitiesKey}`);
    communityCache.delete(userCommunitiesKey);
  } else if (userId) {
    // Invalidate all of this user's cached data
    const userCommunitiesKey = `userCommunities_${userId}`;
    console.log(`Invalidating cache for key: ${userCommunitiesKey}`);
    communityCache.delete(userCommunitiesKey);
    
    // Also clear any membership status cache entries for this user
    for (const key of communityCache.keys()) {
      if (key.startsWith(`membershipStatus_${userId}_`)) {
        console.log(`Invalidating related membership cache: ${key}`);
        communityCache.delete(key);
      }
    }
  } else {
    console.log(`Clearing entire cache`);
    communityCache.clear();
  }
};

export const communityService = {
  async createCommunity(communityData: {
    name: string;
    description: string;
    icon?: string | null;
    banner?: string | null;
    permission: "open" | "request";
    rules: string[];
    createdBy: string;
  }) {
    const { name, description, icon, banner, permission, rules, createdBy } = communityData;
    
    try {
      // Ensure id is set for the community
      const newCommunity = {
        id: crypto.randomUUID(),
        name,
        description,
        icon: icon || undefined,
        banner: banner || undefined,
        permission,
        rules,
        createdBy,
        createdAt: new Date().toISOString(),
        membersCount: 1
      };

      const result = await client.models.Community.create(newCommunity);
      
      console.log("Community created successfully:", result);
      
      // Create an initial membership for the creator as admin
      if (result.data?.id) {
        await client.models.CommunityMember.create({
          id: crypto.randomUUID(),
          communityId: result.data.id,
          userId: createdBy,
          role: "admin",
          joinedAt: new Date().toISOString()
        });
      }
      
      return result;
    } catch (error) {
      console.error("Error in createCommunity:", error);
      throw error;
    }
  },
  
  async getRecommendedCommunities(limit = 5) {
    try {
      const result = await client.models.Community.list({
        limit
      });
      
      return {
        data: {
          listCommunities: {
            items: result.data
          }
        }
      };
    } catch (error) {
      console.error("Error fetching communities:", error);
      throw error;
    }
  },
  
  async joinCommunity(userId: string, communityId: string) {
    try {
      // First, check if user is already a member
      const existingMemberships = await client.models.CommunityMember.list({
        filter: {
          communityId: {
            eq: communityId
          },
          userId: {
            eq: userId
          }
        }
      });
      
      if (existingMemberships.data.length > 0) {
        console.log("User is already a member of this community");
        setCachedData(`membershipStatus_${userId}_${communityId}`, true);
        return existingMemberships.data[0];
      }
      
      // Create new membership
      const membership = await client.models.CommunityMember.create({
        communityId,
        userId,
        role: "member",
        joinedAt: new Date().toISOString()
      });
      
      // Get the current community
      const community = await client.models.Community.get({
        id: communityId
      });
      
      // Update members count (ensure correct increment)
      if (community.data) {
        await client.models.Community.update({
          id: communityId,
          membersCount: (community.data.membersCount || 0) + 1
        });
      }
      
      invalidateCache(userId, communityId);
      setCachedData(`membershipStatus_${userId}_${communityId}`, true);
      this.refreshUserCommunities(userId).catch(err => {
        console.error("Failed to refresh communities after join:", err);
      });
      
      return membership;
    } catch (error) {
      console.error("Error joining community:", error);
      throw error;
    }
  },
  
  async checkMembershipStatus(userId: string, communityId: string): Promise<boolean> {
    const cacheKey = `membershipStatus_${userId}_${communityId}`;
    console.log(`Checking membership status cache for: ${cacheKey}`);
    
    // Check cache first
    const cachedMembership = getCachedData<boolean>(cacheKey);
    if (cachedMembership !== null) {
      console.log(`Using cached membership status for ${userId} in community ${communityId}: ${cachedMembership}`);
      return cachedMembership;
    }
    
    console.log(`No cached membership status, checking API for ${userId} in community ${communityId}`);
    try {
      // Check if user is already a member
      const existingMemberships = await client.models.CommunityMember.list({
        filter: {
          communityId: {
            eq: communityId
          },
          userId: {
            eq: userId
          }
        }
      });
      
      const isMember = existingMemberships.data.length > 0;
      
      // Cache the result
      console.log(`Caching membership status for ${userId} in community ${communityId}: ${isMember}`);
      setCachedData(cacheKey, isMember);
      
      return isMember;
    } catch (error) {
      console.error("Error checking membership status:", error);
      return false;
    }
  },
  
  async getUserCommunities(userEmail: string, forceRefresh = false) {
    const cacheKey = `userCommunities_${userEmail}`;
    
    // Print cache debugging info
    console.log(`Cache stats - Total entries: ${communityCache.size}`);
    console.log(`Looking for cache key: ${cacheKey}`);
    console.log(`Force refresh requested: ${forceRefresh}`);
    
    // If not forcing refresh, check cache first
    if (!forceRefresh) {
      const cachedData = getCachedData(cacheKey);
      if (cachedData) {
        console.log("Returning cached communities data");
        return {
          ...cachedData,
          fromCache: true
        };
      }
    } else {
      // If forcing refresh, invalidate the cache for this user
      console.log("Force refresh - invalidating cache");
      invalidateCache(userEmail);
    }

    // Check if request is already pending
    if (pendingRequests.has(userEmail)) {
      console.log("Request already pending, returning existing promise");
      return pendingRequests.get(userEmail);
    }
    
    console.log("No cache hit or pending request, fetching fresh data");
    const requestPromise = (async () => {
      try {
        console.log("Fetching user memberships from API");
        const memberships = await client.models.CommunityMember.list({
          filter: {
            userId: {
              eq: userEmail
            }
          }
        });
        
        console.log("Found memberships:", memberships.data?.length || 0);
        
        if (!memberships.data || memberships.data.length === 0) {
          const result = { data: [], fromCache: false };
          console.log("No memberships found, caching empty result");
          setCachedData(cacheKey, result);
          return result;
        }
        
        // Extract community IDs
        const communityIds = memberships.data.map(membership => membership.communityId);
        console.log("Community IDs:", communityIds);
        
        // Fetch the complete community information for each membership
        console.log("Fetching detailed community data");
        const communities = await Promise.all(
          communityIds.map(async (communityId) => {
            const community = await client.models.Community.get({
              id: communityId
            });
            return community.data;
          })
        );
        
        console.log("Fetched communities:", communities.length);
        
        // Filter out any null values
        const result = {
          data: communities.filter(Boolean),
          fromCache: false
        };
        
        // Store result in cache
        console.log("Storing communities in cache");
        setCachedData(cacheKey, result);
        
        // Double check if data was cached
        const verifyCached = communityCache.has(cacheKey);
        console.log(`Verified cache contains key ${cacheKey}: ${verifyCached}`);
        
        return result;
      } catch (error) {
        console.error("Error fetching user communities:", error);
        throw error;
      } finally {
        // Remove from pending requests
        pendingRequests.delete(userEmail);
        console.log(`Removed pending request for ${userEmail}`);
      }
    })();

    // Store pending request
    pendingRequests.set(userEmail, requestPromise);
    console.log(`Stored pending request for ${userEmail}`);
    return requestPromise;
  },
  
  // Add convenient method to refresh user communities
  async refreshUserCommunities(userEmail: string) {
    console.log(`Explicitly refreshing communities for ${userEmail}`);
    return await this.getUserCommunities(userEmail, true);
  },
  
  // Export cache control functions for external use
  clearCache: () => invalidateCache(),
  clearUserCache: (userId: string) => invalidateCache(userId),
  clearMembershipCache: (userId: string, communityId: string) => invalidateCache(userId, communityId),
  
  // Add debug utility
  getCacheStatus() {
    return {
      size: communityCache.size,
      keys: Array.from(communityCache.keys()),
      pendingRequests: Array.from(pendingRequests.keys())
    };
  }
};
