"use client"
import { useEffect, useState, useCallback } from "react"
import { fetchAuthSession } from "aws-amplify/auth"
import { MomentProps } from "@/types/moment"
import { CommentWithLikes } from "@/types/comment"
import { LikeWithUser } from "@/types/like"
import { commentService } from "@/services/commentService"
import { likeService } from "@/services/likeService"
import { bookmarkService } from "@/services/bookmarkService"
import MomentHeader from "./MomentHeader"
import MomentContent from "./MomentContent"
import MomentActions from "./MomentActions"
import MomentCommentForm from "./MomentCommentForm"
import Comments from "./Comments"
import LikesPopup from "./LikesPopup"
import { GraphQLResult } from "@aws-amplify/api"
import { generateClient } from "aws-amplify/api"
import { Schema } from "@/amplify/data/resource"

interface PostLikeItem {
  id: string;
  userId: string;
  user?: {
    name: string;
    profilePicture?: string;
  };
}

interface PostLikesResponse {
  listLikesByPost: {
    items: PostLikeItem[];
  };
}

export default function Moment({ moment, onCommentAdd, onBookmarkChange }: MomentProps) {
  const [showComments, setShowComments] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(null)
  const [comments, setComments] = useState<CommentWithLikes[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [initialized, setInitialized] = useState(false)
  const [shouldRefetch, setShouldRefetch] = useState(false)
  const [isReplying, setIsReplying] = useState(false)
  const [isLiked, setIsLiked] = useState(moment.isLiked || false)
  const [likesCount, setLikesCount] = useState(moment.likes)
  const [totalCommentsCount, setTotalCommentsCount] = useState(moment.comments || 0)
  const [showLikesPopup, setShowLikesPopup] = useState(false)
  const [likesList, setLikesList] = useState<LikeWithUser[]>([])
  const client = generateClient<Schema>()

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession()
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email)
          setCurrentUser({ email })
        } else {
          setCurrentUser({ email: "<EMAIL>" })
        }
      } catch (error) {
        setCurrentUser({ email: "<EMAIL>" })
      }
    }
    fetchUser()
  }, [])

  useEffect(() => {
    setIsBookmarked(moment.isSaved || false)
  }, [moment.isSaved])

  // New function to fetch only the comment count
  const fetchCommentCount = useCallback(async () => {
    if (!moment.id) return;
    
    try {
      const result = await commentService.fetchComments(moment.id);
      
      if ('data' in result && result.data?.listCommentsByPost?.items) {
        const comments = result.data.listCommentsByPost.items;
        
        // Calculate total comments count including replies
        let totalCount = comments.length;
        comments.forEach((comment: CommentWithLikes) => {
          if (comment && 
              'replies' in comment && 
              comment.replies && 
              typeof comment.replies === 'object' && 
              'items' in comment.replies && 
              Array.isArray(comment.replies.items)) {
            totalCount += comment.replies.items.length;
          }
        });
        
        setTotalCommentsCount(totalCount);
      }
    } catch (error) {
      console.error("Error fetching comment count:", error);
    }
  }, [moment.id]);

  // Fetch comment count on component mount
  useEffect(() => {
    fetchCommentCount();
  }, [fetchCommentCount]);

  const fetchComments = useCallback(async () => {
    if (isLoading) return
    
    setIsLoading(true)
    try {
      const result = await commentService.fetchComments(moment.id)

      if ('data' in result && result.data?.listCommentsByPost?.items) {
        const commentsWithLikeState = result.data.listCommentsByPost.items.map((comment: CommentWithLikes) => ({
          ...comment,
          isLiked: comment.likedBy?.items.some(like => like.userId === currentUser?.email),
        }))
        setComments(commentsWithLikeState)
        
        // Calculate total comments count including replies
        let totalCount = commentsWithLikeState.length;
        commentsWithLikeState.forEach((comment: CommentWithLikes) => {
          // Safely check if comment has replies property with items
          if (comment && 
              'replies' in comment && 
              comment.replies && 
              typeof comment.replies === 'object' && 
              'items' in comment.replies && 
              Array.isArray(comment.replies.items)) {
            totalCount += comment.replies.items.length;
          }
        });
        setTotalCommentsCount(totalCount);
        setShouldRefetch(false)
      }
    } catch (error) {
      console.error("Error fetching comments:", error)
    } finally {
      setIsLoading(false)
    }
  }, [moment.id, isLoading, currentUser?.email])

  useEffect(() => {
    if (showComments && !initialized) {
      setInitialized(true)
      fetchComments()
    }
  }, [showComments, initialized, fetchComments])

  useEffect(() => {
    if (shouldRefetch && showComments) {
      fetchComments()
    }
  }, [shouldRefetch, showComments, fetchComments])

  const handleSubmitComment = useCallback(async (content: string) => {
    if (!content.trim() || !currentUser || isLoading) return
    
    setIsLoading(true)
    try {
      await commentService.createComment({
        content,
        userId: currentUser.email,
        postId: moment.id,
        likes: 0
      })

      setShouldRefetch(true)
      if (onCommentAdd) {
        onCommentAdd(moment.id)
      }
    } catch (error) {
      console.error("Error creating comment:", error)
    } finally {
      setIsLoading(false)
    }
  }, [currentUser, moment.id, onCommentAdd, isLoading])

  const handleBookmark = async () => {
    if (!currentUser?.email) return
    
    try {
      if (!isBookmarked) {
        await bookmarkService.createBookmark(currentUser.email, moment.id)
      } else {
        const result = await bookmarkService.findBookmark(currentUser.email, moment.id)
        
        if ('data' in result && result.data) {
          const savedPost = result.data.listSavedPostsByUser?.items?.[0]
          
          if (savedPost) {
            await bookmarkService.deleteBookmark(savedPost.id)
          }
        }
      }
      
      setIsBookmarked(!isBookmarked)
      if (onBookmarkChange) {
        onBookmarkChange()
      }
    } catch (error) {
      console.error("Error toggling bookmark:", error)
    }
  }

  // Визначаємо isLiked на основі likesList та currentUser
  useEffect(() => {
    if (!currentUser?.email) {
      setIsLiked(false)
      return
    }
    // Якщо серед likesList є лайк з userId === currentUser.email, то isLiked = true
    const liked = likesList.some(like => like.userId === currentUser.email)
    setIsLiked(liked)
  }, [likesList, currentUser])

  // Fetch initial likes list on mount
  useEffect(() => {
    const fetchInitialLikes = async () => {
      if (!currentUser?.email) return
      try {
        // Отримати список лайків при першому рендері
        const result = await likeService.fetchLikes(moment.id)
        if (result && result.listLikesByPost?.items) {
          setLikesList(
            result.listLikesByPost.items.map((like: any) => ({
              id: like.id,
              userId: like.userId,
              user: like.user ? {
                name: like.user.name,
                profilePicture: like.user.profilePicture
              } : { name: like.userId, profilePicture: "/groupavatar.svg" }
            }))
          )
        }
      } catch (error) {
        console.error("Error fetching initial likes:", error)
      }
    }
    fetchInitialLikes()
  }, [moment.id, currentUser])

  const handleLike = async () => {
    if (!currentUser?.email) return

    try {
      if (!isLiked) {
        // Використовуй результат createLike для оновлення лічильника та статусу
        const result = await likeService.createLike(currentUser.email, moment.id)
        if (result) {
          setLikesCount(result.updatedPost?.data?.likes ?? likesCount + 1)
          setIsLiked(true)
          // Онови список лайків одразу
          setLikesList(
            result.likesList.map((like: any) => ({
              id: like.id,
              userId: like.userId,
              user: like.user ? {
                name: like.user.name,
                profilePicture: like.user.profilePicture
              } : { name: like.userId, profilePicture: "/groupavatar.svg" }
            }))
          )
        }
      } else {
        // Знайди лайк поточного користувача
        const likeResult = await likeService.checkExistingLike(moment.id, currentUser.email)
        // likeResult.data - це масив лайків
        if ('data' in likeResult && Array.isArray(likeResult.data) && likeResult.data.length > 0) {
          const likeId = likeResult.data[0].id
          // Перевірка на null/undefined для likeId та currentUser.email
          if (likeId && currentUser.email) {
            // Використовуй результат deleteLike для оновлення лічильника та статусу
            const result = await likeService.deleteLike(likeId, moment.id, currentUser.email)
            if (result) {
              setLikesCount(result.updatedPost?.data?.likes ?? Math.max(likesCount - 1, 0))
              setIsLiked(false)
              setLikesList(
                result.likesList.map((like: any) => ({
                  id: like.id,
                  userId: like.userId,
                  user: like.user ? {
                    name: like.user.name,
                    profilePicture: like.user.profilePicture
                  } : { name: like.userId, profilePicture: "/groupavatar.svg" }
                }))
              )
            }
          }
        }
      }
    } catch (error) {
      console.error("Error updating like:", error)
    }
  }

  const fetchLikes = async () => {
    if (!moment.id) return
    
    try {
      const result = await client.graphql({
        query: `
          query GetPostLikes($postId: String!) {
            listLikesByPost(postId: $postId) {
              items {
                id
                userId
                user {
                  name
                  profilePicture
                }
              }
            }
          }
        `,
        variables: {
          postId: moment.id
        }
      }) as GraphQLResult<PostLikesResponse>;

      if ('data' in result && result.data?.listLikesByPost?.items) {
        const likesWithUserInfo = result.data.listLikesByPost.items.map((like: PostLikeItem) => ({
          id: like.id,
          userId: like.userId,
          user: like.user || { name: like.userId, profilePicture: "/groupavatar.svg" }
        }));
        
        setLikesList(likesWithUserInfo);
      }
    } catch (error) {
      console.error("Error fetching likes:", error);
    }
  }

  return (
    
    <div className="bg-white px-5 rounded-lg overflow-hidden sm:px-2 md:px-4 lg:px-5">
     
      <MomentHeader 
        id={moment.id}
        userName={moment.userName}
        avatar={moment.avatar || "/groupavatar.svg"}
        timeAgo={moment.timeAgo}
      />
      
      <MomentContent
        text={moment.text}
        content={moment.content}
        image={moment.image }
        imageGrid={moment.imageGrid }
      />

      <MomentActions
        likesCount={likesCount}
        commentsCount={totalCommentsCount}
        isLiked={isLiked}
        isBookmarked={isBookmarked}
        onLikeClick={handleLike}
        onLikesShow={() => {
          fetchLikes()
          setShowLikesPopup(true)
        }}
        onCommentClick={() => setShowComments(!showComments)}
        onBookmarkClick={handleBookmark}
      />

      {/* Gray divider line with 20px spacing top and bottom */}
      <div className="h-[1px] w-full bg-gray-200 my-5"></div>

      {showComments && (
        <>
          {/* Desktop version */}
          <div className="hidden md:block pb-5">
            <MomentCommentForm
              currentUser={currentUser}
              onSubmit={handleSubmitComment}
              isLoading={isLoading}
            />
            <Comments 
              comments={comments} 
              onRefresh={() => setShouldRefetch(true)}
            />
          </div>

          {/* Mobile version */}
          <div className="md:hidden">
            <div className="pb-5">
              <Comments 
                comments={comments} 
                onRefresh={() => setShouldRefetch(true)}
                onMobileReply={() => setIsReplying(true)}
                onMobileReplyClose={() => setIsReplying(false)}
              />
            </div>

            {/* Mobile Comment Form - Fixed at bottom */}
            {!isReplying && showComments && (
              <MomentCommentForm
                currentUser={currentUser}
                onSubmit={handleSubmitComment}
                isLoading={isLoading}
                isMobile={true}
              />
            )}
          </div>
        </>
      )}
      
      <LikesPopup 
        isOpen={showLikesPopup}
        onClose={() => setShowLikesPopup(false)}
        likes={likesList}
      />
    </div>
  )
}