"use client";

import { useContext, useEffect } from "react";
import { usePathname } from "next/navigation";
import {
  ChatContext,
  ChatContextType,
} from "@/components/Chat/context/ChatContext";
import { Chat } from "@/types/chat";
import { ChatMembersContainer } from "./ChatMembersContainer";
import { UserDetailsContainer } from "./UserDetailsContainer";
import { NewChatUsersContainer } from "./NewChatUsersContainer";
import { useViewState } from "@/components/Chat/context/ViewStateContext";
import { parseChatPathname } from "@/utils/pathHelpers";

interface ChatDetailsMobileContainerProps {
  currentChat: Chat | null;
  showAllMembers: boolean;
  setShowAllMembers: (value: boolean) => void;
}

/**
 * Main container that orchestrates different detail views based on pathname and view state
 */
export const ChatDetailsMobileContainer = ({
  currentChat,
  showAllMembers,
  setShowAllMembers,
}: ChatDetailsMobileContainerProps) => {
  const pathname = usePathname();
  const parsedPath = parseChatPathname(pathname);
  const { showChatDetails, newChatData } = useContext(
    ChatContext
  ) as ChatContextType;
  const { selectedUserDetail, activeView } = useViewState();

  const isNewChatRoute =
    parsedPath.isNewChatRoute || parsedPath.isConfirmGroupChatRoute;
  const isChatDetailsRoute = parsedPath.isDetailsRoute;
  const isUserDetailsView = activeView === "USER_DETAILS";

  useEffect(() => {
    console.log(
      "ChatDetailsMobileContainer - currentChat:",
      activeView,
      currentChat
    );
  });
  if (
    !showChatDetails &&
    !isNewChatRoute &&
    !selectedUserDetail &&
    !isChatDetailsRoute
  ) {
    return null;
  }

  if (selectedUserDetail && isUserDetailsView) {
    return <UserDetailsContainer />;
  }

  if (isNewChatRoute && newChatData) {
    return <NewChatUsersContainer />;
  }

  if ((showChatDetails || isChatDetailsRoute) && currentChat) {
    return (
      <ChatMembersContainer
        currentChat={currentChat}
        showAllMembers={showAllMembers}
        setShowAllMembers={setShowAllMembers}
      />
    );
  }

  return null;
};
