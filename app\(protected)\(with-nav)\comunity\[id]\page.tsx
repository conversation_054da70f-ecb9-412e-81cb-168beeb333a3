"use client";
import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { fetchAuthSession } from "aws-amplify/auth";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";


import NavHeader from "@/components/community/NavHeader";
import MobileCommunityHeader from "@/components/community/MobileCommunityHeader";
import DesktopCommunityBanner from "@/components/community/DesktopCommunityBanner";
import CommunityRulesModal from "@/components/community/CommunityRulesModal";
import CommunitySidebar from "@/components/community/CommunitySidebar";
import PostsList from "@/components/community/PostsList";

import CommunityGroupSidebar from "@/components/community/CommunityGroupSidebar";

type Community = {
  id: string;
  name: string;
  description: string;
  icon?: string;
  banner?: string;
  membersCount: number;
  createdAt: string;
  rules: string[];
  permission: string;
};

type Post = {
  id: string;
  title: string;
  content: string;
  userName: string;
  images: string[];
  likes: number;
  commentsCount: number;
  createdAt: string;
};

export default function CommunityDetail() {
  const params = useParams();
  const communityId = params?.id as string;
  const router = useRouter();
  const client = generateClient<Schema>();
  const [community, setCommunity] = useState<Community | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<string | null>(null);
  const [isMember, setIsMember] = useState(false);
  const [showRules, setShowRules] = useState(false);
  

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          setCurrentUser(String(session.tokens.idToken.payload.email));
        }
      } catch (error) {
        console.error("Error fetching user:", error);
      }
    };
    
    fetchUser();
  }, []);
  
  useEffect(() => {
    const fetchCommunity = async () => {
      if (!communityId) return;
      
      setIsLoading(true);
      try {
        const communityResult = await client.models.Community.get({
          id: communityId
        });
        
        if (communityResult && communityResult.data && communityResult.data.id) {
          setCommunity(communityResult.data as Community);
        } else {
          router.push("/comunity");
          return;
        }
        

        if (currentUser) {
          const membershipResult = await client.models.CommunityMember.list({
            filter: {
              communityId: { eq: communityId },
              userId: { eq: currentUser }
            }
          });
          
          setIsMember(membershipResult.data.length > 0);
        }
        

        const postsResult = await client.models.Post.list({
          filter: {
            communityId: { eq: communityId }
          }
        });
        
        if (postsResult.data) {

          const sortedPosts = [...postsResult.data]
            .filter(post => post && post.id)
            .sort((a, b) => {

              const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
              const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
              return dateB - dateA;
            })
            .map(post => ({
              id: post.id || '',
              title: post.title || '',
              content: post.content || '',
              userName: post.userName || '',

              images: Array.isArray(post.images) 
                ? post.images.filter((img): img is string => img !== null && img !== undefined) 
                : [],
              likes: typeof post.likes === 'number' ? post.likes : 0,
              commentsCount: typeof post.commentsCount === 'number' ? post.commentsCount : 0,
              createdAt: post.createdAt || new Date().toISOString()
            }));
          
          setPosts(sortedPosts);
        }
      } catch (error) {
        console.error("Error fetching community:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (communityId && (currentUser !== null)) {
      fetchCommunity();
    }
  }, [communityId, currentUser, router]);
  
  const handleJoinCommunity = async () => {
    if (!currentUser || !communityId) return;
    
    try {
      await client.models.CommunityMember.create({
        communityId,
        userId: currentUser,
        role: "member",
        joinedAt: new Date().toISOString()
      });
      

      if (community) {
        await client.models.Community.update({
          id: communityId,
          membersCount: (community.membersCount || 0) + 1
        });
        

        setCommunity({
          ...community,
          membersCount: (community.membersCount || 0) + 1
        });
      }
      
      setIsMember(true);
    } catch (error) {
      console.error("Error joining community:", error);
    }
  };
  
  const handleCommentAdded = async (postId: string) => {
    try {

      const updatedPosts = posts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            commentsCount: post.commentsCount + 1
          };
        }
        return post;
      });
      
      setPosts(updatedPosts);
      

      const post = posts.find(p => p.id === postId);
      if (post) {
        await client.models.Post.update({
          id: postId,
          commentsCount: post.commentsCount + 1
        });
      }
    } catch (error) {
      console.error("Error updating comment count:", error);
    }
  };
  
  const transformPostToMoment = (post: Post) => {
    return {
      id: post.id,
      userName: post.userName || "Anonymous",
      groupTitle: community?.name || "",
      groupText: community?.description || "",
      avatar: community?.icon || "/groupavatar.svg",
      text: post.title,
      content: post.content,
      image: post.images?.length > 0 ? post.images[0] : null,
      imageGrid: post.images?.length > 1 ? post.images : undefined,
      likes: post.likes || 0,
      comments: post.commentsCount || 0,
      timeAgo: post.createdAt ? new Date(post.createdAt).toLocaleString() : "",
      onCommentAdd: () => handleCommentAdded(post.id)
    };
  };
  
  return (
    <div className="flex min-h-screen  flex-row  bg-white">

      <CommunityGroupSidebar />
      <div className="w-full">
       
      <NavHeader 
        communityId={communityId} 
        isMember={isMember} 
        handleJoinCommunity={handleJoinCommunity} 
      />
      

      <MobileCommunityHeader
        community={community}
        isMember={isMember}
        handleJoinCommunity={handleJoinCommunity}
        showRules={showRules}
        setShowRules={setShowRules}
      />
      

      <DesktopCommunityBanner
        community={community}
        
        
      />
      

      <CommunityRulesModal
        showRules={showRules}
        setShowRules={setShowRules}
        rules={community?.rules || []}
      />
      

      <div className=" overflow-auto md:h-full h-[calc(100vh-120px)]  flex flex-col md:flex-row">
     
        <PostsList
          communityId={communityId}
          posts={posts}
          isLoading={isLoading}
          isMember={isMember}
          community={community}
          transformPostToMoment={transformPostToMoment}
        />

        <CommunitySidebar community={community} />
         
      </div>
      </div>
    </div>
  );
}
