import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { LucideIcon } from "lucide-react";
import { Loader2 } from "lucide-react";

interface ActionButtonProps {
  icon: LucideIcon;
  text: string;
  onConfirm: () => Promise<void>;
  confirmationTitle: string;
  confirmationMessage: string;
  confirmationButtonText: string;
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
}

export function ActionButton({
  icon: Icon,
  text,
  onConfirm,
  confirmationTitle,
  confirmationMessage,
  confirmationButtonText,
  disabled = false,
  className = "",
  isLoading = false,
}: ActionButtonProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [internalLoading, setInternalLoading] = useState(false);

  const loading = isLoading || internalLoading;

  const handleConfirm = async () => {
    setInternalLoading(true);
    try {
      await onConfirm();
    } finally {
      setInternalLoading(false);
      setShowConfirmation(false);
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        className={`flex items-center justify-center gap-2 hover:bg-red-50 text-red-500 hover:text-red-600 w-full rounded-xl py-3 relative ${className}`}
        onClick={() => setShowConfirmation(true)}
        disabled={disabled || loading}
      >
        {loading && (
          <svg
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-5 h-5 animate-spin text-red-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        <div className={loading ? "opacity-0" : "flex items-center gap-2"}>
          <Icon className="h-5 w-5" />
          <span className="font-medium">{text}</span>
        </div>
      </Button>

      <AlertDialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{confirmationTitle}</AlertDialogTitle>
            <AlertDialogDescription>
              {confirmationMessage}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleConfirm();
              }}
              className="bg-red-500 hover:bg-red-600"
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin text-white" />
                  <span>Processing...</span>
                </div>
              ) : (
                confirmationButtonText
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
