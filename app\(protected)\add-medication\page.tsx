'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';
import CustomButton from '@/components/ui/CustomButton';
import { Input } from '@/components/ui/input';

export default function AddMedication() {
  const router = useRouter();
  const [isStartDateOpen, setIsStartDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);
  const [noEndDate, setNoEndDate] = useState(false);

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF] relative">
      {/* Header */}
      <div className="flex items-center pt-[51px] pl-6 pr-6 py-4">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <div className="ml-4">
          <h1 className="text-[16px] font-medium">Add Medication</h1>
        </div>
      </div>

      {/* Form Content */}
      <div className="px-6 bg-white rounded-3xl pb-5 pt-5  h-full overflow-auto">
        {/* Medication Name */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Medication Name</label>
          <Input
            type="text"
            placeholder="e.g., Hydroxychloroquine"
            className="w-full bg-[#F5F5F5] border-none"
          />
        </div>

        {/* Dosage */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Dosage</label>
          <Input
            type="text"
            placeholder="e.g., 500 mmg"
            className="w-full bg-[#F5F5F5] border-none"
          />
        </div>

        {/* Start Date */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Start date</label>
          <div className="relative">
            <Input
              type="text"
              placeholder="MM / DD / YYYY"
              className="w-full bg-[#F5F5F5] border-none"
              onClick={() => setIsStartDateOpen(!isStartDateOpen)}
            />
            <Image
              src="/health/calender.svg"
              alt="Calendar"
              width={24}
              height={24}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
            />
          </div>
        </div>

        {/* End Date */}
        <div className="mb-2">
          <label className="block mb-2 font-medium">End date</label>
          <div className="relative">
            <Input
              type="text"
              placeholder="MM / DD / YYYY"
              disabled={noEndDate}
              className={`w-full bg-[#F5F5F5] border-none ${noEndDate ? 'opacity-50' : ''}`}
              onClick={() => !noEndDate && setIsEndDateOpen(!isEndDateOpen)}
            />
            <Image
              src="/health/calender.svg"
              alt="Calendar"
              width={24}
              height={24}
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer ${noEndDate ? 'opacity-50' : ''}`}
            />
          </div>
        </div>

        {/* No End Date Checkbox */}
        <div className="mb-6 flex items-center">
          <input
            type="checkbox"
            id="noEndDate"
            checked={noEndDate}
            onChange={() => setNoEndDate(!noEndDate)}
            className="mr-2 h-5 w-5"
          />
          <label htmlFor="noEndDate" className="text-gray-700">No end date</label>
        </div>

        {/* Route */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Route</label>
          <Input
            type="text"
            placeholder="e.g., Oral"
            className="w-full bg-[#F5F5F5] border-none"
          />
        </div>

        {/* Administrator */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Administrator</label>
          <Input
            type="text"
            placeholder="e.g., Self"
            className="w-full bg-[#F5F5F5] border-none"
          />
        </div>

        {/* Prescribing Physician */}
        <div className="mb-8">
          <label className="block mb-2 font-medium">Prescribing Physician</label>
          <Input
            type="text"
            placeholder="e.g., Dr. Sarah Lee"
            className="w-full bg-[#F5F5F5] border-none"
          />
        </div>

        {/* Save Button */}
        <div className="mb-8">
          <CustomButton onClick={() => router.push('/medication')}>
            Save
          </CustomButton>
        </div>
      </div>
    </div>
  );
}
