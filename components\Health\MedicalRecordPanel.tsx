import { useEffect, useState } from 'react';
import { ChevronRightIcon, XIcon } from 'lucide-react';
import Image from 'next/image';
import Modal from '@/components/Modal';
import OutpatientContent from '@/app/(protected)/medical-record/components/OutpatientContent';
import SymptomContent from '@/app/(protected)/medical-record/components/SymptomContent';
import MedicationReactionContent from '@/app/(protected)/medical-record/components/MedicationReactionContent';
import { motion, AnimatePresence } from 'framer-motion';

interface MedicalRecordPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

// Example medical records data - same as in the medical-record page
const medicalRecords = [
  {
    date: 'March 3, 2025',
    type: 'Outpatient Visit',
    location: 'Bellevue Medical Center',
    doctor: 'Dr. <PERSON>',
    description: 'Annual wellness checkup',
    isClickable: true,
  },
  {
    date: 'March 24, 2025',
    type: 'Symptom',
    description: 'Some description of sympton',
    isClickable: true,
  },
  {
    date: 'March 3, 2025',
    type: 'Medication Reaction',
    description: 'Reaction description',
    isClickable: true,
  },
];

const MedicalRecordPanel = ({ isOpen, onClose }: MedicalRecordPanelProps) => {
  const [activeTab, setActiveTab] = useState('All');
  const [modalOpen, setModalOpen] = useState(false);
  const [activeRecord, setActiveRecord] = useState<null | typeof medicalRecords[0]>(null);
  const [showContent, setShowContent] = useState(false);
  const [showUserInfo, setShowUserInfo] = useState(false);
  
  // Function to handle record click
  const handleRecordClick = (record: typeof medicalRecords[0]) => {
    if (record.isClickable) {
      setActiveRecord(record);
      setModalOpen(true);
    }
  };

  // Function to render modal content based on record type
  const renderModalContent = () => {
    if (!activeRecord) return null;

    switch (activeRecord.type) {
      case 'Outpatient Visit':
        return <OutpatientContent />;
      case 'Symptom':
        return <SymptomContent />;
      case 'Medication Reaction':
        return <MedicationReactionContent />;
      default:
        return null;
    }
  };
  
  // Handle connect button click with animation delay
  const handleConnect = () => {
    setShowContent(true);
    setTimeout(() => {
      setShowUserInfo(true);
    }, 300); // Delay showing user info for a better visual effect
  };
  
  return (
    <>
      <div 
        className={`flex-shrink-0 h-full overflow-hidden transition-all duration-300 ease-in-out border-l border-gray-200 bg-white`}
        style={{ 
          width: isOpen ? '400px' : '0',
          opacity: isOpen ? 1 : 0
        }}
      >
        {isOpen && (
          <div className="flex flex-col h-full w-[400px]">
            {/* Header */}
            <div className="flex items-center justify-between p-6 ">
              <h1 className="text-lg font-medium">Medical Record</h1>
            </div>

            {/* Content */}
            <div className="flex-1 px-6 overflow-auto">
              {/* General Info Section - Always visible but data is conditional */}
              <div className="bg-white rounded-3xl pt-5 px-5 pb-5  border border-gray-200 bg-[#E9F0FF]">
                <div className="flex items-center mb-4">
                  <Image
                    src="/medical-record/inputt.svg"
                    alt="Info"
                    width={24}
                    height={24}
                    className="mr-2"
                  />
                  <h2 className="text-blue-500 font-medium">General Info</h2>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Birth Year</span>
                    <AnimatePresence mode="wait">
                      <motion.span 
                        key={showUserInfo ? "birth-info" : "birth-dash"}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -5 }}
                        transition={{ duration: 0.3 }}
                        className="font-medium"
                      >
                        {showUserInfo ? '1985' : '-'}
                      </motion.span>
                    </AnimatePresence>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Gender</span>
                    <AnimatePresence mode="wait">
                      <motion.span 
                        key={showUserInfo ? "gender-info" : "gender-dash"}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -5 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="font-medium"
                      >
                        {showUserInfo ? 'Female' : '-'}
                      </motion.span>
                    </AnimatePresence>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Region</span>
                    <AnimatePresence mode="wait">
                      <motion.span 
                        key={showUserInfo ? "region-info" : "region-dash"}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -5 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                        className="font-medium"
                      >
                        {showUserInfo ? 'United States' : '-'}
                      </motion.span>
                    </AnimatePresence>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Race</span>
                    <AnimatePresence mode="wait">
                      <motion.span 
                        key={showUserInfo ? "race-info" : "race-dash"}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -5 }}
                        transition={{ duration: 0.3, delay: 0.3 }}
                        className="font-medium"
                      >
                        {showUserInfo ? 'Asian' : '-'}
                      </motion.span>
                    </AnimatePresence>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Ethnicity</span>
                    <AnimatePresence mode="wait">
                      <motion.span 
                        key={showUserInfo ? "ethnicity-info" : "ethnicity-dash"}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -5 }}
                        transition={{ duration: 0.3, delay: 0.4 }}
                        className="font-medium"
                      >
                        {showUserInfo ? 'Non-Hispanic' : '-'}
                      </motion.span>
                    </AnimatePresence>
                  </div>
                </div>
              </div>
              
              {/* Filter Tabs */}
              <div className="flex space-x-2 my-[30px] h-[38px]">
                {['All', 'Day', 'Month', 'Year'].map((tab) => (
                  <button
                    key={tab}
                    className={`px-6 py-2 rounded-full ${
                      activeTab === tab
                        ? 'bg-[#E9F0FF] text-blue-500 font-medium shadow-sm'
                        : 'bg-[#F5F5F5] text-black'
                    }`}
                    onClick={() => setActiveTab(tab)}
                  >
                    {tab}
                  </button>
                ))}
              </div>

              {!showContent ? (
                /* Empty state with centered button */
                <div className="flex flex-col items-center justify-center mt-20">
                  <p className="text-gray-500 mb-6">There's no data found</p>
                  <button
                    onClick={handleConnect}
                    className="w-full py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition-colors"
                  >
                    Connect Medical Data
                  </button>
                </div>
              ) : (
                /* Medical Records List with animation using framer-motion */
                <motion.div 
                  className="space-y-4 "
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                >
                  {medicalRecords.map((record, index) => (
                    <div key={index} className="relative">
                      {/* Timeline line for the current item (connecting to next) */}
                      {index < medicalRecords.length - 1 && (
                        <div 
                          className="absolute top-5 left-1.5 w-[1px] h-full bg-[#E5E5E5]" 
                          style={{ transform: "translateX(-50%)" }}
                        ></div>
                      )}
                      
                      {/* Timeline dot */}
                      <div className="absolute top-1.5 left-0 w-3 h-3 bg-white rounded-full border-2 border-blue-100 z-10"></div>

                      {/* Date */}
                      <div className="ml-6 text-sm text-blue-500 bg-[#E9F0FF] rounded-2xl px-4 inline-block">
                        {record.date}
                      </div>

                      {/* Record card */}
                      <div 
                        className="ml-6 bg-white rounded-2xl border border-gray-200 shadow-sm p-4 mt-2 cursor-pointer" 
                        onClick={() => handleRecordClick(record)}
                      >
                        <div className="flex justify-between items-center mb-3">
                          <h3 className="font-medium text-lg">{record.type}</h3>
                          <ChevronRightIcon className="h-6 w-6 text-gray-400" />
                        </div>

                        {record.location && (
                          <div className="flex items-center text-gray-500 text-base mb-2">
                            <div className="mr-2">
                              <Image
                                src="/medical-record/map-marker.svg"
                                alt="Location"
                                width={16}
                                height={16} 
                                className="w-4 h-4"
                              />
                            </div>
                            {record.location}
                          </div>
                        )}

                        {record.doctor && (
                          <div className="flex items-center text-gray-500 text-base mb-2">
                            <div className="mr-2">
                              <Image
                                src="/menu-deepg.svg"
                                alt="Doctor"
                                width={16}
                                height={16} 
                                className="w-4 h-4"
                              />
                            </div>
                            {record.doctor}
                          </div>
                        )}

                        <div className="flex items-start text-gray-600 text-sm bg-gray-100 rounded-2xl p-2">
                          <div className="w-5 flex justify-center mr-1 mt-0.5">
                            <Image
                              src="/medical-record/note.svg"
                              alt="Description"
                              width={16}
                              height={16} 
                              className="w-4 h-4"
                            />
                          </div>
                          {record.description}
                        </div>
                      </div>
                    </div>
                  ))}
                </motion.div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Modal for record details */}
      <Modal 
        isOpen={modalOpen} 
        onClose={() => setModalOpen(false)} 
        title={activeRecord?.type || ''}
        date={activeRecord?.date}
      >
        {renderModalContent()}
      </Modal>
    </>
  );
};

export default MedicalRecordPanel;
