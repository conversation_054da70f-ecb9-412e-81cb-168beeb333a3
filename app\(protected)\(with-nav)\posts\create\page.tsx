"use client";

import { PostHeader } from "./components/PostHeader";
import { GroupSelection } from "./components/GroupSelection";
import { PostEditor } from "./components/PostEditor";
import { ImagePreview } from "./components/ImagePreview";
import { ActionBar } from "./components/ActionBar";
import { usePostCreation } from "./hooks/usePostCreation";

export default function CreatePost() {
  const {
    postTitle,
    setPostTitle,
    postContent,
    setPostContent,
    postImages,
    imageLoading,
    handleImageChange,
    removeImage,
    handleCreatePost,
    isFormValid
  } = usePostCreation();

  return (
    <div className="max-w-2xl mx-auto p-4">
      <PostHeader onCreatePost={handleCreatePost} isValid={isFormValid} />
      <GroupSelection />
      <PostEditor 
        title={postTitle}
        content={postContent}
        onTitleChange={setPostTitle}
        onContentChange={setPostContent}
      />
      <ImagePreview images={postImages} onRemoveImage={removeImage} />
      <ActionBar onImageChange={handleImageChange} imageLoading={imageLoading} />
      <ActionBar onImageChange={handleImageChange} imageLoading={imageLoading} isMobile={true} />
    </div>
  );
}