"use client";


import { generateClient } from "aws-amplify/api";

import { Schema } from "@/amplify/data/resource";
import { parseAmplifyConfig } from "aws-amplify/utils";
import { Amplify } from "aws-amplify";
import outputs from "@/amplify_outputs.json";
const amplifyConfig = parseAmplifyConfig(outputs);
Amplify.configure({
  ...amplifyConfig,
  API: {
    ...amplifyConfig.API,
    Events: {
      endpoint:
        "https://jo6vp743zjf6xdt4b6u5amf434.appsync-api.us-east-1.amazonaws.com/event",
      region: "us-east-1",
      defaultAuthMode: "apiKey",
      apiKey: "da2-liw5uercxfccrhgzlqhxj3tu2u",
    },
  },
});

const client = generateClient<Schema>();
export default function AdminPage() {
 
  return (
    <div className="container mx-auto p-6">
      
    </div>
  );
}
