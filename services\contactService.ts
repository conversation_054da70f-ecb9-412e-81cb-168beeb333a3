import { generateClient } from "aws-amplify/api";
import type { Schema } from "@/amplify/data/resource";
import { UserType, FriendRequest } from "../types/contacts/types";

const client = generateClient<Schema>();

export const fetchUsers = async (
  currentUserId: string
): Promise<UserType[]> => {
  try {
    const { data } = await client.models.User.list();

    if (Array.isArray(data)) {
      return data
        .filter(
          (user) =>
            user &&
            typeof user === "object" &&
            "userId" in user &&
            user.userId !== currentUserId
        )
        .map((user) => ({
          userId: user.userId,
          name: user.name || "",
          email: user.email || "",
          friendRequestStatus: null,
        }));
    }
    return [];
  } catch (error) {
    console.error("Error fetching users:", error);
    throw error;
  }
};

export const fetchFriendRequests = async (
  currentUserId: string,
  allUsers: UserType[]
): Promise<{
  pendingRequests: FriendRequest[];
  acceptedFriends: UserType[];
  updatedAllUsers: UserType[];
}> => {
  if (!currentUserId) {
    return { pendingRequests: [], acceptedFriends: [], updatedAllUsers: [] };
  }

  try {
    const receivedRequests =
      await client.models.FriendRequest.listReceivedFriendRequests({
        receiverId: currentUserId,
      });

    const sentRequests =
      await client.models.FriendRequest.listSentFriendRequests({
        senderId: currentUserId,
      });

    const allRequests = [
      ...(receivedRequests.data || []).map((req) => ({
        ...req,
        direction: "received",
      })),
      ...(sentRequests.data || []).map((req) => ({
        ...req,
        direction: "sent",
      })),
    ];

    const pending = allRequests.filter(
      (req) => req.status === "PENDING" && req.direction === "received"
    );

    const pendingWithUsers = await Promise.all(
      pending.map(async (request) => {
        try {
          const { data: user } = await client.models.User.get({
            userId: request.senderId,
          });

          return {
            ...request,
            user: {
              userId: request.senderId,
              name: user?.name || "",
              email: user?.email || "",
            },
          };
        } catch (error) {
          console.error("Error fetching user:", error);
          return request;
        }
      })
    );

    const accepted = allRequests.filter((req) => req.status === "ACCEPTED");

    const acceptedFriendIds = accepted.map((req) =>
      req.direction === "received" ? req.senderId : req.receiverId
    );

    const updatedUsers = updateUsersWithFriendStatus(
      allUsers,
      allRequests,
      currentUserId
    );

    const acceptedFriendEntries = updatedUsers.filter((user) =>
      acceptedFriendIds.includes(user.userId)
    );

    return {
      pendingRequests: pendingWithUsers.filter(
        (req) => req.id
      ) as FriendRequest[],
      acceptedFriends: acceptedFriendEntries,
      updatedAllUsers: updatedUsers,
    };
  } catch (error) {
    console.error("Error fetching friend requests:", error);
    throw error;
  }
};

export const updateUsersWithFriendStatus = (
  users: UserType[],
  requests: any[],
  currentUserId: string
): UserType[] => {
  return users.map((user) => {
    const sentRequest = requests.find(
      (req) => req.senderId === currentUserId && req.receiverId === user.userId
    );

    const receivedRequest = requests.find(
      (req) => req.senderId === user.userId && req.receiverId === currentUserId
    );

    let friendRequestStatus = null;

    if (sentRequest) {
      friendRequestStatus = {
        type: "sent" as const,
        status: sentRequest.status,
        requestId: sentRequest.id,
      };
    } else if (receivedRequest) {
      friendRequestStatus = {
        type: "received" as const,
        status: receivedRequest.status,
        requestId: receivedRequest.id,
      };
    }

    return {
      ...user,
      friendRequestStatus,
    };
  });
};

export const sendFriendRequest = async (
  currentUserId: string,
  receiverId: string
): Promise<{ success: boolean; requestId?: string }> => {
  if (!currentUserId) {
    console.error("No current user found");
    return { success: false };
  }

  try {
    const result = await client.models.FriendRequest.create({
      senderId: currentUserId,
      receiverId,
      status: "PENDING",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    const requestId = result?.data?.id || undefined;

    return {
      success: true,
      requestId,
    };
  } catch (error) {
    console.error("Error sending friend request:", error);
    return { success: false };
  }
};

export const respondToFriendRequest = async (
  requestId: string,
  status: "ACCEPTED" | "DECLINED"
): Promise<boolean> => {
  try {
    await client.models.FriendRequest.update({
      id: requestId,
      status,
      updatedAt: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error(`Error ${status.toLowerCase()}ing friend request:`, error);
    return false;
  }
};

export const deleteContact = async (
  currentUserId: string,
  otherUserId: string
): Promise<boolean> => {
  try {
    const sentRequests =
      await client.models.FriendRequest.listSentFriendRequests({
        senderId: currentUserId,
      });

    const receivedRequests =
      await client.models.FriendRequest.listReceivedFriendRequests({
        receiverId: currentUserId,
      });

    const sentRequest = sentRequests.data?.find(
      (req) => req.receiverId === otherUserId
    );

    const receivedRequest = receivedRequests.data?.find(
      (req) => req.senderId === otherUserId
    );

    const requestToDelete = sentRequest || receivedRequest;

    if (requestToDelete && requestToDelete.id) {
      await client.models.FriendRequest.delete({
        id: requestToDelete.id,
      });
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error deleting contact:", error);
    return false;
  }
};

export const getFriendRequestStatus = async (
  currentUserId: string,
  otherUserId: string
) => {
  try {
    const sentRequests =
      await client.models.FriendRequest.listSentFriendRequests({
        senderId: currentUserId,
      });

    const sentRequest = sentRequests.data?.find(
      (req) => req.receiverId === otherUserId
    );

    if (sentRequest) {
      return {
        type: "sent" as const,
        status: sentRequest.status,
        requestId: sentRequest.id,
      };
    }

    const receivedRequests =
      await client.models.FriendRequest.listReceivedFriendRequests({
        receiverId: currentUserId,
      });

    const receivedRequest = receivedRequests.data?.find(
      (req) => req.senderId === otherUserId
    );

    if (receivedRequest) {
      return {
        type: "received" as const,
        status: receivedRequest.status,
        requestId: receivedRequest.id,
      };
    }

    return null;
  } catch (error) {
    console.error("Error getting friend request status:", error);
    return null;
  }
};
