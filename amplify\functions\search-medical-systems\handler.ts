import { Amplify } from "aws-amplify";
import axios from "axios";
import type { Schema } from "../../data/resource";
import type { <PERSON><PERSON> } from "aws-lambda";
import { AxiosError } from "axios";

import { getAmplifyDataClientConfig } from "@aws-amplify/backend/function/runtime";
import { env } from "$amplify/env/search-medical-systems";
import { generateClient } from "@aws-amplify/api";

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);

Amplify.configure(resourceConfig, libraryOptions);
const client = generateClient<Schema>();

interface HandlerEvent {
  arguments: {
    query: string;
    offset?: number;
    system_type?: string;
    userId: string;
  };
  identity?: {
    sub: string;
  };
}

type ResponseType = Array<{
  id: number;
  name: string;
  address: string;
  fhirVersion: string;
  ehr: string;
  resourceUrl: string;
  logo: string;
}>;

export const handler = async (event: HandlerEvent): Promise<ResponseType> => {
  console.log("Received event:", JSON.stringify(event, null, 2));

  // Access arguments from event.arguments
  const { query, offset = 0, system_type } = event.arguments || {};
  const userId = event.identity?.sub;
  if (!userId) {
    console.error("User ID not found in event identity");
    throw new Error("User authentication required");
  }
  if (!query) {
    console.error("Query parameter is missing or empty");
    throw new Error("Query parameter is required");
  }

  const clientId = process.env.ONEUP_CLIENT_ID;
  const clientSecret = process.env.ONEUP_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    console.error("Client ID or Client Secret not configured");
    throw new Error("Client ID or Client Secret not configured");
  }

  const accessToken = await getValidAccessToken(clientId, clientSecret, userId);

  if (!accessToken) {
    console.error("Failed to obtain access token for user:", userId);
    throw new Error("Failed to obtain access token");
  }

  try {
    const url = `https://system-search.1up.health/api/search?query=${encodeURIComponent(query)}&offset=${offset}${system_type ? `&system_type=${encodeURIComponent(system_type)}` : ""}`;
    console.log("Making API request to:", url);
    const response = await axios.post(
      url,
      {},
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );
    console.log("API response:", JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(
      "Error making API request:",
      axiosError?.response?.data || axiosError?.message
    );
    throw new Error("Failed to retrieve search results");
  }
};

async function getValidAccessToken(
  clientId: string,
  clientSecret: string,
  userId: string
): Promise<string | null> {
  const appUserId = `jinix_${userId}`;

  try {
    // Fetch HealthDataConnection record
    const { data: connection } = await client.models.HealthDataConnection.get({
      appUserId,
    });

    if (!connection) {
      console.log(
        `No health data connection found for user: ${userId}, creating new connection...`
      );
      // Create a new user and connection
      try {
        // Create 1upHealth user
        const userResponse = await axios.post(
          `https://api.1up.health/user-management/v1/user?app_user_id=${appUserId}`,
          {},
          {
            headers: {
              client_id: clientId,
              client_secret: clientSecret,
            },
          }
        );

        // Get authorization code
        const authCodeResponse = await axios.post(
          `https://api.1up.health/user-management/v1/user/auth-code?app_user_id=${appUserId}&client_id=${clientId}&client_secret=${clientSecret}`,
          {}
        );
        const code = authCodeResponse.data.code;

        // Exchange code for tokens
        const tokenResponse = await axios.post(
          "https://auth.1up.health/oauth2/token",
          new URLSearchParams({
            grant_type: "authorization_code",
            code: String(code),
            client_id: clientId,
            client_secret: clientSecret,
          }),
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        const tokenData = tokenResponse.data as {
          access_token: string;
          refresh_token: string;
          expires_in: number;
        };

        if (!tokenData?.access_token) {
          console.error(
            `Failed to obtain initial access token for user: ${userId}`
          );
          return null;
        }

        const now = new Date();

        // Create HealthDataConnection record
        await client.models.HealthDataConnection.create({
          appUserId,
          userId,
          accessToken: tokenData.access_token,
          refreshToken: tokenData.refresh_token,
          code,
          createdAt: now.toISOString(),
          updatedAt: now.toISOString(),
        });

        console.log(`Created new health data connection for user: ${userId}`);
        return tokenData.access_token;
      } catch (error) {
        const axiosError = error as AxiosError;
        console.error(
          "Error creating new health data connection:",
          axiosError?.response?.data || axiosError?.message
        );
        return null;
      }
    }

    // Verify if the access token is valid
    const isTokenValid = await verifyAccessToken(connection.accessToken);
    if (isTokenValid) {
      console.log(`Using existing valid access token for user: ${userId}`);
      return connection.accessToken;
    }

    console.log(
      `Access token invalid for user: ${userId}, attempting to refresh...`
    );
    try {
      // Attempt to refresh the token
      const tokenResponse = await axios.post(
        "https://auth.1up.health/oauth2/token",
        new URLSearchParams({
          grant_type: "refresh_token",
          refresh_token: connection.refreshToken,
          client_id: clientId,
          client_secret: clientSecret,
        }),
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const tokenData = tokenResponse.data as {
        access_token: string;
        refresh_token?: string;
        expires_in: number;
      };

      if (tokenData?.access_token) {
        const newAccessToken = tokenData.access_token;
        const newRefreshToken =
          tokenData.refresh_token || connection.refreshToken;
        const now = new Date();

        await client.models.HealthDataConnection.update({
          appUserId: connection.appUserId,
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          updatedAt: now.toISOString(),
        });

        console.log(`Successfully refreshed access token for user: ${userId}`);
        return newAccessToken;
      } else {
        throw new Error("No access token returned in refresh response");
      }
    } catch (refreshError) {
      const axiosError = refreshError as AxiosError;
      console.error(
        "Error refreshing access token:",
        axiosError?.response?.data || axiosError?.message
      );

      // If refresh token is invalid/expired, generate a new auth code
      if (
        axiosError.response?.status === 400 ||
        axiosError.response?.status === 401
      ) {
        console.log("Refresh token expired, generating new auth code...");
        const authCodeResponse = await axios.post(
          `https://api.1up.health/user-management/v1/user/auth-code?app_user_id=${appUserId}&client_id=${clientId}&client_secret=${clientSecret}`,
          {}
        );
        const code = authCodeResponse.data.code;

        // Exchange new code for tokens
        const tokenResponse = await axios.post(
          "https://auth.1up.health/oauth2/token",
          new URLSearchParams({
            grant_type: "authorization_code",
            code: String(code),
            client_id: clientId,
            client_secret: clientSecret,
          }),
          {
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        const tokenData = tokenResponse.data as {
          access_token: string;
          refresh_token: string;
          expires_in: number;
        };

        if (tokenData?.access_token) {
          const now = new Date();
          await client.models.HealthDataConnection.update({
            appUserId: connection.appUserId,
            accessToken: tokenData.access_token,
            refreshToken: tokenData.refresh_token,
            code,
            updatedAt: now.toISOString(),
          });

          console.log(
            `Successfully obtained new access token for user: ${userId} using new auth code`
          );
          return tokenData.access_token;
        } else {
          throw new Error("No access token returned in new token response");
        }
      }

      throw new Error(
        `Failed to refresh access token: ${axiosError?.response?.data || axiosError?.message}`
      );
    }
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(
      "Error getting valid access token:",
      axiosError?.response?.data || axiosError?.message
    );
    return null;
  }
}

/**
 * Verifies if an access token is still valid by making a test request
 */
async function verifyAccessToken(accessToken: string): Promise<boolean> {
  try {
    // Make a lightweight request to validate the token
    await axios.get("https://api.1up.health/r4/Patient", {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
    return true;
  } catch (error) {
    const axiosError = error as AxiosError;
    console.error(
      "Token verification failed:",
      axiosError?.response?.data || axiosError?.message
    );
    return false;
  }
}
