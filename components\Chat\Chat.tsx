"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { toast } from "sonner";
import { Amplify } from "aws-amplify";
import outputs from "@/amplify_outputs.json";
import { generateClient } from "aws-amplify/api";
import type { Schema } from "@/amplify/data/resource";
import { useRouter } from "next/navigation";

import Sidebar from "./Sidebar";

import { useUser } from "@/hooks/useUsers";
import { useChats } from "@/hooks/chat/useChats";
import { useMessages } from "@/hooks/chat/useMessages";
import ChatWindow from "./ChatWindow";
import { MessageWithoutChat } from "@/types/chat";

// Configure Amplify with the standard outputs
Amplify.configure(outputs);

const client = generateClient<Schema>();

interface ChatProps {
  headerAction?: React.ReactNode;
  questionnaireId?: string;
  showSidebar?: boolean;
  isHealthTab?: boolean;
  initialChatId?: string;
}

export default function Chat({
  headerAction,
  questionnaireId,
  showSidebar = true,
  isHealthTab = false,
  initialChatId,
}: ChatProps) {
  const {
    currentUser,
    loading: isLoadingUser,
    error: userContextError,
  } = useUser();
  const userId = currentUser?.userId || null;
  const userError = userContextError?.message || null;
  const {
    chatId,
    userChats,
    isLoading: isLoadingChatsList,
    isLoadingChats,
    isLoadingMore: isLoadingMoreChats,
    hasMoreChats,
    startNewChat,
    switchToChat,
    deleteChat,
    loadMoreChats,
    error: chatError,
  } = useChats(userId, questionnaireId, isHealthTab);

  const activeChatId = chatId;
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [newMessageAdded, setNewMessageAdded] = useState(false);
  const [isCreatingChat, setIsCreatingChat] = useState(false);

  const healthChatInitializedRef = useRef(false);
  const router = useRouter();

  useEffect(() => {
    if (userId && activeChatId) {
      const newSessionId = `${userId}-${Date.now()}`;
      console.log("Chat: Generating new sessionId:", newSessionId);
      setSessionId(newSessionId);
    } else {
      setSessionId(null);
    }
  }, [userId, activeChatId]);

  const {
    messages,
    isLoading: isLoadingMessages,
    isSending,
    sendMessage,
    isStreaming,
    error: messageError,
    hasMore: hasMoreMessages,
    isLoadingMore: isLoadingMoreMessages,
    setObserverTarget,
    updateMessages,
  } = useMessages(userId, activeChatId, sessionId, { setNewMessageAdded });

  useEffect(() => {
    const initializeChat = async () => {
      if (isHealthTab) {
        console.log(
          "Health tab chat initialization is managed by useChats hook"
        );
        return;
      }

      if (
        userId &&
        !chatId &&
        userChats.length === 0 &&
        !isLoadingChats &&
        !isLoadingChatsList &&
        !initialChatId
      ) {
        console.log("Initializing first chat automatically");

        startNewChat().catch((err) => {
          console.error("Failed to auto-initialize first chat:", err);
        });
      }
    };
    initializeChat();
  }, [
    userId,
    chatId,
    userChats.length,
    isLoadingChats,
    isLoadingChatsList,
    startNewChat,
    questionnaireId,
    initialChatId,
    isHealthTab,
    currentUser,
  ]);

  useEffect(() => {
    const showError = (
      title: string,
      description: string | undefined | null
    ) => {
      if (description) {
        toast.error(title, { description, position: "top-center" });
      }
    };
    showError("Authentication Error", userError);
    showError("Chat Error", chatError);
    showError("Message Error", messageError);
  }, [userError, chatError, messageError]);

  const handleNewChat = useCallback(async () => {
    if (isCreatingChat) return;
    setIsCreatingChat(true);
    try {
      await startNewChat();
    } catch (err) {
      console.error("Failed to create new chat:", err);
      toast.error("Error", {
        description: "Failed to create new chat.",
        position: "top-center",
      });
    } finally {
      setIsCreatingChat(false);
    }
  }, [startNewChat, isCreatingChat]);

  const handleChatSelect = useCallback(
    (selectedChatId: string) => {
      if (selectedChatId === chatId) return;
      try {
        switchToChat(selectedChatId);
      } catch (err) {
        console.error("Failed to switch chat:", err);
        toast.error("Error", {
          description: "Failed to switch to selected chat.",
          position: "top-center",
        });
      }
    },
    [switchToChat, chatId]
  );

  const realtimeSubRef = useRef<any>(null);

  const getChatType = useCallback(() => {
    console.log("Getting chat type for activeChatId:", userChats);
    const chat = userChats.find((c) => c.id === activeChatId);
    return chat?.chatType || "AI";
  }, [userChats, activeChatId]);

  useEffect(() => {
    const chatType = getChatType();

    if (!activeChatId || chatType === "AI" || isHealthTab) {
      if (realtimeSubRef.current) {
        console.log(
          "Unsubscribing from real-time updates for chat:",
          activeChatId
        );
        realtimeSubRef.current.unsubscribe();
        realtimeSubRef.current = null;
      }
      return;
    }

    if (realtimeSubRef.current) {
      console.log(
        "Already subscribed to real-time updates for chat:",
        activeChatId
      );
      return;
    }

    console.log(
      "Subscribing to real-time updates for non-AI chat:",
      activeChatId
    );
    const sub = client.models.ChatMessage.observeQuery({
      filter: { chatId: { eq: activeChatId } },
      selectionSet: [
        "id",
        "chatId",
        "userId",
        "message",
        "createdAt",
        "updatedAt",
        "messageType",
        "attachments",
        "user.email",
      ],
    }).subscribe({
      next: ({ items, isSynced }) => {
        console.log("Real-time update received:", items, "Synced:", isSynced);

        if (isSynced) {
          items.forEach((item) => {
            if (!item.userId) {
              console.warn(
                "Skipping real-time message with null userId:",
                item.id
              );
              return;
            }

            const message: MessageWithoutChat = {
              id: item.id,
              chatId: item.chatId,
              userId: item.userId,
              message: item.message || "",
              createdAt: item.createdAt || new Date().toISOString(),
              updatedAt:
                item.updatedAt || item.createdAt || new Date().toISOString(),
              messageType: item.messageType || "TEXT",
              attachments: item.attachments,
              isGhost: false,
              user: item.user
                ? {
                    email: item.user.email || null,
                  }
                : null,
            };

            updateMessages(message);

            setNewMessageAdded(true);
          });
        }
      },
      error: (error) => {
        console.error("Real-time subscription error:", error);
        toast.error("Real-time connection error.");
      },
    });
    realtimeSubRef.current = sub;

    return () => {
      if (realtimeSubRef.current) {
        console.log(
          "Unsubscribing from real-time updates on cleanup for chat:",
          activeChatId
        );
        realtimeSubRef.current.unsubscribe();
        realtimeSubRef.current = null;
      }
    };
  }, [activeChatId, getChatType, isHealthTab, updateMessages]);

  const handleSendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;
      if (!userId) {
        toast.error("Error", {
          description: "Cannot send message: User not identified.",
          position: "top-center",
        });
        return;
      }

      let currentChatId = activeChatId;
      let chatType = getChatType();

      if (!questionnaireId && !currentChatId) {
        console.log("No active chat, creating one before sending message");
        try {
          const newChatId = await startNewChat();
          if (!newChatId) {
            throw new Error("Failed to get new chat ID from startNewChat");
          }
          currentChatId = newChatId;

          chatType = getChatType();
        } catch (err) {
          console.error("Error creating chat or sending message:", err);
          toast.error("Error", {
            description: "Could not start a new chat to send the message.",
            position: "top-center",
          });
          return;
        }
      }

      if (!currentChatId) {
        console.error("Attempted to send message without a valid chat ID.");
        toast.error("Error", {
          description: "Cannot send message: No active chat.",
          position: "top-center",
        });
        return;
      }

      if (chatType === "AI" || isHealthTab) {
        try {
          await sendMessage(content);
        } catch (err) {
          console.error("Failed to send message (AI/Health):", err);
          toast.error("Error", {
            description: "Failed to send message.",
            position: "top-center",
          });
        }
        return;
      }

      try {
        await client.models.ChatMessage.create({
          chatId: currentChatId,
          userId: userId,
          message: content,
          messageType: "TEXT",
          attachments: [],
        });
      } catch (err) {
        console.error("Failed to send message (real user chat):", err);
        toast.error("Error", {
          description: "Failed to send message.",
          position: "top-center",
        });
      }
    },
    [
      activeChatId,
      questionnaireId,
      startNewChat,
      sendMessage,
      userId,
      getChatType,
      isHealthTab,
    ]
  );

  const displayedMessages = messages;

  const observerTargetProp = setObserverTarget;

  const handleDeleteChat = useCallback(
    async (idToDelete: string) => {
      try {
        await deleteChat(idToDelete);
        toast.success("Chat Deleted", { position: "top-center" });
      } catch (err) {
        console.error("Failed to delete chat:", err);
        toast.error("Error", {
          description: "Failed to delete chat.",
          position: "top-center",
        });
      }
    },
    [deleteChat]
  );

  useEffect(() => {
    if (initialChatId && userId && !isLoadingChats && !chatId) {
      console.log(`Loading initial chat: ${initialChatId}`);
      switchToChat(initialChatId).catch((err) => {
        console.error("Failed to load initial chat:", err);
        toast.error("Error", {
          description: "Failed to load the requested chat.",
          position: "top-center",
        });
      });
    }
  }, [
    initialChatId,
    userId,
    isLoadingChats,
    chatId,
    switchToChat,
    currentUser,
  ]);

  const isInitialLoading =
    isLoadingUser || (isLoadingChatsList && userChats.length === 0);

  // Check if the chat exists, if not redirect to /chats
  useEffect(() => {
    if (
      !isLoadingChats &&
      !isLoadingChatsList &&
      userChats.length > 0 &&
      activeChatId &&
      !userChats.find((chat) => chat.id === activeChatId)
    ) {
      console.log("Chat not found, redirecting to /chats");
      toast.error("Chat not found", {
        description: "The requested chat does not exist or was deleted.",
        position: "top-center",
      });
      router.push("/chats");
    }
  }, [isLoadingChats, isLoadingChatsList, userChats, activeChatId, router]);

  //  Handle initial chat ID validation
  // useEffect(() => {
  //   if (
  //     initialChatId &&
  //     userId &&
  //     !isLoadingChats &&
  //     !isLoadingChatsList &&
  //     userChats.length > 0 &&
  //     !userChats.find((chat) => chat.id === initialChatId)
  //   ) {
  //     console.log(
  //       `Initial chat ID ${initialChatId} not found, redirecting to /chats`
  //     );
  //     toast.error("Chat not found", {
  //       description: "The requested chat does not exist or was deleted.",
  //       position: "top-center",
  //     });
  //     router.push("/chats");
  //   }
  // }, [
  //   initialChatId,
  //   userId,
  //   isLoadingChats,
  //   isLoadingChatsList,
  //   userChats,
  //   router,
  // ]);

  return (
    <div className="h-full w-full flex flex-col md:flex-row">
      <main className="flex-1 flex flex-col max-sm:pt-0 lg:px-24 px-6 overflow-hidden">
        <ChatWindow
          messages={displayedMessages}
          isLoadingMessages={isLoadingMessages}
          isSending={isSending}
          isStreaming={isStreaming}
          isLoadingMore={isLoadingMoreMessages}
          hasMore={hasMoreMessages}
          setObserverTarget={observerTargetProp}
          onSendMessage={handleSendMessage}
          chatId={activeChatId}
          newMessageAdded={newMessageAdded}
          setNewMessageAdded={setNewMessageAdded}
          headerAction={headerAction}
          currentUserId={userId}
          isInitialLoading={isInitialLoading}
        />
      </main>

      {showSidebar && (
        <Sidebar
          chats={userChats}
          currentChatId={chatId}
          onChatSelect={handleChatSelect}
          onNewChat={handleNewChat}
          onDeleteChat={handleDeleteChat}
          onLoadMore={loadMoreChats}
          isLoadingMore={isLoadingMoreChats}
          hasMoreChats={hasMoreChats}
          isLoading={isLoadingChatsList}
        />
      )}
    </div>
  );
}
