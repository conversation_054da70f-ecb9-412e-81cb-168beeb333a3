import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { fetchAuthSession } from "@aws-amplify/auth";
import { Schema } from "@/amplify/data/resource";
import { generateClient } from "aws-amplify/api";

export function usePostCreation() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(null);
  const [postTitle, setPostTitle] = useState("");
  const [postContent, setPostContent] = useState("");
  const [postImages, setPostImages] = useState<string[]>([]);
  const [imageLoading, setImageLoading] = useState(false);
  const client = generateClient<Schema>();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email);
          setCurrentUser({ email });
        }
      } catch (error) {
        router.push("/posts");
      }
    };
    fetchUser();
  }, [router]);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImageLoading(true);
      const fileArray = Array.from(e.target.files);
      const imagePromises = fileArray.map(file => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            if (e.target && typeof e.target.result === 'string') {
              resolve(e.target.result);
            }
          };
          reader.readAsDataURL(file);
        });
      });

      try {
        const newImages = await Promise.all(imagePromises);
        setPostImages(prev => [...prev, ...newImages]);
      } catch (error) {
        console.error("Error processing images:", error);
        alert("Error uploading images. Please try again.");
      } finally {
        setImageLoading(false);
      }
    }
  };

  const removeImage = (index: number) => {
    setPostImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleCreatePost = async () => {
    try {
      if (!currentUser || !postTitle.trim() || !postContent.trim()) {
        alert('Please fill in both title and content');
        return;
      }

      const newPost = {
        id: crypto.randomUUID(),
        userId: currentUser.email,
        userName: currentUser.email.split('@')[0],
        title: postTitle,
        content: postContent,
        images: postImages,
        likes: 0,
        commentsCount: 0,
        createdAt: new Date().toISOString(),
      };
      await client.models.Post.create(newPost);
      router.push("/posts");
    } catch (error) {
      console.error("Error creating post:", error);
      alert('Failed to create post. Please try again.');
    }
  };

  const isFormValid = Boolean(postTitle.trim() && postContent.trim());

  return {
    currentUser,
    postTitle,
    setPostTitle,
    postContent,
    setPostContent,
    postImages,
    imageLoading,
    handleImageChange,
    removeImage,
    handleCreatePost,
    isFormValid
  };
}
