'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import CustomButton from '@/components/ui/CustomButton';

interface AddSymptomModalProps {
  onClose: () => void;
  onSave?: (symptomData: any) => void;
}

const AddSymptomModal = ({ onClose, onSave }: AddSymptomModalProps) => {
  const [symptom, setSymptom] = useState('');
  const [date, setDate] = useState('');
  
  const handleSave = () => {
    if (onSave) {
      onSave({ symptom, date });
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[1001]">
      <div className="bg-white rounded-2xl w-[520px] h-[490px] overflow-y-auto flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 ">
          <h2 className="text-[16px] font-medium">Add Symptom</h2>
          <button onClick={onClose}>
            <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 px-8 pb-6 flex flex-col">
          {/* Symptom Field */}
          <div className="mb-5">
            <label className="block mb-2 font-medium">Symptom</label>
            <textarea
              value={symptom}
              onChange={(e) => setSymptom(e.target.value)}
              placeholder="e.g., 500 mmg"
              className="w-full px-4 py-2  bg-[#F5F5F5] border-none rounded-xl h-[151px] resize-none focus:outline-none"
            />
          </div>

          {/* Date Field */}
          <div className="mb-8">
            <label className="block mb-2 font-medium">Date</label>
            <div className="relative">
              <Input
                type="text"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                placeholder="MM / DD / YYYY"
                className="w-full bg-[#F5F5F5] border-none p-4 pr-10 "
                style={{ height: '51px' }}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Image
                  src="/health/calender.svg"
                  alt="Calendar"
                  width={24}
                  height={24}
                  className="cursor-pointer"
                />
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="mt-auto">
            <CustomButton onClick={handleSave}>
              Save
            </CustomButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddSymptomModal;
