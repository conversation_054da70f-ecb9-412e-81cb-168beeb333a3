const axios = require('axios');

// 1upHealth API 测试
const CLIENT_ID = 'c6c7d70321bc944210256eaaf14e29fd';
const CLIENT_SECRET = 'YOUR_CLIENT_SECRET_HERE'; // 需要您提供
const BASE_URL = 'https://api.1up.health';
const TEST_USER_ID = 'test123';

async function test1upHealthAPI() {
  console.log('🔍 Testing 1upHealth API integration...');
  console.log('Client ID:', CLIENT_ID);
  console.log('Base URL:', BASE_URL);
  
  try {
    // Step 1: Create User
    console.log('\n📝 Step 1: Creating user...');
    const createUserResponse = await axios.post(
      `${BASE_URL}/user-management/v1/user?app_user_id=${TEST_USER_ID}`,
      {},
      {
        headers: {
          'client_id': CLIENT_ID,
          'client_secret': CLIENT_SECRET,
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('✅ User created successfully:', createUserResponse.data);
    
    // Step 2: Create Auth Code
    console.log('\n🔐 Step 2: Creating auth code...');
    const authCodeResponse = await axios.post(
      `${BASE_URL}/user-management/v1/user/auth-code?app_user_id=${TEST_USER_ID}&client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}`,
      {}
    );
    
    console.log('✅ Auth code created:', authCodeResponse.data);
    const authCode = authCodeResponse.data.code;
    
    // Step 3: Create Access Token
    console.log('\n🎫 Step 3: Creating access token...');
    const tokenResponse = await axios.post(
      'https://auth.1up.health/oauth2/token',
      new URLSearchParams({
        'grant_type': 'authorization_code',
        'code': authCode,
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );
    
    console.log('✅ Access token created:', tokenResponse.data);
    const accessToken = tokenResponse.data.access_token;
    
    // Step 4: Test Health Systems API
    console.log('\n🏥 Step 4: Testing health systems API...');
    const healthSystemsResponse = await axios.get(
      `${BASE_URL}/connect/system/clinical`,
      {
        params: {
          client_id: CLIENT_ID,
          client_secret: CLIENT_SECRET
        }
      }
    );
    
    console.log('✅ Health systems retrieved:', healthSystemsResponse.data.length, 'systems found');
    
    // Step 5: Test Patient Data (if available)
    console.log('\n👤 Step 5: Testing patient data access...');
    try {
      const patientResponse = await axios.get(
        `${BASE_URL}/r4/Patient/`,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );
      
      console.log('✅ Patient data accessible:', patientResponse.data);
    } catch (patientError) {
      console.log('ℹ️ Patient data not available (expected for test user):', patientError.response?.status);
    }
    
    console.log('\n🎉 1upHealth API test completed successfully!');
    return true;
    
  } catch (error) {
    console.error('\n❌ 1upHealth API Error:');
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);
    
    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    
    return false;
  }
}

// 检查配置
function checkConfiguration() {
  console.log('🔧 Checking 1upHealth configuration...');
  
  const issues = [];
  
  if (!CLIENT_ID || CLIENT_ID === 'YOUR_CLIENT_ID_HERE') {
    issues.push('❌ CLIENT_ID not configured');
  } else {
    console.log('✅ CLIENT_ID configured');
  }
  
  if (!CLIENT_SECRET || CLIENT_SECRET === 'YOUR_CLIENT_SECRET_HERE') {
    issues.push('❌ CLIENT_SECRET not configured');
  } else {
    console.log('✅ CLIENT_SECRET configured');
  }
  
  if (issues.length > 0) {
    console.log('\n🚨 Configuration issues found:');
    issues.forEach(issue => console.log(issue));
    console.log('\nPlease update the CLIENT_SECRET in this file and try again.');
    return false;
  }
  
  return true;
}

// 运行测试
if (checkConfiguration()) {
  test1upHealthAPI().then((success) => {
    if (success) {
      console.log('\n✨ All tests passed!');
    } else {
      console.log('\n💥 Tests failed. Please check the error details above.');
    }
    process.exit(success ? 0 : 1);
  }).catch((error) => {
    console.error('\n🚨 Unexpected error:', error);
    process.exit(1);
  });
} else {
  process.exit(1);
}
