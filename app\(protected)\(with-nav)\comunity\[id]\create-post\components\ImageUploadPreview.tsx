import { X } from "lucide-react";

interface ImageUploadPreviewProps {
  postImages: string[];
  removeImage: (index: number) => void;
}

export default function ImageUploadPreview({ postImages, removeImage }: ImageUploadPreviewProps) {
  if (postImages.length === 0) return null;
  
  return (
    <div className="mb-4">
      <h3 className="text-sm font-medium text-gray-700 mb-2">Selected Images</h3>
      <div className="grid grid-cols-3 gap-2">
        {postImages.map((image, index) => (
          <div key={index} className="relative h-24 bg-gray-100 rounded-md overflow-hidden">
            <img
              src={image}
              alt={`Preview ${index}`}
              className="object-cover w-full h-full"
            />
            <button
              type="button"
              onClick={() => removeImage(index)}
              className="absolute top-1 right-1 bg-black bg-opacity-50 rounded-full p-1"
            >
              <X size={16} className="text-white" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
