import { MedicalRecord } from "@/lib/services/oneUpHealthService";

export const defaultUserInfo = {
  birthYear: "-",
  gender: "-",
  region: "-",
  race: "-",
  ethnicity: "-",
};

// Helper function to display value or dash
export const displayValue = (value?: string): string => {
  return value && value !== "-" ? value : "-";
};

// Function to determine the mapped type from a medical record
export const getMappedRecordType = (record: MedicalRecord): string => {
  let resourceType = null;

  if (
    record.rawData &&
    typeof record.rawData === "object" &&
    "resourceType" in record.rawData
  ) {
    resourceType = record.rawData.resourceType;
  }

  // Map FHIR resource types to our internal types for better compatibility
  const mappedType = resourceType
    ? resourceType === "Condition"
      ? "Symptom"
      : resourceType === "AllergyIntolerance"
        ? "Medication Reaction"
        : resourceType === "MedicationRequest" ||
            resourceType === "Medication"
          ? "Medication"
          : resourceType === "Observation" &&
              record.rawData &&
              typeof record.rawData === 'object' &&
              'category' in record.rawData &&
              Array.isArray(record.rawData.category) &&
              record.rawData.category[0] &&
              'coding' in record.rawData.category[0] &&
              Array.isArray(record.rawData.category[0].coding) &&
              record.rawData.category[0].coding[0] &&
              'code' in record.rawData.category[0].coding[0] &&
              record.rawData.category[0].coding[0].code === "vital-signs"
            ? "Vital Signs"
            : resourceType === "Encounter" || resourceType === "Observation"
              ? "Outpatient Visit"
              : record.type
    : record.type;

  return mappedType;
};

export const getRelatedResources = (record: MedicalRecord): any[] => {
  let relatedResources = [];

  // Check if there's a bundle with related entries
  if (
    record.rawData &&
    typeof record.rawData === "object" &&
    "entry" in record.rawData &&
    Array.isArray(record.rawData.entry)
  ) {
    relatedResources = record.rawData.entry
      .filter(
        (entry) => entry && typeof entry === "object" && "resource" in entry
      )
      .map((entry) => entry.resource);
  }
  // Or check if there's an explicit relatedResources array
  else if (
    record.relatedResources &&
    Array.isArray(record.relatedResources)
  ) {
    relatedResources = record.relatedResources;
  }

  return relatedResources;
};

export const formatDate = (dateString: string): string => {
  if (dateString.includes("T") && dateString.includes("Z")) {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }

  return dateString;
};