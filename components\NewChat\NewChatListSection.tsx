import NewChatListItem from "./NewChatListItem";

interface User {
  userId: string; // Changed from id to userId
  name: string;
  email?: string;
}

interface Props {
  letter: string;
  users: User[];
  selectedUserIds: string[];
  onUserSelect: (userId: string) => void;
}

const NewChatListSection = ({
  letter,
  users,
  selectedUserIds,
  onUserSelect,
}: Props) => (
  <div>
    {/* <div className="px-4 py-1 text-xs font-semibold text-blue-500 sticky top-0 z-10">
      {letter}
    </div> */}
    <div className="flex flex-col">
      {users.map((user) => (
        <NewChatListItem
          key={user.userId}
          userId={user.userId}
          name={user.email || user.name}
          selected={selectedUserIds.includes(user.userId)}
          onSelect={onUserSelect}
        />
      ))}
    </div>
  </div>
);

export default NewChatListSection;
