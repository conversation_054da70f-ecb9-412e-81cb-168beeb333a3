"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { ChatItemProps } from "./types";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";

const ChatItem = ({
  avatarSrc,
  displayName,
  message,
  chatId,
  time,
  unreadCount,
  chatType,
}: ChatItemProps) => {
  const pathname = usePathname();
  const isSelected = pathname === `/chats/${chatId}`;
  const isDoctorChat = displayName.startsWith("Doctor") && chatType !== "AI";

  // Define avatar background based on chat type
  const getAvatarBackground = () => {
    if (isDoctorChat) {
      return "bg-white text-gray-900 border border-gray-200";
    } else if (chatType === "AI") {
      return "bg-gradient-to-b from-blue-400 to-purple-400";
    } else if (chatType === "GROUP") {
      return "bg-gray-200";
    } else {
      return "bg-gradient-to-b from-green-300 to-yellow-200";
    }
  };

  return (
    <Link
      href={`/chats/${chatId}`}
      className={cn(
        "flex items-center gap-4 py-3.5 px-5 cursor-pointer w-full text-left rounded-[24px] transition-colors",
        isSelected ? "bg-[#2D7DEE]/10" : isDoctorChat
      )}
    >
      <div className="relative">
        <Avatar className="h-[60px] w-[60px] rounded-full">
          <AvatarImage
            src={chatType === "DIRECT" ? "/Avatar.png" : avatarSrc}
            alt={displayName}
            className="rounded-full object-cover"
            width={60}
            height={60}
          />
          <AvatarFallback
            className={cn(
              "text-white font-semibold text-lg",
              getAvatarBackground(),
              "rounded-full h-[60px] w-[60px] flex items-center justify-center"
            )}
          >
            {displayName?.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        {/* {unreadCount && unreadCount > 0 && (
          <div className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full">
            {unreadCount}
          </div>
        )} */}
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-center mb-1">
          <h3
            className={cn("font-medium text-base truncate", "text-[#171717]")}
          >
            {displayName}
          </h3>
          <span className="text-[#737373] text-sm">{time}</span>
        </div>
        <p className="text-[#929292] text-sm truncate font-normal">{message}</p>
      </div>
    </Link>
  );
};

export default ChatItem;
