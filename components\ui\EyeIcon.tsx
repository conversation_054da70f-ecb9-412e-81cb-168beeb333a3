interface EyeIconProps {
    onClick: () => void;
    isPasswordVisible: boolean;
  }
  
  const EyeIcon: React.FC<EyeIconProps> = ({ onClick, isPasswordVisible }) => {
    return (
      <button onClick={onClick} type="button" className="text-gray-500">
        {isPasswordVisible ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 4.5c5 0 9 4.5 9 7.5s-4 7.5-9 7.5-9-4.5-9-7.5 4-7.5 9-7.5z"
            />
            <circle cx="12" cy="12" r="3" strokeWidth="2" />
          </svg>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M1 1l22 22M12 4.5c5 0 9 4.5 9 7.5s-4 7.5-9 7.5-9-4.5-9-7.5 4-7.5 9-7.5z"
            />
          </svg>
        )}
      </button>
    );
  };
  
  export default EyeIcon;