import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { useCallback, useState, useEffect } from "react";
import Image from "next/image";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ChatParticipant } from "@/types/chat";
import { useIsMobile } from "@/hooks/use-mobile";
import PageHeader from "@/components/ui/PageHeader";
import { ContactManagementButtons } from "../common/ContactManagementButtons";
import { fetchAuthSession } from "aws-amplify/auth";
import {
  getFriendRequestStatus,
  sendFriendRequest,
  deleteContact,
} from "@/services/contactService";

interface ChatDropDownProps {
  children?: React.ReactNode;
  userData?: ChatParticipant | null;
  chatId?: string;
}

export function ChatDropDown({
  children,
  userData,
  chatId,
}: ChatDropDownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [friendRequestStatus, setFriendRequestStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const getCurrentUserId = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.sub) {
          const userId = String(session.tokens.idToken.payload.sub);
          setCurrentUserId(userId);
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    getCurrentUserId();
  }, []);

  useEffect(() => {
    const fetchFriendStatus = async () => {
      if (currentUserId && userData?.userId) {
        try {
          const status = await getFriendRequestStatus(
            currentUserId,
            userData.userId
          );
          setFriendRequestStatus(status);
        } catch (error) {
          console.error("Error fetching friend status:", error);
        }
      }
    };

    fetchFriendStatus();
  }, [currentUserId, userData?.userId, isOpen]);

  const handleDeleteContact = useCallback(async () => {
    if (userData?.userId && currentUserId) {
      setIsLoading(true);
      try {
        const success = await deleteContact(currentUserId, userData.userId);
        if (success) {
          setFriendRequestStatus(null);
        }
      } catch (error) {
        console.error("Error deleting contact:", error);
      } finally {
        setIsLoading(false);
        setIsOpen(false);
      }
    }
  }, [userData?.userId, currentUserId]);

  const handleSendFriendRequest = useCallback(async () => {
    if (userData?.userId && currentUserId) {
      setIsLoading(true);
      try {
        const result = await sendFriendRequest(currentUserId, userData.userId);
        if (result.success && result.requestId) {
          setFriendRequestStatus({
            type: "sent",
            status: "PENDING",
            requestId: result.requestId,
          });
        }
      } catch (error) {
        console.error("Error sending friend request:", error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [userData?.userId, currentUserId]);

  const handleStatusChange = useCallback(() => {
    // Force refresh when status changes
    setRefreshKey((prev) => prev + 1);
  }, []);

  const ContentComponent = () => (
    <div className="p-4 space-y-3 relative">
      <div className="flex items-center gap-3">
        <Avatar className="h-[77px] w-[77px]">
          {userData?.user?.profilePicture ? (
            <AvatarImage
              src={userData.user.profilePicture}
              alt={userData.user.email || "User avatar"}
            />
          ) : (
            <AvatarFallback>
              {userData?.user?.email?.charAt(0) || "U"}
            </AvatarFallback>
          )}
        </Avatar>
        <div className="flex flex-col">
          <h2 className="font-medium text-[16px] text-[#171717]">
            {userData?.user?.email || "User Email"}
          </h2>
          <p className="font-medium text-[14px] text-[#929292]"></p>
        </div>
      </div>

      <div className="mt-4">
        {userData && (
          <ContactManagementButtons
            key={`contact-buttons-${userData.userId}-${refreshKey}`}
            targetUserId={userData.userId}
            onStatusChange={handleStatusChange}
          />
        )}
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <>
        {children ? (
          <div onClick={() => setIsOpen(true)}>{children}</div>
        ) : (
          <Image
            alt="chat details"
            src="/chats/chat-details.svg"
            width={24}
            height={24}
            className="cursor-pointer"
            onClick={() => setIsOpen(true)}
          />
        )}

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent
            className="p-0 max-w-full sm:max-w-md h-screen sm:h-auto flex flex-col rounded-none sm:rounded-xl bg-white"
            showCloseButton={false}
          >
            <DialogHeader>
              <PageHeader
                title={
                  <span className="text-[16px] font-semibold font-figtree">
                    Contact Info
                  </span>
                }
                leftContent={
                  <Button
                    variant="ghost"
                    className="p-2 mr-2"
                    onClick={() => setIsOpen(false)}
                  >
                    <Image
                      alt="back"
                      src="/arrow-left.svg"
                      width={24}
                      height={24}
                    />
                  </Button>
                }
                background="white"
                className="border-b border-gray-200"
                sticky={true}
              />
            </DialogHeader>

            <ContentComponent />
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {children ? (
          children
        ) : (
          <Image
            alt="chat details"
            src="/chats/chat-details.svg"
            width={24}
            height={24}
            className="cursor-pointer"
          />
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[320px] p-0 rounded-2xl shadow-lg border-none mt-2 relative">
        {/* Triangle pointer at top */}
        <div className="absolute -top-2 right-4 w-4 h-4 bg-white rotate-45"></div>
        <ContentComponent />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
