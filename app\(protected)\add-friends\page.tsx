"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useWindowSize } from "@/hooks/useWindowSize";
import dynamic from "next/dynamic";
import { useUser } from "@/hooks/useUsers";
import { useFriendRequests } from "@/hooks/useFriendRequests";

import UserProfile from "@/components/friends/UserProfile";
import UserSearch from "@/components/friends/UserSearch";
import UserList from "@/components/friends/UserList";
import ContactOptions from "@/components/friends/ContactOptions";

const ContactsPageContent = dynamic(
  () => import("@/components/contacts/ContactsPageContent").then((mod) => mod.default),
  { ssr: false }
);

export default function AddFriendsPage() {
  const router = useRouter();
  const [searchValue, setSearchValue] = useState("");
  const [filteredUsers, setFilteredUsers] = useState<any[]>([]);
  const [showContactsModal, setShowContactsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any | null>(null);

  const isDesktop = useWindowSize();
  const { currentUser } = useUser();
  
  const { 
    users, 
    loading, 
    pendingRequestCount, 
    sendFriendRequest, 
    respondToFriendRequest 
  } = useFriendRequests(currentUser);

  useEffect(() => {
    if (searchValue.trim() === "") {
      setFilteredUsers([]);
      return;
    }

    const filtered = users.filter((user) =>
      user && user.email && user.email.toLowerCase().includes(searchValue.toLowerCase())
    ).map(user => {
      const modifiedUser = {...user};
      modifiedUser.originalEmail = user.email;
      modifiedUser.email = (user.email);
      return modifiedUser;
    });
    
    setFilteredUsers(filtered);
  }, [searchValue, users]);

  const handleContactsClick = () => {
    if (isDesktop) {
      setShowContactsModal(true);
    } else {
      router.push('/contacts');
    }
  };

  const handleSearch = () => {
   
    const filtered = users.filter((user) =>
      user.email.toLowerCase().includes(searchValue.toLowerCase())
    ).map(user => {
      const modifiedUser = {...user};
      modifiedUser.originalEmail = user.email;
      modifiedUser.email = (user.email);
      return modifiedUser;
    });
    setFilteredUsers(filtered);
  };

  const handleUserClick = (user: any) => {
    setSelectedUser(user);
  };

  const closeUserProfile = () => {
    setSelectedUser(null);
  };

  return (
    <>
      {showContactsModal && isDesktop ? (
        <ContactsPageContent isModal={true} onClose={() => setShowContactsModal(false)} />
      ) : (
        <div className="relative h-screen ">
          {selectedUser && (
            <div 
              className="absolute inset-0 z-10"
              style={{ backgroundColor: "#E9F0FF" }}
            >
              <UserProfile 
                user={selectedUser} 
                onClose={closeUserProfile}
                onSendFriendRequest={sendFriendRequest}
              />
            </div>
          )}
          
          <div 
            className={`absolute inset-0 flex flex-col bg-white ${
              selectedUser ? 'hidden' : 'block'
            }`}
          >
            <div className="p-4 flex items-center relative">
              <Image
                src="/xrestuk.svg" 
                alt="Close"
                height={24}
                width={24}
                className="absolute top-4 right-4 z-10 cursor-pointer hover:opacity-80 transition-opacity"
                onClick={() => router.back()}
              />
              <h1 className="text-xl font-semibold flex-1 text-center pr-8">Add Friends</h1>
            </div>

            <div className="bg-white py-2 m-[6px] rounded-3xl flex-grow flex flex-col">
              <UserSearch
                searchValue={searchValue}
                setSearchValue={setSearchValue}
                onSearch={handleSearch}
              />
              
              <UserList
                users={filteredUsers}
                onUserClick={handleUserClick}
                onSendFriendRequest={sendFriendRequest}
                onRespondToFriendRequest={respondToFriendRequest}
              />
              
              {!searchValue && (
                <ContactOptions
                  pendingRequestCount={pendingRequestCount}
                  onClick={handleContactsClick}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
