"use client";

import React from "react";
import { motion } from "framer-motion";
import { useMobile } from "@/hooks/use-mobile";
import { chatPanelVariants } from "../animations/LayoutAnimations";

interface MainContentAreaProps {
  children: React.ReactNode;
  headerContent?: React.ReactNode;
}

export const MainContentArea = React.memo<MainContentAreaProps>(
  ({ children, headerContent }) => {
    const isMobile = useMobile();

    const headerElement = React.useMemo(() => {
      if (isMobile || !headerContent) return null;
      return <div className="relative z-10">{headerContent}</div>;
    }, [isMobile, headerContent]);

    return (
      <motion.div
        className="flex flex-col h-full w-full overflow-hidden bg-white rounded-t-3xl md:rounded-none md:rounded-tr-3xl md:rounded-br-3xl"
        variants={chatPanelVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
      >
        {headerElement}
        <div className="flex-grow overflow-hidden relative bg-white">
          {children}
        </div>
      </motion.div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.children === nextProps.children &&
      prevProps.headerContent === nextProps.headerContent
    );
  }
);

MainContentArea.displayName = "MainContentArea";
