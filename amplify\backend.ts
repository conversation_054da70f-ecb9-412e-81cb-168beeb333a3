import { defineBackend } from "@aws-amplify/backend";

import { auth } from "./auth/resource";
import { data } from "./data/resource";
import { requestOrchestrator } from "./request-orchestrator/resource";
import { oneupHealthConnect } from "./functions/oneup-health-connect/resource";

import {
  CfnApi,
  CfnChannelNamespace,
  AuthorizationType,
} from "aws-cdk-lib/aws-appsync";
import { Policy, PolicyStatement } from "aws-cdk-lib/aws-iam";

const backend = defineBackend({
  auth,
  data,
  requestOrchestrator,
  oneupHealthConnect,
});

const customResources = backend.createStack("custom-resources");

const cfnEventAPI = new CfnApi(customResources, "CfnEventAPI", {
  name: "my-event-api",
  eventConfig: {
    authProviders: [
      {
        authType: AuthorizationType.USER_POOL,
        cognitoConfig: {
          awsRegion: customResources.region,

          userPoolId: backend.auth.resources.userPool.userPoolId,
        },
      },
      {
        authType: AuthorizationType.IAM,
      },
    ],

    connectionAuthModes: [{ authType: AuthorizationType.USER_POOL }],

    defaultPublishAuthModes: [
      { authType: AuthorizationType.USER_POOL },
      { authType: AuthorizationType.IAM },
    ],
    defaultSubscribeAuthModes: [{ authType: AuthorizationType.USER_POOL }],
  },
});

const namespace = new CfnChannelNamespace(
  customResources,
  "CfnEventAPINamespace",
  {
    apiId: cfnEventAPI.attrApiId,
    name: "default",
  }
);

backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(
  new Policy(customResources, "AppSyncEventPolicy", {
    statements: [
      new PolicyStatement({
        actions: [
          "appsync:EventConnect",
          "appsync:EventSubscribe",
          "appsync:EventPublish",
        ],
        resources: [`${cfnEventAPI.attrApiArn}/*`, `${cfnEventAPI.attrApiArn}`],
      }),
    ],
  })
);

backend.requestOrchestrator.resources.lambda.addToRolePolicy(
  new PolicyStatement({
    actions: [
      "appsync:EventConnect",
      "appsync:EventSubscribe",
      "appsync:EventPublish",
    ],
    resources: [`${cfnEventAPI.attrApiArn}/*`, `${cfnEventAPI.attrApiArn}`],
  })
);

backend.requestOrchestrator.addEnvironment(
  "EVENT_API_ENDPOINT",
  cfnEventAPI.getAtt("Dns.Http").toString()
);
backend.requestOrchestrator.addEnvironment(
  "EVENT_API_REGION",
  customResources.region
);

backend.addOutput({
  custom: {
    events: {
      url: `https://${cfnEventAPI.getAtt("Dns.Http").toString()}/event`,
      aws_region: customResources.region,
      default_authorization_type: AuthorizationType.USER_POOL,
    },
  },
});

export { backend };
