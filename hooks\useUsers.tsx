"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from "react";
import { generateClient } from "aws-amplify/api";
import type { Schema } from "@/amplify/data/resource";
import { fetchUserAttributes, getCurrentUser, signOut } from "aws-amplify/auth";
import { Hub } from "aws-amplify/utils";

interface UserContextType {
  currentUser: {
    userId: string;
    email: string;
    name: string;
    is_1healthup_connected?: boolean;
  } | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  logout: () => Promise<void>;
}

const UserContext = createContext<UserContextType>({
  currentUser: null,
  loading: true,
  error: null,
  refetch: async () => {},
  logout: async () => {},
});

export function UserProvider({ children }: { children: ReactNode }) {
  const [currentUser, setCurrentUser] = useState<{
    userId: string;
    email: string;
    name: string;
    is_1healthup_connected?: boolean;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchUser = useCallback(async () => {
    setLoading(true);
    setError(null);

    setCurrentUser(null);

    try {
      const authUser = await getCurrentUser();
      const userAttributes = await fetchUserAttributes();
      const client = generateClient<Schema>();

      const userEmail = userAttributes.email
        ? String(userAttributes.email)
        : "";
      const userName = userAttributes.name ? String(userAttributes.name) : "";
      const userId = userAttributes.sub
        ? String(userAttributes.sub)
        : authUser.userId;

      if (!userId) {
        setCurrentUser(null);
        return;
      }

      try {
        const { data: existingUser } = await client.models.User.get({
          userId: userId,
        });

        if (existingUser) {
          setCurrentUser({
            userId: existingUser.userId,
            email: existingUser.email || userEmail,
            name: existingUser.name || userName,
            is_1healthup_connected:
              existingUser.is_1healthup_connected || false,
          });
        } else if (userEmail) {
          setCurrentUser({
            userId,
            email: userEmail,
            name: userName || userEmail.split("@")[0],
            is_1healthup_connected: false,
          });

          try {
            const newUser = await client.models.User.create({
              userId: userId,
              email: userEmail,
              name: userName || userEmail.split("@")[0],
              userType: "HUMAN",
            });
            console.log("Created new user in database:", newUser);
          } catch (createError) {
            console.error("Error creating user in database:", createError);
          }
        }
      } catch (dbError) {
        console.error("Database error fetching user:", dbError);

        if (userId && userEmail) {
          setCurrentUser({
            userId,
            email: userEmail,
            name: userName || userEmail.split("@")[0],
            is_1healthup_connected: false,
          });
        }
      }
    } catch (err) {
      console.error("Error fetching current user:", err);
      setError(err instanceof Error ? err : new Error("Unknown error"));

      setCurrentUser(null);

      if (
        process.env.NODE_ENV === "development" &&
        process.env.NEXT_PUBLIC_USE_TEST_USER === "true"
      ) {
        setCurrentUser({
          userId: "development-user",
          email: "<EMAIL>",
          name: "Development User",
          is_1healthup_connected: true,
        });
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = async () => {
    try {
      setLoading(true);
      await signOut({ global: true });
      setCurrentUser(null);

      window.localStorage.removeItem("amplify-signin-with-hostedUI");

      window.location.href = "/";
    } catch (err) {
      console.error("Error signing out:", err);
      setError(err instanceof Error ? err : new Error("Failed to sign out"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUser();

    const hubListener = Hub.listen("auth", ({ payload }) => {
      const { event } = payload;
      console.log("Auth event received in useUsers:", event);

      if (event === "signedIn" || event === "signInWithRedirect") {
        console.log("User signed in, refetching user data...");
        fetchUser();
      } else if (event === "signedOut") {
        console.log("User signed out, clearing user data...");
        setCurrentUser(null);
        setLoading(false);
        setError(null);
      }
    });

    return () => {
      hubListener();
    };
  }, [fetchUser]);

  const value = {
    currentUser,
    loading,
    error,
    refetch: fetchUser,
    logout,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
}

export function useUser() {
  return useContext(UserContext);
}