import Image from 'next/image';
import * as Dialog from '@radix-ui/react-dialog';
import { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  date?: string;
  children: React.ReactNode;
}

export default function Modal({ isOpen, onClose, title, date, children }: ModalProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <AnimatePresence>
        {isOpen && (
          <Dialog.Portal forceMount>
            <Dialog.Overlay asChild>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40"
              />
            </Dialog.Overlay>

            <Dialog.Content asChild>
              <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
                <motion.div
                  initial={{ opacity: 0, scale: 0.95, y: 20 }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    y: 0,
                    transition: {
                      duration: 0.4,
                      ease: [0.16, 1, 0.3, 1], // Custom spring-like curve
                    },
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.95,
                    y: 10,
                    transition: {
                      duration: 0.25,
                      ease: 'easeInOut',
                    },
                  }}
                  className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-xl"
                >
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.15, duration: 0.3 }}
                    className="flex justify-between items-center p-6 border-b border-gray-100"
                  >
                    <div className="flex items-center">
                      <Dialog.Title className="text-xl font-medium">{title}</Dialog.Title>
                      {date && <span className="ml-2 text-gray-500 text-sm">{date}</span>}
                    </div>
                    <Dialog.Close asChild>
                      <button className="text-gray-500 hover:text-gray-700 transition-colors">
                        <Image
                          src="/xrestuk.svg"
                          alt="Close"
                          width={24}
                          height={24}
                          className="cursor-pointer hover:scale-110 transition-transform"
                          onError={(e) => {
                            // Fallback if SVG is not found
                            const target = e.target as HTMLElement;
                            target.innerText = '✕';
                          }}
                        />
                      </button>
                    </Dialog.Close>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.4 }}
                    className="p-6 overflow-y-auto max-h-[calc(90vh-100px)]"
                  >
                    <Dialog.Description asChild>{children}</Dialog.Description>
                  </motion.div>
                </motion.div>
              </div>
            </Dialog.Content>
          </Dialog.Portal>
        )}
      </AnimatePresence>
    </Dialog.Root>
  );
}
